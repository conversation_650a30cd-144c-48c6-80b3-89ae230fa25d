# Multi-stage build для оптимизации размера образа
FROM oven/bun:1-alpine AS base

# Устанавливаем рабочую директорию
WORKDIR /app

# Устанавливаем системные зависимости для Prisma
RUN apk add --no-cache openssl

# Копируем package.json и bun.lockb для кэширования зависимостей
COPY api/package.json api/bun.lockb* ./

# Устанавливаем зависимости
RUN bun install

# Копируем необходимые файлы для генерации
COPY api/prisma ./prisma
COPY api/schema.zmodel ./
COPY api/*.zmodel ./

# Генерируем Prisma клиент и ZenStack
RUN bunx prisma generate --schema=./prisma/schema.prisma
RUN bunx zenstack generate

# Копируем остальной исходный код
COPY api/ ./

# Production stage
FROM oven/bun:1-alpine AS production

WORKDIR /app

# Устанавливаем только runtime зависимости
RUN apk add --no-cache openssl curl

# Копируем node_modules и сгенерированные файлы
COPY --from=base /app/node_modules ./node_modules
COPY --from=base /app/package.json ./package.json

# Копируем исходный код
COPY --from=base /app/ ./

# Создаем директорию для загрузок и устанавливаем права
RUN mkdir -p uploads && chown -R bun:bun uploads

# Переключаемся на непривилегированного пользователя (bun уже существует)
USER bun

# Открываем порт
EXPOSE 3000

# Устанавливаем переменные окружения
ENV NODE_ENV=production

# Запускаем приложение
CMD ["bun", "index.ts"]
