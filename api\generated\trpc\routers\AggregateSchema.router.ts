/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { db } from ".";
import { createTRPCRouter } from "../../../trpc";
import { procedure } from "../../../trpc";
import * as _Schema from '@zenstackhq/runtime/zod/input';
const $Schema: typeof _Schema = (_Schema as any).default ?? _Schema;
import { checkRead, checkMutate } from '../helper';

export default function createRouter() {
    return createTRPCRouter({

        aggregate: procedure.input($Schema.AggregateSchemaInputSchema.aggregate).query(({ ctx, input }) => checkRead(db(ctx).aggregateSchema.aggregate(input as any))),

        createMany: procedure.input($Schema.AggregateSchemaInputSchema.createMany.optional()).mutation(async ({ ctx, input }) => checkMutate(db(ctx).aggregateSchema.createMany(input as any))),

        create: procedure.input($Schema.AggregateSchemaInputSchema.create).mutation(async ({ ctx, input }) => checkMutate(db(ctx).aggregateSchema.create(input as any))),

        deleteMany: procedure.input($Schema.AggregateSchemaInputSchema.deleteMany.optional()).mutation(async ({ ctx, input }) => checkMutate(db(ctx).aggregateSchema.deleteMany(input as any))),

        delete: procedure.input($Schema.AggregateSchemaInputSchema.delete).mutation(async ({ ctx, input }) => checkMutate(db(ctx).aggregateSchema.delete(input as any))),

        findFirst: procedure.input($Schema.AggregateSchemaInputSchema.findFirst.optional()).query(({ ctx, input }) => checkRead(db(ctx).aggregateSchema.findFirst(input as any))),

        findFirstOrThrow: procedure.input($Schema.AggregateSchemaInputSchema.findFirst.optional()).query(({ ctx, input }) => checkRead(db(ctx).aggregateSchema.findFirstOrThrow(input as any))),

        findMany: procedure.input($Schema.AggregateSchemaInputSchema.findMany.optional()).query(({ ctx, input }) => checkRead(db(ctx).aggregateSchema.findMany(input as any))),

        findUnique: procedure.input($Schema.AggregateSchemaInputSchema.findUnique).query(({ ctx, input }) => checkRead(db(ctx).aggregateSchema.findUnique(input as any))),

        findUniqueOrThrow: procedure.input($Schema.AggregateSchemaInputSchema.findUnique).query(({ ctx, input }) => checkRead(db(ctx).aggregateSchema.findUniqueOrThrow(input as any))),

        groupBy: procedure.input($Schema.AggregateSchemaInputSchema.groupBy).query(({ ctx, input }) => checkRead(db(ctx).aggregateSchema.groupBy(input as any))),

        updateMany: procedure.input($Schema.AggregateSchemaInputSchema.updateMany).mutation(async ({ ctx, input }) => checkMutate(db(ctx).aggregateSchema.updateMany(input as any))),

        update: procedure.input($Schema.AggregateSchemaInputSchema.update).mutation(async ({ ctx, input }) => checkMutate(db(ctx).aggregateSchema.update(input as any))),

        upsert: procedure.input($Schema.AggregateSchemaInputSchema.upsert).mutation(async ({ ctx, input }) => checkMutate(db(ctx).aggregateSchema.upsert(input as any))),

        count: procedure.input($Schema.AggregateSchemaInputSchema.count.optional()).query(({ ctx, input }) => checkRead(db(ctx).aggregateSchema.count(input as any))),

    }
    );
}
