/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { db } from ".";
import { createTRPCRouter } from "../../../trpc";
import { procedure } from "../../../trpc";
import * as _Schema from '@zenstackhq/runtime/zod/input';
const $Schema: typeof _Schema = (_Schema as any).default ?? _Schema;
import { checkRead, checkMutate } from '../helper';

export default function createRouter() {
    return createTRPCRouter({

        aggregate: procedure.input($Schema.AttributeGroupInputSchema.aggregate).query(({ ctx, input }) => checkRead(db(ctx).attributeGroup.aggregate(input as any))),

        createMany: procedure.input($Schema.AttributeGroupInputSchema.createMany.optional()).mutation(async ({ ctx, input }) => checkMutate(db(ctx).attributeGroup.createMany(input as any))),

        create: procedure.input($Schema.AttributeGroupInputSchema.create).mutation(async ({ ctx, input }) => checkMutate(db(ctx).attributeGroup.create(input as any))),

        deleteMany: procedure.input($Schema.AttributeGroupInputSchema.deleteMany.optional()).mutation(async ({ ctx, input }) => checkMutate(db(ctx).attributeGroup.deleteMany(input as any))),

        delete: procedure.input($Schema.AttributeGroupInputSchema.delete).mutation(async ({ ctx, input }) => checkMutate(db(ctx).attributeGroup.delete(input as any))),

        findFirst: procedure.input($Schema.AttributeGroupInputSchema.findFirst.optional()).query(({ ctx, input }) => checkRead(db(ctx).attributeGroup.findFirst(input as any))),

        findFirstOrThrow: procedure.input($Schema.AttributeGroupInputSchema.findFirst.optional()).query(({ ctx, input }) => checkRead(db(ctx).attributeGroup.findFirstOrThrow(input as any))),

        findMany: procedure.input($Schema.AttributeGroupInputSchema.findMany.optional()).query(({ ctx, input }) => checkRead(db(ctx).attributeGroup.findMany(input as any))),

        findUnique: procedure.input($Schema.AttributeGroupInputSchema.findUnique).query(({ ctx, input }) => checkRead(db(ctx).attributeGroup.findUnique(input as any))),

        findUniqueOrThrow: procedure.input($Schema.AttributeGroupInputSchema.findUnique).query(({ ctx, input }) => checkRead(db(ctx).attributeGroup.findUniqueOrThrow(input as any))),

        groupBy: procedure.input($Schema.AttributeGroupInputSchema.groupBy).query(({ ctx, input }) => checkRead(db(ctx).attributeGroup.groupBy(input as any))),

        updateMany: procedure.input($Schema.AttributeGroupInputSchema.updateMany).mutation(async ({ ctx, input }) => checkMutate(db(ctx).attributeGroup.updateMany(input as any))),

        update: procedure.input($Schema.AttributeGroupInputSchema.update).mutation(async ({ ctx, input }) => checkMutate(db(ctx).attributeGroup.update(input as any))),

        upsert: procedure.input($Schema.AttributeGroupInputSchema.upsert).mutation(async ({ ctx, input }) => checkMutate(db(ctx).attributeGroup.upsert(input as any))),

        count: procedure.input($Schema.AttributeGroupInputSchema.count.optional()).query(({ ctx, input }) => checkRead(db(ctx).attributeGroup.count(input as any))),

    }
    );
}
