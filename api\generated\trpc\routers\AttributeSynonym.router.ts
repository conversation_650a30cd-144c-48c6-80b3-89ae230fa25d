/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { db } from ".";
import { createTRPCRouter } from "../../../trpc";
import { procedure } from "../../../trpc";
import * as _Schema from '@zenstackhq/runtime/zod/input';
const $Schema: typeof _Schema = (_Schema as any).default ?? _Schema;
import { checkRead, checkMutate } from '../helper';

export default function createRouter() {
    return createTRPCRouter({

        aggregate: procedure.input($Schema.AttributeSynonymInputSchema.aggregate).query(({ ctx, input }) => checkRead(db(ctx).attributeSynonym.aggregate(input as any))),

        createMany: procedure.input($Schema.AttributeSynonymInputSchema.createMany.optional()).mutation(async ({ ctx, input }) => checkMutate(db(ctx).attributeSynonym.createMany(input as any))),

        create: procedure.input($Schema.AttributeSynonymInputSchema.create).mutation(async ({ ctx, input }) => checkMutate(db(ctx).attributeSynonym.create(input as any))),

        deleteMany: procedure.input($Schema.AttributeSynonymInputSchema.deleteMany.optional()).mutation(async ({ ctx, input }) => checkMutate(db(ctx).attributeSynonym.deleteMany(input as any))),

        delete: procedure.input($Schema.AttributeSynonymInputSchema.delete).mutation(async ({ ctx, input }) => checkMutate(db(ctx).attributeSynonym.delete(input as any))),

        findFirst: procedure.input($Schema.AttributeSynonymInputSchema.findFirst.optional()).query(({ ctx, input }) => checkRead(db(ctx).attributeSynonym.findFirst(input as any))),

        findFirstOrThrow: procedure.input($Schema.AttributeSynonymInputSchema.findFirst.optional()).query(({ ctx, input }) => checkRead(db(ctx).attributeSynonym.findFirstOrThrow(input as any))),

        findMany: procedure.input($Schema.AttributeSynonymInputSchema.findMany.optional()).query(({ ctx, input }) => checkRead(db(ctx).attributeSynonym.findMany(input as any))),

        findUnique: procedure.input($Schema.AttributeSynonymInputSchema.findUnique).query(({ ctx, input }) => checkRead(db(ctx).attributeSynonym.findUnique(input as any))),

        findUniqueOrThrow: procedure.input($Schema.AttributeSynonymInputSchema.findUnique).query(({ ctx, input }) => checkRead(db(ctx).attributeSynonym.findUniqueOrThrow(input as any))),

        groupBy: procedure.input($Schema.AttributeSynonymInputSchema.groupBy).query(({ ctx, input }) => checkRead(db(ctx).attributeSynonym.groupBy(input as any))),

        updateMany: procedure.input($Schema.AttributeSynonymInputSchema.updateMany).mutation(async ({ ctx, input }) => checkMutate(db(ctx).attributeSynonym.updateMany(input as any))),

        update: procedure.input($Schema.AttributeSynonymInputSchema.update).mutation(async ({ ctx, input }) => checkMutate(db(ctx).attributeSynonym.update(input as any))),

        upsert: procedure.input($Schema.AttributeSynonymInputSchema.upsert).mutation(async ({ ctx, input }) => checkMutate(db(ctx).attributeSynonym.upsert(input as any))),

        count: procedure.input($Schema.AttributeSynonymInputSchema.count.optional()).query(({ ctx, input }) => checkRead(db(ctx).attributeSynonym.count(input as any))),

    }
    );
}
