/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { db } from ".";
import { createTRPCRouter } from "../../../trpc";
import { procedure } from "../../../trpc";
import * as _Schema from '@zenstackhq/runtime/zod/input';
const $Schema: typeof _Schema = (_Schema as any).default ?? _Schema;
import { checkRead, checkMutate } from '../helper';

export default function createRouter() {
    return createTRPCRouter({

        aggregate: procedure.input($Schema.AttributeSynonymGroupInputSchema.aggregate).query(({ ctx, input }) => checkRead(db(ctx).attributeSynonymGroup.aggregate(input as any))),

        createMany: procedure.input($Schema.AttributeSynonymGroupInputSchema.createMany.optional()).mutation(async ({ ctx, input }) => checkMutate(db(ctx).attributeSynonymGroup.createMany(input as any))),

        create: procedure.input($Schema.AttributeSynonymGroupInputSchema.create).mutation(async ({ ctx, input }) => checkMutate(db(ctx).attributeSynonymGroup.create(input as any))),

        deleteMany: procedure.input($Schema.AttributeSynonymGroupInputSchema.deleteMany.optional()).mutation(async ({ ctx, input }) => checkMutate(db(ctx).attributeSynonymGroup.deleteMany(input as any))),

        delete: procedure.input($Schema.AttributeSynonymGroupInputSchema.delete).mutation(async ({ ctx, input }) => checkMutate(db(ctx).attributeSynonymGroup.delete(input as any))),

        findFirst: procedure.input($Schema.AttributeSynonymGroupInputSchema.findFirst.optional()).query(({ ctx, input }) => checkRead(db(ctx).attributeSynonymGroup.findFirst(input as any))),

        findFirstOrThrow: procedure.input($Schema.AttributeSynonymGroupInputSchema.findFirst.optional()).query(({ ctx, input }) => checkRead(db(ctx).attributeSynonymGroup.findFirstOrThrow(input as any))),

        findMany: procedure.input($Schema.AttributeSynonymGroupInputSchema.findMany.optional()).query(({ ctx, input }) => checkRead(db(ctx).attributeSynonymGroup.findMany(input as any))),

        findUnique: procedure.input($Schema.AttributeSynonymGroupInputSchema.findUnique).query(({ ctx, input }) => checkRead(db(ctx).attributeSynonymGroup.findUnique(input as any))),

        findUniqueOrThrow: procedure.input($Schema.AttributeSynonymGroupInputSchema.findUnique).query(({ ctx, input }) => checkRead(db(ctx).attributeSynonymGroup.findUniqueOrThrow(input as any))),

        groupBy: procedure.input($Schema.AttributeSynonymGroupInputSchema.groupBy).query(({ ctx, input }) => checkRead(db(ctx).attributeSynonymGroup.groupBy(input as any))),

        updateMany: procedure.input($Schema.AttributeSynonymGroupInputSchema.updateMany).mutation(async ({ ctx, input }) => checkMutate(db(ctx).attributeSynonymGroup.updateMany(input as any))),

        update: procedure.input($Schema.AttributeSynonymGroupInputSchema.update).mutation(async ({ ctx, input }) => checkMutate(db(ctx).attributeSynonymGroup.update(input as any))),

        upsert: procedure.input($Schema.AttributeSynonymGroupInputSchema.upsert).mutation(async ({ ctx, input }) => checkMutate(db(ctx).attributeSynonymGroup.upsert(input as any))),

        count: procedure.input($Schema.AttributeSynonymGroupInputSchema.count.optional()).query(({ ctx, input }) => checkRead(db(ctx).attributeSynonymGroup.count(input as any))),

    }
    );
}
