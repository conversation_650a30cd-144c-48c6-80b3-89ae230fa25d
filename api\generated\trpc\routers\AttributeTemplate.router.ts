/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { db } from ".";
import { createTRPCRouter } from "../../../trpc";
import { procedure } from "../../../trpc";
import * as _Schema from '@zenstackhq/runtime/zod/input';
const $Schema: typeof _Schema = (_Schema as any).default ?? _Schema;
import { checkRead, checkMutate } from '../helper';

export default function createRouter() {
    return createTRPCRouter({

        aggregate: procedure.input($Schema.AttributeTemplateInputSchema.aggregate).query(({ ctx, input }) => checkRead(db(ctx).attributeTemplate.aggregate(input as any))),

        createMany: procedure.input($Schema.AttributeTemplateInputSchema.createMany.optional()).mutation(async ({ ctx, input }) => checkMutate(db(ctx).attributeTemplate.createMany(input as any))),

        create: procedure.input($Schema.AttributeTemplateInputSchema.create).mutation(async ({ ctx, input }) => checkMutate(db(ctx).attributeTemplate.create(input as any))),

        deleteMany: procedure.input($Schema.AttributeTemplateInputSchema.deleteMany.optional()).mutation(async ({ ctx, input }) => checkMutate(db(ctx).attributeTemplate.deleteMany(input as any))),

        delete: procedure.input($Schema.AttributeTemplateInputSchema.delete).mutation(async ({ ctx, input }) => checkMutate(db(ctx).attributeTemplate.delete(input as any))),

        findFirst: procedure.input($Schema.AttributeTemplateInputSchema.findFirst.optional()).query(({ ctx, input }) => checkRead(db(ctx).attributeTemplate.findFirst(input as any))),

        findFirstOrThrow: procedure.input($Schema.AttributeTemplateInputSchema.findFirst.optional()).query(({ ctx, input }) => checkRead(db(ctx).attributeTemplate.findFirstOrThrow(input as any))),

        findMany: procedure.input($Schema.AttributeTemplateInputSchema.findMany.optional()).query(({ ctx, input }) => checkRead(db(ctx).attributeTemplate.findMany(input as any))),

        findUnique: procedure.input($Schema.AttributeTemplateInputSchema.findUnique).query(({ ctx, input }) => checkRead(db(ctx).attributeTemplate.findUnique(input as any))),

        findUniqueOrThrow: procedure.input($Schema.AttributeTemplateInputSchema.findUnique).query(({ ctx, input }) => checkRead(db(ctx).attributeTemplate.findUniqueOrThrow(input as any))),

        groupBy: procedure.input($Schema.AttributeTemplateInputSchema.groupBy).query(({ ctx, input }) => checkRead(db(ctx).attributeTemplate.groupBy(input as any))),

        updateMany: procedure.input($Schema.AttributeTemplateInputSchema.updateMany).mutation(async ({ ctx, input }) => checkMutate(db(ctx).attributeTemplate.updateMany(input as any))),

        update: procedure.input($Schema.AttributeTemplateInputSchema.update).mutation(async ({ ctx, input }) => checkMutate(db(ctx).attributeTemplate.update(input as any))),

        upsert: procedure.input($Schema.AttributeTemplateInputSchema.upsert).mutation(async ({ ctx, input }) => checkMutate(db(ctx).attributeTemplate.upsert(input as any))),

        count: procedure.input($Schema.AttributeTemplateInputSchema.count.optional()).query(({ ctx, input }) => checkRead(db(ctx).attributeTemplate.count(input as any))),

    }
    );
}
