/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { db } from ".";
import { createTRPCRouter } from "../../../trpc";
import { procedure } from "../../../trpc";
import * as _Schema from '@zenstackhq/runtime/zod/input';
const $Schema: typeof _Schema = (_Schema as any).default ?? _Schema;
import { checkRead, checkMutate } from '../helper';

export default function createRouter() {
    return createTRPCRouter({

        aggregate: procedure.input($Schema.BrandInputSchema.aggregate).query(({ ctx, input }) => checkRead(db(ctx).brand.aggregate(input as any))),

        createMany: procedure.input($Schema.BrandInputSchema.createMany.optional()).mutation(async ({ ctx, input }) => checkMutate(db(ctx).brand.createMany(input as any))),

        create: procedure.input($Schema.BrandInputSchema.create).mutation(async ({ ctx, input }) => checkMutate(db(ctx).brand.create(input as any))),

        deleteMany: procedure.input($Schema.BrandInputSchema.deleteMany.optional()).mutation(async ({ ctx, input }) => checkMutate(db(ctx).brand.deleteMany(input as any))),

        delete: procedure.input($Schema.BrandInputSchema.delete).mutation(async ({ ctx, input }) => checkMutate(db(ctx).brand.delete(input as any))),

        findFirst: procedure.input($Schema.BrandInputSchema.findFirst.optional()).query(({ ctx, input }) => checkRead(db(ctx).brand.findFirst(input as any))),

        findFirstOrThrow: procedure.input($Schema.BrandInputSchema.findFirst.optional()).query(({ ctx, input }) => checkRead(db(ctx).brand.findFirstOrThrow(input as any))),

        findMany: procedure.input($Schema.BrandInputSchema.findMany.optional()).query(({ ctx, input }) => checkRead(db(ctx).brand.findMany(input as any))),

        findUnique: procedure.input($Schema.BrandInputSchema.findUnique).query(({ ctx, input }) => checkRead(db(ctx).brand.findUnique(input as any))),

        findUniqueOrThrow: procedure.input($Schema.BrandInputSchema.findUnique).query(({ ctx, input }) => checkRead(db(ctx).brand.findUniqueOrThrow(input as any))),

        groupBy: procedure.input($Schema.BrandInputSchema.groupBy).query(({ ctx, input }) => checkRead(db(ctx).brand.groupBy(input as any))),

        updateMany: procedure.input($Schema.BrandInputSchema.updateMany).mutation(async ({ ctx, input }) => checkMutate(db(ctx).brand.updateMany(input as any))),

        update: procedure.input($Schema.BrandInputSchema.update).mutation(async ({ ctx, input }) => checkMutate(db(ctx).brand.update(input as any))),

        upsert: procedure.input($Schema.BrandInputSchema.upsert).mutation(async ({ ctx, input }) => checkMutate(db(ctx).brand.upsert(input as any))),

        count: procedure.input($Schema.BrandInputSchema.count.optional()).query(({ ctx, input }) => checkRead(db(ctx).brand.count(input as any))),

    }
    );
}
