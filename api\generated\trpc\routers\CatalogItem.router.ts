/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { db } from ".";
import { createTRPCRouter } from "../../../trpc";
import { procedure } from "../../../trpc";
import * as _Schema from '@zenstackhq/runtime/zod/input';
const $Schema: typeof _Schema = (_Schema as any).default ?? _Schema;
import { checkRead, checkMutate } from '../helper';

export default function createRouter() {
    return createTRPCRouter({

        aggregate: procedure.input($Schema.CatalogItemInputSchema.aggregate).query(({ ctx, input }) => checkRead(db(ctx).catalogItem.aggregate(input as any))),

        createMany: procedure.input($Schema.CatalogItemInputSchema.createMany.optional()).mutation(async ({ ctx, input }) => checkMutate(db(ctx).catalogItem.createMany(input as any))),

        create: procedure.input($Schema.CatalogItemInputSchema.create).mutation(async ({ ctx, input }) => checkMutate(db(ctx).catalogItem.create(input as any))),

        deleteMany: procedure.input($Schema.CatalogItemInputSchema.deleteMany.optional()).mutation(async ({ ctx, input }) => checkMutate(db(ctx).catalogItem.deleteMany(input as any))),

        delete: procedure.input($Schema.CatalogItemInputSchema.delete).mutation(async ({ ctx, input }) => checkMutate(db(ctx).catalogItem.delete(input as any))),

        findFirst: procedure.input($Schema.CatalogItemInputSchema.findFirst.optional()).query(({ ctx, input }) => checkRead(db(ctx).catalogItem.findFirst(input as any))),

        findFirstOrThrow: procedure.input($Schema.CatalogItemInputSchema.findFirst.optional()).query(({ ctx, input }) => checkRead(db(ctx).catalogItem.findFirstOrThrow(input as any))),

        findMany: procedure.input($Schema.CatalogItemInputSchema.findMany.optional()).query(({ ctx, input }) => checkRead(db(ctx).catalogItem.findMany(input as any))),

        findUnique: procedure.input($Schema.CatalogItemInputSchema.findUnique).query(({ ctx, input }) => checkRead(db(ctx).catalogItem.findUnique(input as any))),

        findUniqueOrThrow: procedure.input($Schema.CatalogItemInputSchema.findUnique).query(({ ctx, input }) => checkRead(db(ctx).catalogItem.findUniqueOrThrow(input as any))),

        groupBy: procedure.input($Schema.CatalogItemInputSchema.groupBy).query(({ ctx, input }) => checkRead(db(ctx).catalogItem.groupBy(input as any))),

        updateMany: procedure.input($Schema.CatalogItemInputSchema.updateMany).mutation(async ({ ctx, input }) => checkMutate(db(ctx).catalogItem.updateMany(input as any))),

        update: procedure.input($Schema.CatalogItemInputSchema.update).mutation(async ({ ctx, input }) => checkMutate(db(ctx).catalogItem.update(input as any))),

        upsert: procedure.input($Schema.CatalogItemInputSchema.upsert).mutation(async ({ ctx, input }) => checkMutate(db(ctx).catalogItem.upsert(input as any))),

        count: procedure.input($Schema.CatalogItemInputSchema.count.optional()).query(({ ctx, input }) => checkRead(db(ctx).catalogItem.count(input as any))),

    }
    );
}
