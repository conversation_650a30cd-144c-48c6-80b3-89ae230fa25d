/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { db } from ".";
import { createTRPCRouter } from "../../../trpc";
import { procedure } from "../../../trpc";
import * as _Schema from '@zenstackhq/runtime/zod/input';
const $Schema: typeof _Schema = (_Schema as any).default ?? _Schema;
import { checkRead, checkMutate } from '../helper';

export default function createRouter() {
    return createTRPCRouter({

        aggregate: procedure.input($Schema.CatalogItemAttributeInputSchema.aggregate).query(({ ctx, input }) => checkRead(db(ctx).catalogItemAttribute.aggregate(input as any))),

        createMany: procedure.input($Schema.CatalogItemAttributeInputSchema.createMany.optional()).mutation(async ({ ctx, input }) => checkMutate(db(ctx).catalogItemAttribute.createMany(input as any))),

        create: procedure.input($Schema.CatalogItemAttributeInputSchema.create).mutation(async ({ ctx, input }) => checkMutate(db(ctx).catalogItemAttribute.create(input as any))),

        deleteMany: procedure.input($Schema.CatalogItemAttributeInputSchema.deleteMany.optional()).mutation(async ({ ctx, input }) => checkMutate(db(ctx).catalogItemAttribute.deleteMany(input as any))),

        delete: procedure.input($Schema.CatalogItemAttributeInputSchema.delete).mutation(async ({ ctx, input }) => checkMutate(db(ctx).catalogItemAttribute.delete(input as any))),

        findFirst: procedure.input($Schema.CatalogItemAttributeInputSchema.findFirst.optional()).query(({ ctx, input }) => checkRead(db(ctx).catalogItemAttribute.findFirst(input as any))),

        findFirstOrThrow: procedure.input($Schema.CatalogItemAttributeInputSchema.findFirst.optional()).query(({ ctx, input }) => checkRead(db(ctx).catalogItemAttribute.findFirstOrThrow(input as any))),

        findMany: procedure.input($Schema.CatalogItemAttributeInputSchema.findMany.optional()).query(({ ctx, input }) => checkRead(db(ctx).catalogItemAttribute.findMany(input as any))),

        findUnique: procedure.input($Schema.CatalogItemAttributeInputSchema.findUnique).query(({ ctx, input }) => checkRead(db(ctx).catalogItemAttribute.findUnique(input as any))),

        findUniqueOrThrow: procedure.input($Schema.CatalogItemAttributeInputSchema.findUnique).query(({ ctx, input }) => checkRead(db(ctx).catalogItemAttribute.findUniqueOrThrow(input as any))),

        groupBy: procedure.input($Schema.CatalogItemAttributeInputSchema.groupBy).query(({ ctx, input }) => checkRead(db(ctx).catalogItemAttribute.groupBy(input as any))),

        updateMany: procedure.input($Schema.CatalogItemAttributeInputSchema.updateMany).mutation(async ({ ctx, input }) => checkMutate(db(ctx).catalogItemAttribute.updateMany(input as any))),

        update: procedure.input($Schema.CatalogItemAttributeInputSchema.update).mutation(async ({ ctx, input }) => checkMutate(db(ctx).catalogItemAttribute.update(input as any))),

        upsert: procedure.input($Schema.CatalogItemAttributeInputSchema.upsert).mutation(async ({ ctx, input }) => checkMutate(db(ctx).catalogItemAttribute.upsert(input as any))),

        count: procedure.input($Schema.CatalogItemAttributeInputSchema.count.optional()).query(({ ctx, input }) => checkRead(db(ctx).catalogItemAttribute.count(input as any))),

    }
    );
}
