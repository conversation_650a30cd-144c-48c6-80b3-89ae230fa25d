/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { db } from ".";
import { createTRPCRouter } from "../../../trpc";
import { procedure } from "../../../trpc";
import * as _Schema from '@zenstackhq/runtime/zod/input';
const $Schema: typeof _Schema = (_Schema as any).default ?? _Schema;
import { checkRead, checkMutate } from '../helper';

export default function createRouter() {
    return createTRPCRouter({

        aggregate: procedure.input($Schema.EquipmentApplicabilityInputSchema.aggregate).query(({ ctx, input }) => checkRead(db(ctx).equipmentApplicability.aggregate(input as any))),

        createMany: procedure.input($Schema.EquipmentApplicabilityInputSchema.createMany.optional()).mutation(async ({ ctx, input }) => checkMutate(db(ctx).equipmentApplicability.createMany(input as any))),

        create: procedure.input($Schema.EquipmentApplicabilityInputSchema.create).mutation(async ({ ctx, input }) => checkMutate(db(ctx).equipmentApplicability.create(input as any))),

        deleteMany: procedure.input($Schema.EquipmentApplicabilityInputSchema.deleteMany.optional()).mutation(async ({ ctx, input }) => checkMutate(db(ctx).equipmentApplicability.deleteMany(input as any))),

        delete: procedure.input($Schema.EquipmentApplicabilityInputSchema.delete).mutation(async ({ ctx, input }) => checkMutate(db(ctx).equipmentApplicability.delete(input as any))),

        findFirst: procedure.input($Schema.EquipmentApplicabilityInputSchema.findFirst.optional()).query(({ ctx, input }) => checkRead(db(ctx).equipmentApplicability.findFirst(input as any))),

        findFirstOrThrow: procedure.input($Schema.EquipmentApplicabilityInputSchema.findFirst.optional()).query(({ ctx, input }) => checkRead(db(ctx).equipmentApplicability.findFirstOrThrow(input as any))),

        findMany: procedure.input($Schema.EquipmentApplicabilityInputSchema.findMany.optional()).query(({ ctx, input }) => checkRead(db(ctx).equipmentApplicability.findMany(input as any))),

        findUnique: procedure.input($Schema.EquipmentApplicabilityInputSchema.findUnique).query(({ ctx, input }) => checkRead(db(ctx).equipmentApplicability.findUnique(input as any))),

        findUniqueOrThrow: procedure.input($Schema.EquipmentApplicabilityInputSchema.findUnique).query(({ ctx, input }) => checkRead(db(ctx).equipmentApplicability.findUniqueOrThrow(input as any))),

        groupBy: procedure.input($Schema.EquipmentApplicabilityInputSchema.groupBy).query(({ ctx, input }) => checkRead(db(ctx).equipmentApplicability.groupBy(input as any))),

        updateMany: procedure.input($Schema.EquipmentApplicabilityInputSchema.updateMany).mutation(async ({ ctx, input }) => checkMutate(db(ctx).equipmentApplicability.updateMany(input as any))),

        update: procedure.input($Schema.EquipmentApplicabilityInputSchema.update).mutation(async ({ ctx, input }) => checkMutate(db(ctx).equipmentApplicability.update(input as any))),

        upsert: procedure.input($Schema.EquipmentApplicabilityInputSchema.upsert).mutation(async ({ ctx, input }) => checkMutate(db(ctx).equipmentApplicability.upsert(input as any))),

        count: procedure.input($Schema.EquipmentApplicabilityInputSchema.count.optional()).query(({ ctx, input }) => checkRead(db(ctx).equipmentApplicability.count(input as any))),

    }
    );
}
