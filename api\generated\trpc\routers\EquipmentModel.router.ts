/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { db } from ".";
import { createTRPCRouter } from "../../../trpc";
import { procedure } from "../../../trpc";
import * as _Schema from '@zenstackhq/runtime/zod/input';
const $Schema: typeof _Schema = (_Schema as any).default ?? _Schema;
import { checkRead, checkMutate } from '../helper';

export default function createRouter() {
    return createTRPCRouter({

        aggregate: procedure.input($Schema.EquipmentModelInputSchema.aggregate).query(({ ctx, input }) => checkRead(db(ctx).equipmentModel.aggregate(input as any))),

        createMany: procedure.input($Schema.EquipmentModelInputSchema.createMany.optional()).mutation(async ({ ctx, input }) => checkMutate(db(ctx).equipmentModel.createMany(input as any))),

        create: procedure.input($Schema.EquipmentModelInputSchema.create).mutation(async ({ ctx, input }) => checkMutate(db(ctx).equipmentModel.create(input as any))),

        deleteMany: procedure.input($Schema.EquipmentModelInputSchema.deleteMany.optional()).mutation(async ({ ctx, input }) => checkMutate(db(ctx).equipmentModel.deleteMany(input as any))),

        delete: procedure.input($Schema.EquipmentModelInputSchema.delete).mutation(async ({ ctx, input }) => checkMutate(db(ctx).equipmentModel.delete(input as any))),

        findFirst: procedure.input($Schema.EquipmentModelInputSchema.findFirst.optional()).query(({ ctx, input }) => checkRead(db(ctx).equipmentModel.findFirst(input as any))),

        findFirstOrThrow: procedure.input($Schema.EquipmentModelInputSchema.findFirst.optional()).query(({ ctx, input }) => checkRead(db(ctx).equipmentModel.findFirstOrThrow(input as any))),

        findMany: procedure.input($Schema.EquipmentModelInputSchema.findMany.optional()).query(({ ctx, input }) => checkRead(db(ctx).equipmentModel.findMany(input as any))),

        findUnique: procedure.input($Schema.EquipmentModelInputSchema.findUnique).query(({ ctx, input }) => checkRead(db(ctx).equipmentModel.findUnique(input as any))),

        findUniqueOrThrow: procedure.input($Schema.EquipmentModelInputSchema.findUnique).query(({ ctx, input }) => checkRead(db(ctx).equipmentModel.findUniqueOrThrow(input as any))),

        groupBy: procedure.input($Schema.EquipmentModelInputSchema.groupBy).query(({ ctx, input }) => checkRead(db(ctx).equipmentModel.groupBy(input as any))),

        updateMany: procedure.input($Schema.EquipmentModelInputSchema.updateMany).mutation(async ({ ctx, input }) => checkMutate(db(ctx).equipmentModel.updateMany(input as any))),

        update: procedure.input($Schema.EquipmentModelInputSchema.update).mutation(async ({ ctx, input }) => checkMutate(db(ctx).equipmentModel.update(input as any))),

        upsert: procedure.input($Schema.EquipmentModelInputSchema.upsert).mutation(async ({ ctx, input }) => checkMutate(db(ctx).equipmentModel.upsert(input as any))),

        count: procedure.input($Schema.EquipmentModelInputSchema.count.optional()).query(({ ctx, input }) => checkRead(db(ctx).equipmentModel.count(input as any))),

    }
    );
}
