/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { db } from ".";
import { createTRPCRouter } from "../../../trpc";
import { procedure } from "../../../trpc";
import * as _Schema from '@zenstackhq/runtime/zod/input';
const $Schema: typeof _Schema = (_Schema as any).default ?? _Schema;
import { checkRead, checkMutate } from '../helper';

export default function createRouter() {
    return createTRPCRouter({

        aggregate: procedure.input($Schema.EquipmentModelAttributeInputSchema.aggregate).query(({ ctx, input }) => checkRead(db(ctx).equipmentModelAttribute.aggregate(input as any))),

        createMany: procedure.input($Schema.EquipmentModelAttributeInputSchema.createMany.optional()).mutation(async ({ ctx, input }) => checkMutate(db(ctx).equipmentModelAttribute.createMany(input as any))),

        create: procedure.input($Schema.EquipmentModelAttributeInputSchema.create).mutation(async ({ ctx, input }) => checkMutate(db(ctx).equipmentModelAttribute.create(input as any))),

        deleteMany: procedure.input($Schema.EquipmentModelAttributeInputSchema.deleteMany.optional()).mutation(async ({ ctx, input }) => checkMutate(db(ctx).equipmentModelAttribute.deleteMany(input as any))),

        delete: procedure.input($Schema.EquipmentModelAttributeInputSchema.delete).mutation(async ({ ctx, input }) => checkMutate(db(ctx).equipmentModelAttribute.delete(input as any))),

        findFirst: procedure.input($Schema.EquipmentModelAttributeInputSchema.findFirst.optional()).query(({ ctx, input }) => checkRead(db(ctx).equipmentModelAttribute.findFirst(input as any))),

        findFirstOrThrow: procedure.input($Schema.EquipmentModelAttributeInputSchema.findFirst.optional()).query(({ ctx, input }) => checkRead(db(ctx).equipmentModelAttribute.findFirstOrThrow(input as any))),

        findMany: procedure.input($Schema.EquipmentModelAttributeInputSchema.findMany.optional()).query(({ ctx, input }) => checkRead(db(ctx).equipmentModelAttribute.findMany(input as any))),

        findUnique: procedure.input($Schema.EquipmentModelAttributeInputSchema.findUnique).query(({ ctx, input }) => checkRead(db(ctx).equipmentModelAttribute.findUnique(input as any))),

        findUniqueOrThrow: procedure.input($Schema.EquipmentModelAttributeInputSchema.findUnique).query(({ ctx, input }) => checkRead(db(ctx).equipmentModelAttribute.findUniqueOrThrow(input as any))),

        groupBy: procedure.input($Schema.EquipmentModelAttributeInputSchema.groupBy).query(({ ctx, input }) => checkRead(db(ctx).equipmentModelAttribute.groupBy(input as any))),

        updateMany: procedure.input($Schema.EquipmentModelAttributeInputSchema.updateMany).mutation(async ({ ctx, input }) => checkMutate(db(ctx).equipmentModelAttribute.updateMany(input as any))),

        update: procedure.input($Schema.EquipmentModelAttributeInputSchema.update).mutation(async ({ ctx, input }) => checkMutate(db(ctx).equipmentModelAttribute.update(input as any))),

        upsert: procedure.input($Schema.EquipmentModelAttributeInputSchema.upsert).mutation(async ({ ctx, input }) => checkMutate(db(ctx).equipmentModelAttribute.upsert(input as any))),

        count: procedure.input($Schema.EquipmentModelAttributeInputSchema.count.optional()).query(({ ctx, input }) => checkRead(db(ctx).equipmentModelAttribute.count(input as any))),

    }
    );
}
