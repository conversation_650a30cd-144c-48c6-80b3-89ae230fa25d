/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { db } from ".";
import { createTRPCRouter } from "../../../trpc";
import { procedure } from "../../../trpc";
import * as _Schema from '@zenstackhq/runtime/zod/input';
const $Schema: typeof _Schema = (_Schema as any).default ?? _Schema;
import { checkRead, checkMutate } from '../helper';

export default function createRouter() {
    return createTRPCRouter({

        aggregate: procedure.input($Schema.MatchingProposalInputSchema.aggregate).query(({ ctx, input }) => checkRead(db(ctx).matchingProposal.aggregate(input as any))),

        createMany: procedure.input($Schema.MatchingProposalInputSchema.createMany.optional()).mutation(async ({ ctx, input }) => checkMutate(db(ctx).matchingProposal.createMany(input as any))),

        create: procedure.input($Schema.MatchingProposalInputSchema.create).mutation(async ({ ctx, input }) => checkMutate(db(ctx).matchingProposal.create(input as any))),

        deleteMany: procedure.input($Schema.MatchingProposalInputSchema.deleteMany.optional()).mutation(async ({ ctx, input }) => checkMutate(db(ctx).matchingProposal.deleteMany(input as any))),

        delete: procedure.input($Schema.MatchingProposalInputSchema.delete).mutation(async ({ ctx, input }) => checkMutate(db(ctx).matchingProposal.delete(input as any))),

        findFirst: procedure.input($Schema.MatchingProposalInputSchema.findFirst.optional()).query(({ ctx, input }) => checkRead(db(ctx).matchingProposal.findFirst(input as any))),

        findFirstOrThrow: procedure.input($Schema.MatchingProposalInputSchema.findFirst.optional()).query(({ ctx, input }) => checkRead(db(ctx).matchingProposal.findFirstOrThrow(input as any))),

        findMany: procedure.input($Schema.MatchingProposalInputSchema.findMany.optional()).query(({ ctx, input }) => checkRead(db(ctx).matchingProposal.findMany(input as any))),

        findUnique: procedure.input($Schema.MatchingProposalInputSchema.findUnique).query(({ ctx, input }) => checkRead(db(ctx).matchingProposal.findUnique(input as any))),

        findUniqueOrThrow: procedure.input($Schema.MatchingProposalInputSchema.findUnique).query(({ ctx, input }) => checkRead(db(ctx).matchingProposal.findUniqueOrThrow(input as any))),

        groupBy: procedure.input($Schema.MatchingProposalInputSchema.groupBy).query(({ ctx, input }) => checkRead(db(ctx).matchingProposal.groupBy(input as any))),

        updateMany: procedure.input($Schema.MatchingProposalInputSchema.updateMany).mutation(async ({ ctx, input }) => checkMutate(db(ctx).matchingProposal.updateMany(input as any))),

        update: procedure.input($Schema.MatchingProposalInputSchema.update).mutation(async ({ ctx, input }) => checkMutate(db(ctx).matchingProposal.update(input as any))),

        upsert: procedure.input($Schema.MatchingProposalInputSchema.upsert).mutation(async ({ ctx, input }) => checkMutate(db(ctx).matchingProposal.upsert(input as any))),

        count: procedure.input($Schema.MatchingProposalInputSchema.count.optional()).query(({ ctx, input }) => checkRead(db(ctx).matchingProposal.count(input as any))),

    }
    );
}
