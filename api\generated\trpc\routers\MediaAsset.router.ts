/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { db } from ".";
import { createTRPCRouter } from "../../../trpc";
import { procedure } from "../../../trpc";
import * as _Schema from '@zenstackhq/runtime/zod/input';
const $Schema: typeof _Schema = (_Schema as any).default ?? _Schema;
import { checkRead, checkMutate } from '../helper';

export default function createRouter() {
    return createTRPCRouter({

        aggregate: procedure.input($Schema.MediaAssetInputSchema.aggregate).query(({ ctx, input }) => checkRead(db(ctx).mediaAsset.aggregate(input as any))),

        createMany: procedure.input($Schema.MediaAssetInputSchema.createMany.optional()).mutation(async ({ ctx, input }) => checkMutate(db(ctx).mediaAsset.createMany(input as any))),

        create: procedure.input($Schema.MediaAssetInputSchema.create).mutation(async ({ ctx, input }) => checkMutate(db(ctx).mediaAsset.create(input as any))),

        deleteMany: procedure.input($Schema.MediaAssetInputSchema.deleteMany.optional()).mutation(async ({ ctx, input }) => checkMutate(db(ctx).mediaAsset.deleteMany(input as any))),

        delete: procedure.input($Schema.MediaAssetInputSchema.delete).mutation(async ({ ctx, input }) => checkMutate(db(ctx).mediaAsset.delete(input as any))),

        findFirst: procedure.input($Schema.MediaAssetInputSchema.findFirst.optional()).query(({ ctx, input }) => checkRead(db(ctx).mediaAsset.findFirst(input as any))),

        findFirstOrThrow: procedure.input($Schema.MediaAssetInputSchema.findFirst.optional()).query(({ ctx, input }) => checkRead(db(ctx).mediaAsset.findFirstOrThrow(input as any))),

        findMany: procedure.input($Schema.MediaAssetInputSchema.findMany.optional()).query(({ ctx, input }) => checkRead(db(ctx).mediaAsset.findMany(input as any))),

        findUnique: procedure.input($Schema.MediaAssetInputSchema.findUnique).query(({ ctx, input }) => checkRead(db(ctx).mediaAsset.findUnique(input as any))),

        findUniqueOrThrow: procedure.input($Schema.MediaAssetInputSchema.findUnique).query(({ ctx, input }) => checkRead(db(ctx).mediaAsset.findUniqueOrThrow(input as any))),

        groupBy: procedure.input($Schema.MediaAssetInputSchema.groupBy).query(({ ctx, input }) => checkRead(db(ctx).mediaAsset.groupBy(input as any))),

        updateMany: procedure.input($Schema.MediaAssetInputSchema.updateMany).mutation(async ({ ctx, input }) => checkMutate(db(ctx).mediaAsset.updateMany(input as any))),

        update: procedure.input($Schema.MediaAssetInputSchema.update).mutation(async ({ ctx, input }) => checkMutate(db(ctx).mediaAsset.update(input as any))),

        upsert: procedure.input($Schema.MediaAssetInputSchema.upsert).mutation(async ({ ctx, input }) => checkMutate(db(ctx).mediaAsset.upsert(input as any))),

        count: procedure.input($Schema.MediaAssetInputSchema.count.optional()).query(({ ctx, input }) => checkRead(db(ctx).mediaAsset.count(input as any))),

    }
    );
}
