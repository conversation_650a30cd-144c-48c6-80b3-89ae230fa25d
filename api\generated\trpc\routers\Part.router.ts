/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { db } from ".";
import { createTRPCRouter } from "../../../trpc";
import { procedure } from "../../../trpc";
import * as _Schema from '@zenstackhq/runtime/zod/input';
const $Schema: typeof _Schema = (_Schema as any).default ?? _Schema;
import { checkRead, checkMutate } from '../helper';

export default function createRouter() {
    return createTRPCRouter({

        aggregate: procedure.input($Schema.PartInputSchema.aggregate).query(({ ctx, input }) => checkRead(db(ctx).part.aggregate(input as any))),

        createMany: procedure.input($Schema.PartInputSchema.createMany.optional()).mutation(async ({ ctx, input }) => checkMutate(db(ctx).part.createMany(input as any))),

        create: procedure.input($Schema.PartInputSchema.create).mutation(async ({ ctx, input }) => checkMutate(db(ctx).part.create(input as any))),

        deleteMany: procedure.input($Schema.PartInputSchema.deleteMany.optional()).mutation(async ({ ctx, input }) => checkMutate(db(ctx).part.deleteMany(input as any))),

        delete: procedure.input($Schema.PartInputSchema.delete).mutation(async ({ ctx, input }) => checkMutate(db(ctx).part.delete(input as any))),

        findFirst: procedure.input($Schema.PartInputSchema.findFirst.optional()).query(({ ctx, input }) => checkRead(db(ctx).part.findFirst(input as any))),

        findFirstOrThrow: procedure.input($Schema.PartInputSchema.findFirst.optional()).query(({ ctx, input }) => checkRead(db(ctx).part.findFirstOrThrow(input as any))),

        findMany: procedure.input($Schema.PartInputSchema.findMany.optional()).query(({ ctx, input }) => checkRead(db(ctx).part.findMany(input as any))),

        findUnique: procedure.input($Schema.PartInputSchema.findUnique).query(({ ctx, input }) => checkRead(db(ctx).part.findUnique(input as any))),

        findUniqueOrThrow: procedure.input($Schema.PartInputSchema.findUnique).query(({ ctx, input }) => checkRead(db(ctx).part.findUniqueOrThrow(input as any))),

        groupBy: procedure.input($Schema.PartInputSchema.groupBy).query(({ ctx, input }) => checkRead(db(ctx).part.groupBy(input as any))),

        updateMany: procedure.input($Schema.PartInputSchema.updateMany).mutation(async ({ ctx, input }) => checkMutate(db(ctx).part.updateMany(input as any))),

        update: procedure.input($Schema.PartInputSchema.update).mutation(async ({ ctx, input }) => checkMutate(db(ctx).part.update(input as any))),

        upsert: procedure.input($Schema.PartInputSchema.upsert).mutation(async ({ ctx, input }) => checkMutate(db(ctx).part.upsert(input as any))),

        count: procedure.input($Schema.PartInputSchema.count.optional()).query(({ ctx, input }) => checkRead(db(ctx).part.count(input as any))),

    }
    );
}
