/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { db } from ".";
import { createTRPCRouter } from "../../../trpc";
import { procedure } from "../../../trpc";
import * as _Schema from '@zenstackhq/runtime/zod/input';
const $Schema: typeof _Schema = (_Schema as any).default ?? _Schema;
import { checkRead, checkMutate } from '../helper';

export default function createRouter() {
    return createTRPCRouter({

        aggregate: procedure.input($Schema.PartApplicabilityInputSchema.aggregate).query(({ ctx, input }) => checkRead(db(ctx).partApplicability.aggregate(input as any))),

        createMany: procedure.input($Schema.PartApplicabilityInputSchema.createMany.optional()).mutation(async ({ ctx, input }) => checkMutate(db(ctx).partApplicability.createMany(input as any))),

        create: procedure.input($Schema.PartApplicabilityInputSchema.create).mutation(async ({ ctx, input }) => checkMutate(db(ctx).partApplicability.create(input as any))),

        deleteMany: procedure.input($Schema.PartApplicabilityInputSchema.deleteMany.optional()).mutation(async ({ ctx, input }) => checkMutate(db(ctx).partApplicability.deleteMany(input as any))),

        delete: procedure.input($Schema.PartApplicabilityInputSchema.delete).mutation(async ({ ctx, input }) => checkMutate(db(ctx).partApplicability.delete(input as any))),

        findFirst: procedure.input($Schema.PartApplicabilityInputSchema.findFirst.optional()).query(({ ctx, input }) => checkRead(db(ctx).partApplicability.findFirst(input as any))),

        findFirstOrThrow: procedure.input($Schema.PartApplicabilityInputSchema.findFirst.optional()).query(({ ctx, input }) => checkRead(db(ctx).partApplicability.findFirstOrThrow(input as any))),

        findMany: procedure.input($Schema.PartApplicabilityInputSchema.findMany.optional()).query(({ ctx, input }) => checkRead(db(ctx).partApplicability.findMany(input as any))),

        findUnique: procedure.input($Schema.PartApplicabilityInputSchema.findUnique).query(({ ctx, input }) => checkRead(db(ctx).partApplicability.findUnique(input as any))),

        findUniqueOrThrow: procedure.input($Schema.PartApplicabilityInputSchema.findUnique).query(({ ctx, input }) => checkRead(db(ctx).partApplicability.findUniqueOrThrow(input as any))),

        groupBy: procedure.input($Schema.PartApplicabilityInputSchema.groupBy).query(({ ctx, input }) => checkRead(db(ctx).partApplicability.groupBy(input as any))),

        updateMany: procedure.input($Schema.PartApplicabilityInputSchema.updateMany).mutation(async ({ ctx, input }) => checkMutate(db(ctx).partApplicability.updateMany(input as any))),

        update: procedure.input($Schema.PartApplicabilityInputSchema.update).mutation(async ({ ctx, input }) => checkMutate(db(ctx).partApplicability.update(input as any))),

        upsert: procedure.input($Schema.PartApplicabilityInputSchema.upsert).mutation(async ({ ctx, input }) => checkMutate(db(ctx).partApplicability.upsert(input as any))),

        count: procedure.input($Schema.PartApplicabilityInputSchema.count.optional()).query(({ ctx, input }) => checkRead(db(ctx).partApplicability.count(input as any))),

    }
    );
}
