/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { db } from ".";
import { createTRPCRouter } from "../../../trpc";
import { procedure } from "../../../trpc";
import * as _Schema from '@zenstackhq/runtime/zod/input';
const $Schema: typeof _Schema = (_Schema as any).default ?? _Schema;
import { checkRead, checkMutate } from '../helper';

export default function createRouter() {
    return createTRPCRouter({

        aggregate: procedure.input($Schema.PartAttributeInputSchema.aggregate).query(({ ctx, input }) => checkRead(db(ctx).partAttribute.aggregate(input as any))),

        createMany: procedure.input($Schema.PartAttributeInputSchema.createMany.optional()).mutation(async ({ ctx, input }) => checkMutate(db(ctx).partAttribute.createMany(input as any))),

        create: procedure.input($Schema.PartAttributeInputSchema.create).mutation(async ({ ctx, input }) => checkMutate(db(ctx).partAttribute.create(input as any))),

        deleteMany: procedure.input($Schema.PartAttributeInputSchema.deleteMany.optional()).mutation(async ({ ctx, input }) => checkMutate(db(ctx).partAttribute.deleteMany(input as any))),

        delete: procedure.input($Schema.PartAttributeInputSchema.delete).mutation(async ({ ctx, input }) => checkMutate(db(ctx).partAttribute.delete(input as any))),

        findFirst: procedure.input($Schema.PartAttributeInputSchema.findFirst.optional()).query(({ ctx, input }) => checkRead(db(ctx).partAttribute.findFirst(input as any))),

        findFirstOrThrow: procedure.input($Schema.PartAttributeInputSchema.findFirst.optional()).query(({ ctx, input }) => checkRead(db(ctx).partAttribute.findFirstOrThrow(input as any))),

        findMany: procedure.input($Schema.PartAttributeInputSchema.findMany.optional()).query(({ ctx, input }) => checkRead(db(ctx).partAttribute.findMany(input as any))),

        findUnique: procedure.input($Schema.PartAttributeInputSchema.findUnique).query(({ ctx, input }) => checkRead(db(ctx).partAttribute.findUnique(input as any))),

        findUniqueOrThrow: procedure.input($Schema.PartAttributeInputSchema.findUnique).query(({ ctx, input }) => checkRead(db(ctx).partAttribute.findUniqueOrThrow(input as any))),

        groupBy: procedure.input($Schema.PartAttributeInputSchema.groupBy).query(({ ctx, input }) => checkRead(db(ctx).partAttribute.groupBy(input as any))),

        updateMany: procedure.input($Schema.PartAttributeInputSchema.updateMany).mutation(async ({ ctx, input }) => checkMutate(db(ctx).partAttribute.updateMany(input as any))),

        update: procedure.input($Schema.PartAttributeInputSchema.update).mutation(async ({ ctx, input }) => checkMutate(db(ctx).partAttribute.update(input as any))),

        upsert: procedure.input($Schema.PartAttributeInputSchema.upsert).mutation(async ({ ctx, input }) => checkMutate(db(ctx).partAttribute.upsert(input as any))),

        count: procedure.input($Schema.PartAttributeInputSchema.count.optional()).query(({ ctx, input }) => checkRead(db(ctx).partAttribute.count(input as any))),

    }
    );
}
