/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { db } from ".";
import { createTRPCRouter } from "../../../trpc";
import { procedure } from "../../../trpc";
import * as _Schema from '@zenstackhq/runtime/zod/input';
const $Schema: typeof _Schema = (_Schema as any).default ?? _Schema;
import { checkRead, checkMutate } from '../helper';

export default function createRouter() {
    return createTRPCRouter({

        aggregate: procedure.input($Schema.PartCategoryInputSchema.aggregate).query(({ ctx, input }) => checkRead(db(ctx).partCategory.aggregate(input as any))),

        createMany: procedure.input($Schema.PartCategoryInputSchema.createMany.optional()).mutation(async ({ ctx, input }) => checkMutate(db(ctx).partCategory.createMany(input as any))),

        create: procedure.input($Schema.PartCategoryInputSchema.create).mutation(async ({ ctx, input }) => checkMutate(db(ctx).partCategory.create(input as any))),

        deleteMany: procedure.input($Schema.PartCategoryInputSchema.deleteMany.optional()).mutation(async ({ ctx, input }) => checkMutate(db(ctx).partCategory.deleteMany(input as any))),

        delete: procedure.input($Schema.PartCategoryInputSchema.delete).mutation(async ({ ctx, input }) => checkMutate(db(ctx).partCategory.delete(input as any))),

        findFirst: procedure.input($Schema.PartCategoryInputSchema.findFirst.optional()).query(({ ctx, input }) => checkRead(db(ctx).partCategory.findFirst(input as any))),

        findFirstOrThrow: procedure.input($Schema.PartCategoryInputSchema.findFirst.optional()).query(({ ctx, input }) => checkRead(db(ctx).partCategory.findFirstOrThrow(input as any))),

        findMany: procedure.input($Schema.PartCategoryInputSchema.findMany.optional()).query(({ ctx, input }) => checkRead(db(ctx).partCategory.findMany(input as any))),

        findUnique: procedure.input($Schema.PartCategoryInputSchema.findUnique).query(({ ctx, input }) => checkRead(db(ctx).partCategory.findUnique(input as any))),

        findUniqueOrThrow: procedure.input($Schema.PartCategoryInputSchema.findUnique).query(({ ctx, input }) => checkRead(db(ctx).partCategory.findUniqueOrThrow(input as any))),

        groupBy: procedure.input($Schema.PartCategoryInputSchema.groupBy).query(({ ctx, input }) => checkRead(db(ctx).partCategory.groupBy(input as any))),

        updateMany: procedure.input($Schema.PartCategoryInputSchema.updateMany).mutation(async ({ ctx, input }) => checkMutate(db(ctx).partCategory.updateMany(input as any))),

        update: procedure.input($Schema.PartCategoryInputSchema.update).mutation(async ({ ctx, input }) => checkMutate(db(ctx).partCategory.update(input as any))),

        upsert: procedure.input($Schema.PartCategoryInputSchema.upsert).mutation(async ({ ctx, input }) => checkMutate(db(ctx).partCategory.upsert(input as any))),

        count: procedure.input($Schema.PartCategoryInputSchema.count.optional()).query(({ ctx, input }) => checkRead(db(ctx).partCategory.count(input as any))),

    }
    );
}
