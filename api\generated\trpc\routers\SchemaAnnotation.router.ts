/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { db } from ".";
import { createTRPCRouter } from "../../../trpc";
import { procedure } from "../../../trpc";
import * as _Schema from '@zenstackhq/runtime/zod/input';
const $Schema: typeof _Schema = (_Schema as any).default ?? _Schema;
import { checkRead, checkMutate } from '../helper';

export default function createRouter() {
    return createTRPCRouter({

        aggregate: procedure.input($Schema.SchemaAnnotationInputSchema.aggregate).query(({ ctx, input }) => checkRead(db(ctx).schemaAnnotation.aggregate(input as any))),

        createMany: procedure.input($Schema.SchemaAnnotationInputSchema.createMany.optional()).mutation(async ({ ctx, input }) => checkMutate(db(ctx).schemaAnnotation.createMany(input as any))),

        create: procedure.input($Schema.SchemaAnnotationInputSchema.create).mutation(async ({ ctx, input }) => checkMutate(db(ctx).schemaAnnotation.create(input as any))),

        deleteMany: procedure.input($Schema.SchemaAnnotationInputSchema.deleteMany.optional()).mutation(async ({ ctx, input }) => checkMutate(db(ctx).schemaAnnotation.deleteMany(input as any))),

        delete: procedure.input($Schema.SchemaAnnotationInputSchema.delete).mutation(async ({ ctx, input }) => checkMutate(db(ctx).schemaAnnotation.delete(input as any))),

        findFirst: procedure.input($Schema.SchemaAnnotationInputSchema.findFirst.optional()).query(({ ctx, input }) => checkRead(db(ctx).schemaAnnotation.findFirst(input as any))),

        findFirstOrThrow: procedure.input($Schema.SchemaAnnotationInputSchema.findFirst.optional()).query(({ ctx, input }) => checkRead(db(ctx).schemaAnnotation.findFirstOrThrow(input as any))),

        findMany: procedure.input($Schema.SchemaAnnotationInputSchema.findMany.optional()).query(({ ctx, input }) => checkRead(db(ctx).schemaAnnotation.findMany(input as any))),

        findUnique: procedure.input($Schema.SchemaAnnotationInputSchema.findUnique).query(({ ctx, input }) => checkRead(db(ctx).schemaAnnotation.findUnique(input as any))),

        findUniqueOrThrow: procedure.input($Schema.SchemaAnnotationInputSchema.findUnique).query(({ ctx, input }) => checkRead(db(ctx).schemaAnnotation.findUniqueOrThrow(input as any))),

        groupBy: procedure.input($Schema.SchemaAnnotationInputSchema.groupBy).query(({ ctx, input }) => checkRead(db(ctx).schemaAnnotation.groupBy(input as any))),

        updateMany: procedure.input($Schema.SchemaAnnotationInputSchema.updateMany).mutation(async ({ ctx, input }) => checkMutate(db(ctx).schemaAnnotation.updateMany(input as any))),

        update: procedure.input($Schema.SchemaAnnotationInputSchema.update).mutation(async ({ ctx, input }) => checkMutate(db(ctx).schemaAnnotation.update(input as any))),

        upsert: procedure.input($Schema.SchemaAnnotationInputSchema.upsert).mutation(async ({ ctx, input }) => checkMutate(db(ctx).schemaAnnotation.upsert(input as any))),

        count: procedure.input($Schema.SchemaAnnotationInputSchema.count.optional()).query(({ ctx, input }) => checkRead(db(ctx).schemaAnnotation.count(input as any))),

    }
    );
}
