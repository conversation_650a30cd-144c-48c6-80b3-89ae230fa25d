/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { db } from ".";
import { createTRPCRouter } from "../../../trpc";
import { procedure } from "../../../trpc";
import * as _Schema from '@zenstackhq/runtime/zod/input';
const $Schema: typeof _Schema = (_Schema as any).default ?? _Schema;
import { checkRead, checkMutate } from '../helper';

export default function createRouter() {
    return createTRPCRouter({

        aggregate: procedure.input($Schema.SchemaPositionInputSchema.aggregate).query(({ ctx, input }) => checkRead(db(ctx).schemaPosition.aggregate(input as any))),

        createMany: procedure.input($Schema.SchemaPositionInputSchema.createMany.optional()).mutation(async ({ ctx, input }) => checkMutate(db(ctx).schemaPosition.createMany(input as any))),

        create: procedure.input($Schema.SchemaPositionInputSchema.create).mutation(async ({ ctx, input }) => checkMutate(db(ctx).schemaPosition.create(input as any))),

        deleteMany: procedure.input($Schema.SchemaPositionInputSchema.deleteMany.optional()).mutation(async ({ ctx, input }) => checkMutate(db(ctx).schemaPosition.deleteMany(input as any))),

        delete: procedure.input($Schema.SchemaPositionInputSchema.delete).mutation(async ({ ctx, input }) => checkMutate(db(ctx).schemaPosition.delete(input as any))),

        findFirst: procedure.input($Schema.SchemaPositionInputSchema.findFirst.optional()).query(({ ctx, input }) => checkRead(db(ctx).schemaPosition.findFirst(input as any))),

        findFirstOrThrow: procedure.input($Schema.SchemaPositionInputSchema.findFirst.optional()).query(({ ctx, input }) => checkRead(db(ctx).schemaPosition.findFirstOrThrow(input as any))),

        findMany: procedure.input($Schema.SchemaPositionInputSchema.findMany.optional()).query(({ ctx, input }) => checkRead(db(ctx).schemaPosition.findMany(input as any))),

        findUnique: procedure.input($Schema.SchemaPositionInputSchema.findUnique).query(({ ctx, input }) => checkRead(db(ctx).schemaPosition.findUnique(input as any))),

        findUniqueOrThrow: procedure.input($Schema.SchemaPositionInputSchema.findUnique).query(({ ctx, input }) => checkRead(db(ctx).schemaPosition.findUniqueOrThrow(input as any))),

        groupBy: procedure.input($Schema.SchemaPositionInputSchema.groupBy).query(({ ctx, input }) => checkRead(db(ctx).schemaPosition.groupBy(input as any))),

        updateMany: procedure.input($Schema.SchemaPositionInputSchema.updateMany).mutation(async ({ ctx, input }) => checkMutate(db(ctx).schemaPosition.updateMany(input as any))),

        update: procedure.input($Schema.SchemaPositionInputSchema.update).mutation(async ({ ctx, input }) => checkMutate(db(ctx).schemaPosition.update(input as any))),

        upsert: procedure.input($Schema.SchemaPositionInputSchema.upsert).mutation(async ({ ctx, input }) => checkMutate(db(ctx).schemaPosition.upsert(input as any))),

        count: procedure.input($Schema.SchemaPositionInputSchema.count.optional()).query(({ ctx, input }) => checkRead(db(ctx).schemaPosition.count(input as any))),

    }
    );
}
