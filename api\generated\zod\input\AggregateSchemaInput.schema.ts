/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
;
import type { Prisma } from '@prisma/client';

;
import { AggregateSchemaSelectObjectSchema } from '../objects/AggregateSchemaSelect.schema';
import { AggregateSchemaIncludeObjectSchema } from '../objects/AggregateSchemaInclude.schema';
import { AggregateSchemaWhereUniqueInputObjectSchema } from '../objects/AggregateSchemaWhereUniqueInput.schema';
import { AggregateSchemaWhereInputObjectSchema } from '../objects/AggregateSchemaWhereInput.schema';
import { AggregateSchemaOrderByWithRelationInputObjectSchema } from '../objects/AggregateSchemaOrderByWithRelationInput.schema';
import { AggregateSchemaScalarFieldEnumSchema } from '../enums/AggregateSchemaScalarFieldEnum.schema';
import { AggregateSchemaCreateInputObjectSchema } from '../objects/AggregateSchemaCreateInput.schema';
import { AggregateSchemaUncheckedCreateInputObjectSchema } from '../objects/AggregateSchemaUncheckedCreateInput.schema';
import { AggregateSchemaCreateManyInputObjectSchema } from '../objects/AggregateSchemaCreateManyInput.schema';
import { AggregateSchemaUpdateInputObjectSchema } from '../objects/AggregateSchemaUpdateInput.schema';
import { AggregateSchemaUncheckedUpdateInputObjectSchema } from '../objects/AggregateSchemaUncheckedUpdateInput.schema';
import { AggregateSchemaUpdateManyMutationInputObjectSchema } from '../objects/AggregateSchemaUpdateManyMutationInput.schema';
import { AggregateSchemaUncheckedUpdateManyInputObjectSchema } from '../objects/AggregateSchemaUncheckedUpdateManyInput.schema';
import { AggregateSchemaCountAggregateInputObjectSchema } from '../objects/AggregateSchemaCountAggregateInput.schema';
import { AggregateSchemaMinAggregateInputObjectSchema } from '../objects/AggregateSchemaMinAggregateInput.schema';
import { AggregateSchemaMaxAggregateInputObjectSchema } from '../objects/AggregateSchemaMaxAggregateInput.schema';
import { AggregateSchemaAvgAggregateInputObjectSchema } from '../objects/AggregateSchemaAvgAggregateInput.schema';
import { AggregateSchemaSumAggregateInputObjectSchema } from '../objects/AggregateSchemaSumAggregateInput.schema';
import { AggregateSchemaOrderByWithAggregationInputObjectSchema } from '../objects/AggregateSchemaOrderByWithAggregationInput.schema';
import { AggregateSchemaScalarWhereWithAggregatesInputObjectSchema } from '../objects/AggregateSchemaScalarWhereWithAggregatesInput.schema'

type AggregateSchemaInputSchemaType = {
    findUnique: z.ZodType<Prisma.AggregateSchemaFindUniqueArgs>,
    findFirst: z.ZodType<Prisma.AggregateSchemaFindFirstArgs>,
    findMany: z.ZodType<Prisma.AggregateSchemaFindManyArgs>,
    create: z.ZodType<Prisma.AggregateSchemaCreateArgs>,
    createMany: z.ZodType<Prisma.AggregateSchemaCreateManyArgs>,
    delete: z.ZodType<Prisma.AggregateSchemaDeleteArgs>,
    deleteMany: z.ZodType<Prisma.AggregateSchemaDeleteManyArgs>,
    update: z.ZodType<Prisma.AggregateSchemaUpdateArgs>,
    updateMany: z.ZodType<Prisma.AggregateSchemaUpdateManyArgs>,
    upsert: z.ZodType<Prisma.AggregateSchemaUpsertArgs>,
    aggregate: z.ZodType<Prisma.AggregateSchemaAggregateArgs>,
    groupBy: z.ZodType<Prisma.AggregateSchemaGroupByArgs>,
    count: z.ZodType<Prisma.AggregateSchemaCountArgs>
}

export const AggregateSchemaInputSchema = {
    findUnique: z.object({
        select: z.lazy(() => AggregateSchemaSelectObjectSchema.optional()), include: z.lazy(() => AggregateSchemaIncludeObjectSchema.optional()), where: AggregateSchemaWhereUniqueInputObjectSchema
    }).strict(), findFirst: z.object({
        select: z.lazy(() => AggregateSchemaSelectObjectSchema.optional()), include: z.lazy(() => AggregateSchemaIncludeObjectSchema.optional()), where: AggregateSchemaWhereInputObjectSchema.optional(), orderBy: z.union([AggregateSchemaOrderByWithRelationInputObjectSchema, AggregateSchemaOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: AggregateSchemaWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(AggregateSchemaScalarFieldEnumSchema).optional()
    }).strict(), findMany: z.object({
        select: z.lazy(() => AggregateSchemaSelectObjectSchema.optional()), include: z.lazy(() => AggregateSchemaIncludeObjectSchema.optional()), where: AggregateSchemaWhereInputObjectSchema.optional(), orderBy: z.union([AggregateSchemaOrderByWithRelationInputObjectSchema, AggregateSchemaOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: AggregateSchemaWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(AggregateSchemaScalarFieldEnumSchema).optional()
    }).strict(), create: z.object({
        select: z.lazy(() => AggregateSchemaSelectObjectSchema.optional()), include: z.lazy(() => AggregateSchemaIncludeObjectSchema.optional()), data: z.union([AggregateSchemaCreateInputObjectSchema, AggregateSchemaUncheckedCreateInputObjectSchema])
    }).strict(), createMany: z.object({
        data: z.union([AggregateSchemaCreateManyInputObjectSchema, z.array(AggregateSchemaCreateManyInputObjectSchema)]), skipDuplicates: z.boolean().optional()
    }).strict(), 'delete': z.object({
        select: z.lazy(() => AggregateSchemaSelectObjectSchema.optional()), include: z.lazy(() => AggregateSchemaIncludeObjectSchema.optional()), where: AggregateSchemaWhereUniqueInputObjectSchema
    }).strict(), deleteMany: z.object({
        where: AggregateSchemaWhereInputObjectSchema.optional()
    }).strict(), update: z.object({
        select: z.lazy(() => AggregateSchemaSelectObjectSchema.optional()), include: z.lazy(() => AggregateSchemaIncludeObjectSchema.optional()), data: z.union([AggregateSchemaUpdateInputObjectSchema, AggregateSchemaUncheckedUpdateInputObjectSchema]), where: AggregateSchemaWhereUniqueInputObjectSchema
    }).strict(), updateMany: z.object({
        data: z.union([AggregateSchemaUpdateManyMutationInputObjectSchema, AggregateSchemaUncheckedUpdateManyInputObjectSchema]), where: AggregateSchemaWhereInputObjectSchema.optional()
    }).strict(), upsert: z.object({
        select: z.lazy(() => AggregateSchemaSelectObjectSchema.optional()), include: z.lazy(() => AggregateSchemaIncludeObjectSchema.optional()), where: AggregateSchemaWhereUniqueInputObjectSchema, create: z.union([AggregateSchemaCreateInputObjectSchema, AggregateSchemaUncheckedCreateInputObjectSchema]), update: z.union([AggregateSchemaUpdateInputObjectSchema, AggregateSchemaUncheckedUpdateInputObjectSchema])
    }).strict(), aggregate: z.object({
        where: AggregateSchemaWhereInputObjectSchema.optional(), orderBy: z.union([AggregateSchemaOrderByWithRelationInputObjectSchema, AggregateSchemaOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: AggregateSchemaWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), _count: z.union([z.literal(true), AggregateSchemaCountAggregateInputObjectSchema]).optional(), _min: AggregateSchemaMinAggregateInputObjectSchema.optional(), _max: AggregateSchemaMaxAggregateInputObjectSchema.optional(), _avg: AggregateSchemaAvgAggregateInputObjectSchema.optional(), _sum: AggregateSchemaSumAggregateInputObjectSchema.optional()
    }).strict(), groupBy: z.object({
        where: AggregateSchemaWhereInputObjectSchema.optional(), orderBy: z.union([AggregateSchemaOrderByWithAggregationInputObjectSchema, AggregateSchemaOrderByWithAggregationInputObjectSchema.array()]).optional(), having: AggregateSchemaScalarWhereWithAggregatesInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), by: z.array(AggregateSchemaScalarFieldEnumSchema), _count: z.union([z.literal(true), AggregateSchemaCountAggregateInputObjectSchema]).optional(), _min: AggregateSchemaMinAggregateInputObjectSchema.optional(), _max: AggregateSchemaMaxAggregateInputObjectSchema.optional(), _avg: AggregateSchemaAvgAggregateInputObjectSchema.optional(), _sum: AggregateSchemaSumAggregateInputObjectSchema.optional()
    }).strict(), count: z.object({
        where: AggregateSchemaWhereInputObjectSchema.optional(), orderBy: z.union([AggregateSchemaOrderByWithRelationInputObjectSchema, AggregateSchemaOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: AggregateSchemaWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(AggregateSchemaScalarFieldEnumSchema).optional(), select: z.union([z.literal(true), AggregateSchemaCountAggregateInputObjectSchema]).optional()
    }).strict(),
} as AggregateSchemaInputSchemaType;
