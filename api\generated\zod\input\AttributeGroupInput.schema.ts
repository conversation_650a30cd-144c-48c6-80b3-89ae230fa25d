/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
;
import type { Prisma } from '@prisma/client';

;
import { AttributeGroupSelectObjectSchema } from '../objects/AttributeGroupSelect.schema';
import { AttributeGroupIncludeObjectSchema } from '../objects/AttributeGroupInclude.schema';
import { AttributeGroupWhereUniqueInputObjectSchema } from '../objects/AttributeGroupWhereUniqueInput.schema';
import { AttributeGroupWhereInputObjectSchema } from '../objects/AttributeGroupWhereInput.schema';
import { AttributeGroupOrderByWithRelationInputObjectSchema } from '../objects/AttributeGroupOrderByWithRelationInput.schema';
import { AttributeGroupScalarFieldEnumSchema } from '../enums/AttributeGroupScalarFieldEnum.schema';
import { AttributeGroupCreateInputObjectSchema } from '../objects/AttributeGroupCreateInput.schema';
import { AttributeGroupUncheckedCreateInputObjectSchema } from '../objects/AttributeGroupUncheckedCreateInput.schema';
import { AttributeGroupCreateManyInputObjectSchema } from '../objects/AttributeGroupCreateManyInput.schema';
import { AttributeGroupUpdateInputObjectSchema } from '../objects/AttributeGroupUpdateInput.schema';
import { AttributeGroupUncheckedUpdateInputObjectSchema } from '../objects/AttributeGroupUncheckedUpdateInput.schema';
import { AttributeGroupUpdateManyMutationInputObjectSchema } from '../objects/AttributeGroupUpdateManyMutationInput.schema';
import { AttributeGroupUncheckedUpdateManyInputObjectSchema } from '../objects/AttributeGroupUncheckedUpdateManyInput.schema';
import { AttributeGroupCountAggregateInputObjectSchema } from '../objects/AttributeGroupCountAggregateInput.schema';
import { AttributeGroupMinAggregateInputObjectSchema } from '../objects/AttributeGroupMinAggregateInput.schema';
import { AttributeGroupMaxAggregateInputObjectSchema } from '../objects/AttributeGroupMaxAggregateInput.schema';
import { AttributeGroupAvgAggregateInputObjectSchema } from '../objects/AttributeGroupAvgAggregateInput.schema';
import { AttributeGroupSumAggregateInputObjectSchema } from '../objects/AttributeGroupSumAggregateInput.schema';
import { AttributeGroupOrderByWithAggregationInputObjectSchema } from '../objects/AttributeGroupOrderByWithAggregationInput.schema';
import { AttributeGroupScalarWhereWithAggregatesInputObjectSchema } from '../objects/AttributeGroupScalarWhereWithAggregatesInput.schema'

type AttributeGroupInputSchemaType = {
    findUnique: z.ZodType<Prisma.AttributeGroupFindUniqueArgs>,
    findFirst: z.ZodType<Prisma.AttributeGroupFindFirstArgs>,
    findMany: z.ZodType<Prisma.AttributeGroupFindManyArgs>,
    create: z.ZodType<Prisma.AttributeGroupCreateArgs>,
    createMany: z.ZodType<Prisma.AttributeGroupCreateManyArgs>,
    delete: z.ZodType<Prisma.AttributeGroupDeleteArgs>,
    deleteMany: z.ZodType<Prisma.AttributeGroupDeleteManyArgs>,
    update: z.ZodType<Prisma.AttributeGroupUpdateArgs>,
    updateMany: z.ZodType<Prisma.AttributeGroupUpdateManyArgs>,
    upsert: z.ZodType<Prisma.AttributeGroupUpsertArgs>,
    aggregate: z.ZodType<Prisma.AttributeGroupAggregateArgs>,
    groupBy: z.ZodType<Prisma.AttributeGroupGroupByArgs>,
    count: z.ZodType<Prisma.AttributeGroupCountArgs>
}

export const AttributeGroupInputSchema = {
    findUnique: z.object({
        select: z.lazy(() => AttributeGroupSelectObjectSchema.optional()), include: z.lazy(() => AttributeGroupIncludeObjectSchema.optional()), where: AttributeGroupWhereUniqueInputObjectSchema
    }).strict(), findFirst: z.object({
        select: z.lazy(() => AttributeGroupSelectObjectSchema.optional()), include: z.lazy(() => AttributeGroupIncludeObjectSchema.optional()), where: AttributeGroupWhereInputObjectSchema.optional(), orderBy: z.union([AttributeGroupOrderByWithRelationInputObjectSchema, AttributeGroupOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: AttributeGroupWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(AttributeGroupScalarFieldEnumSchema).optional()
    }).strict(), findMany: z.object({
        select: z.lazy(() => AttributeGroupSelectObjectSchema.optional()), include: z.lazy(() => AttributeGroupIncludeObjectSchema.optional()), where: AttributeGroupWhereInputObjectSchema.optional(), orderBy: z.union([AttributeGroupOrderByWithRelationInputObjectSchema, AttributeGroupOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: AttributeGroupWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(AttributeGroupScalarFieldEnumSchema).optional()
    }).strict(), create: z.object({
        select: z.lazy(() => AttributeGroupSelectObjectSchema.optional()), include: z.lazy(() => AttributeGroupIncludeObjectSchema.optional()), data: z.union([AttributeGroupCreateInputObjectSchema, AttributeGroupUncheckedCreateInputObjectSchema])
    }).strict(), createMany: z.object({
        data: z.union([AttributeGroupCreateManyInputObjectSchema, z.array(AttributeGroupCreateManyInputObjectSchema)]), skipDuplicates: z.boolean().optional()
    }).strict(), 'delete': z.object({
        select: z.lazy(() => AttributeGroupSelectObjectSchema.optional()), include: z.lazy(() => AttributeGroupIncludeObjectSchema.optional()), where: AttributeGroupWhereUniqueInputObjectSchema
    }).strict(), deleteMany: z.object({
        where: AttributeGroupWhereInputObjectSchema.optional()
    }).strict(), update: z.object({
        select: z.lazy(() => AttributeGroupSelectObjectSchema.optional()), include: z.lazy(() => AttributeGroupIncludeObjectSchema.optional()), data: z.union([AttributeGroupUpdateInputObjectSchema, AttributeGroupUncheckedUpdateInputObjectSchema]), where: AttributeGroupWhereUniqueInputObjectSchema
    }).strict(), updateMany: z.object({
        data: z.union([AttributeGroupUpdateManyMutationInputObjectSchema, AttributeGroupUncheckedUpdateManyInputObjectSchema]), where: AttributeGroupWhereInputObjectSchema.optional()
    }).strict(), upsert: z.object({
        select: z.lazy(() => AttributeGroupSelectObjectSchema.optional()), include: z.lazy(() => AttributeGroupIncludeObjectSchema.optional()), where: AttributeGroupWhereUniqueInputObjectSchema, create: z.union([AttributeGroupCreateInputObjectSchema, AttributeGroupUncheckedCreateInputObjectSchema]), update: z.union([AttributeGroupUpdateInputObjectSchema, AttributeGroupUncheckedUpdateInputObjectSchema])
    }).strict(), aggregate: z.object({
        where: AttributeGroupWhereInputObjectSchema.optional(), orderBy: z.union([AttributeGroupOrderByWithRelationInputObjectSchema, AttributeGroupOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: AttributeGroupWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), _count: z.union([z.literal(true), AttributeGroupCountAggregateInputObjectSchema]).optional(), _min: AttributeGroupMinAggregateInputObjectSchema.optional(), _max: AttributeGroupMaxAggregateInputObjectSchema.optional(), _avg: AttributeGroupAvgAggregateInputObjectSchema.optional(), _sum: AttributeGroupSumAggregateInputObjectSchema.optional()
    }).strict(), groupBy: z.object({
        where: AttributeGroupWhereInputObjectSchema.optional(), orderBy: z.union([AttributeGroupOrderByWithAggregationInputObjectSchema, AttributeGroupOrderByWithAggregationInputObjectSchema.array()]).optional(), having: AttributeGroupScalarWhereWithAggregatesInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), by: z.array(AttributeGroupScalarFieldEnumSchema), _count: z.union([z.literal(true), AttributeGroupCountAggregateInputObjectSchema]).optional(), _min: AttributeGroupMinAggregateInputObjectSchema.optional(), _max: AttributeGroupMaxAggregateInputObjectSchema.optional(), _avg: AttributeGroupAvgAggregateInputObjectSchema.optional(), _sum: AttributeGroupSumAggregateInputObjectSchema.optional()
    }).strict(), count: z.object({
        where: AttributeGroupWhereInputObjectSchema.optional(), orderBy: z.union([AttributeGroupOrderByWithRelationInputObjectSchema, AttributeGroupOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: AttributeGroupWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(AttributeGroupScalarFieldEnumSchema).optional(), select: z.union([z.literal(true), AttributeGroupCountAggregateInputObjectSchema]).optional()
    }).strict(),
} as AttributeGroupInputSchemaType;
