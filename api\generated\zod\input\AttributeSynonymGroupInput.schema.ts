/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
;
import type { Prisma } from '@prisma/client';

;
import { AttributeSynonymGroupSelectObjectSchema } from '../objects/AttributeSynonymGroupSelect.schema';
import { AttributeSynonymGroupIncludeObjectSchema } from '../objects/AttributeSynonymGroupInclude.schema';
import { AttributeSynonymGroupWhereUniqueInputObjectSchema } from '../objects/AttributeSynonymGroupWhereUniqueInput.schema';
import { AttributeSynonymGroupWhereInputObjectSchema } from '../objects/AttributeSynonymGroupWhereInput.schema';
import { AttributeSynonymGroupOrderByWithRelationInputObjectSchema } from '../objects/AttributeSynonymGroupOrderByWithRelationInput.schema';
import { AttributeSynonymGroupScalarFieldEnumSchema } from '../enums/AttributeSynonymGroupScalarFieldEnum.schema';
import { AttributeSynonymGroupCreateInputObjectSchema } from '../objects/AttributeSynonymGroupCreateInput.schema';
import { AttributeSynonymGroupUncheckedCreateInputObjectSchema } from '../objects/AttributeSynonymGroupUncheckedCreateInput.schema';
import { AttributeSynonymGroupCreateManyInputObjectSchema } from '../objects/AttributeSynonymGroupCreateManyInput.schema';
import { AttributeSynonymGroupUpdateInputObjectSchema } from '../objects/AttributeSynonymGroupUpdateInput.schema';
import { AttributeSynonymGroupUncheckedUpdateInputObjectSchema } from '../objects/AttributeSynonymGroupUncheckedUpdateInput.schema';
import { AttributeSynonymGroupUpdateManyMutationInputObjectSchema } from '../objects/AttributeSynonymGroupUpdateManyMutationInput.schema';
import { AttributeSynonymGroupUncheckedUpdateManyInputObjectSchema } from '../objects/AttributeSynonymGroupUncheckedUpdateManyInput.schema';
import { AttributeSynonymGroupCountAggregateInputObjectSchema } from '../objects/AttributeSynonymGroupCountAggregateInput.schema';
import { AttributeSynonymGroupMinAggregateInputObjectSchema } from '../objects/AttributeSynonymGroupMinAggregateInput.schema';
import { AttributeSynonymGroupMaxAggregateInputObjectSchema } from '../objects/AttributeSynonymGroupMaxAggregateInput.schema';
import { AttributeSynonymGroupAvgAggregateInputObjectSchema } from '../objects/AttributeSynonymGroupAvgAggregateInput.schema';
import { AttributeSynonymGroupSumAggregateInputObjectSchema } from '../objects/AttributeSynonymGroupSumAggregateInput.schema';
import { AttributeSynonymGroupOrderByWithAggregationInputObjectSchema } from '../objects/AttributeSynonymGroupOrderByWithAggregationInput.schema';
import { AttributeSynonymGroupScalarWhereWithAggregatesInputObjectSchema } from '../objects/AttributeSynonymGroupScalarWhereWithAggregatesInput.schema'

type AttributeSynonymGroupInputSchemaType = {
    findUnique: z.ZodType<Prisma.AttributeSynonymGroupFindUniqueArgs>,
    findFirst: z.ZodType<Prisma.AttributeSynonymGroupFindFirstArgs>,
    findMany: z.ZodType<Prisma.AttributeSynonymGroupFindManyArgs>,
    create: z.ZodType<Prisma.AttributeSynonymGroupCreateArgs>,
    createMany: z.ZodType<Prisma.AttributeSynonymGroupCreateManyArgs>,
    delete: z.ZodType<Prisma.AttributeSynonymGroupDeleteArgs>,
    deleteMany: z.ZodType<Prisma.AttributeSynonymGroupDeleteManyArgs>,
    update: z.ZodType<Prisma.AttributeSynonymGroupUpdateArgs>,
    updateMany: z.ZodType<Prisma.AttributeSynonymGroupUpdateManyArgs>,
    upsert: z.ZodType<Prisma.AttributeSynonymGroupUpsertArgs>,
    aggregate: z.ZodType<Prisma.AttributeSynonymGroupAggregateArgs>,
    groupBy: z.ZodType<Prisma.AttributeSynonymGroupGroupByArgs>,
    count: z.ZodType<Prisma.AttributeSynonymGroupCountArgs>
}

export const AttributeSynonymGroupInputSchema = {
    findUnique: z.object({
        select: z.lazy(() => AttributeSynonymGroupSelectObjectSchema.optional()), include: z.lazy(() => AttributeSynonymGroupIncludeObjectSchema.optional()), where: AttributeSynonymGroupWhereUniqueInputObjectSchema
    }).strict(), findFirst: z.object({
        select: z.lazy(() => AttributeSynonymGroupSelectObjectSchema.optional()), include: z.lazy(() => AttributeSynonymGroupIncludeObjectSchema.optional()), where: AttributeSynonymGroupWhereInputObjectSchema.optional(), orderBy: z.union([AttributeSynonymGroupOrderByWithRelationInputObjectSchema, AttributeSynonymGroupOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: AttributeSynonymGroupWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(AttributeSynonymGroupScalarFieldEnumSchema).optional()
    }).strict(), findMany: z.object({
        select: z.lazy(() => AttributeSynonymGroupSelectObjectSchema.optional()), include: z.lazy(() => AttributeSynonymGroupIncludeObjectSchema.optional()), where: AttributeSynonymGroupWhereInputObjectSchema.optional(), orderBy: z.union([AttributeSynonymGroupOrderByWithRelationInputObjectSchema, AttributeSynonymGroupOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: AttributeSynonymGroupWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(AttributeSynonymGroupScalarFieldEnumSchema).optional()
    }).strict(), create: z.object({
        select: z.lazy(() => AttributeSynonymGroupSelectObjectSchema.optional()), include: z.lazy(() => AttributeSynonymGroupIncludeObjectSchema.optional()), data: z.union([AttributeSynonymGroupCreateInputObjectSchema, AttributeSynonymGroupUncheckedCreateInputObjectSchema])
    }).strict(), createMany: z.object({
        data: z.union([AttributeSynonymGroupCreateManyInputObjectSchema, z.array(AttributeSynonymGroupCreateManyInputObjectSchema)]), skipDuplicates: z.boolean().optional()
    }).strict(), 'delete': z.object({
        select: z.lazy(() => AttributeSynonymGroupSelectObjectSchema.optional()), include: z.lazy(() => AttributeSynonymGroupIncludeObjectSchema.optional()), where: AttributeSynonymGroupWhereUniqueInputObjectSchema
    }).strict(), deleteMany: z.object({
        where: AttributeSynonymGroupWhereInputObjectSchema.optional()
    }).strict(), update: z.object({
        select: z.lazy(() => AttributeSynonymGroupSelectObjectSchema.optional()), include: z.lazy(() => AttributeSynonymGroupIncludeObjectSchema.optional()), data: z.union([AttributeSynonymGroupUpdateInputObjectSchema, AttributeSynonymGroupUncheckedUpdateInputObjectSchema]), where: AttributeSynonymGroupWhereUniqueInputObjectSchema
    }).strict(), updateMany: z.object({
        data: z.union([AttributeSynonymGroupUpdateManyMutationInputObjectSchema, AttributeSynonymGroupUncheckedUpdateManyInputObjectSchema]), where: AttributeSynonymGroupWhereInputObjectSchema.optional()
    }).strict(), upsert: z.object({
        select: z.lazy(() => AttributeSynonymGroupSelectObjectSchema.optional()), include: z.lazy(() => AttributeSynonymGroupIncludeObjectSchema.optional()), where: AttributeSynonymGroupWhereUniqueInputObjectSchema, create: z.union([AttributeSynonymGroupCreateInputObjectSchema, AttributeSynonymGroupUncheckedCreateInputObjectSchema]), update: z.union([AttributeSynonymGroupUpdateInputObjectSchema, AttributeSynonymGroupUncheckedUpdateInputObjectSchema])
    }).strict(), aggregate: z.object({
        where: AttributeSynonymGroupWhereInputObjectSchema.optional(), orderBy: z.union([AttributeSynonymGroupOrderByWithRelationInputObjectSchema, AttributeSynonymGroupOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: AttributeSynonymGroupWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), _count: z.union([z.literal(true), AttributeSynonymGroupCountAggregateInputObjectSchema]).optional(), _min: AttributeSynonymGroupMinAggregateInputObjectSchema.optional(), _max: AttributeSynonymGroupMaxAggregateInputObjectSchema.optional(), _avg: AttributeSynonymGroupAvgAggregateInputObjectSchema.optional(), _sum: AttributeSynonymGroupSumAggregateInputObjectSchema.optional()
    }).strict(), groupBy: z.object({
        where: AttributeSynonymGroupWhereInputObjectSchema.optional(), orderBy: z.union([AttributeSynonymGroupOrderByWithAggregationInputObjectSchema, AttributeSynonymGroupOrderByWithAggregationInputObjectSchema.array()]).optional(), having: AttributeSynonymGroupScalarWhereWithAggregatesInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), by: z.array(AttributeSynonymGroupScalarFieldEnumSchema), _count: z.union([z.literal(true), AttributeSynonymGroupCountAggregateInputObjectSchema]).optional(), _min: AttributeSynonymGroupMinAggregateInputObjectSchema.optional(), _max: AttributeSynonymGroupMaxAggregateInputObjectSchema.optional(), _avg: AttributeSynonymGroupAvgAggregateInputObjectSchema.optional(), _sum: AttributeSynonymGroupSumAggregateInputObjectSchema.optional()
    }).strict(), count: z.object({
        where: AttributeSynonymGroupWhereInputObjectSchema.optional(), orderBy: z.union([AttributeSynonymGroupOrderByWithRelationInputObjectSchema, AttributeSynonymGroupOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: AttributeSynonymGroupWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(AttributeSynonymGroupScalarFieldEnumSchema).optional(), select: z.union([z.literal(true), AttributeSynonymGroupCountAggregateInputObjectSchema]).optional()
    }).strict(),
} as AttributeSynonymGroupInputSchemaType;
