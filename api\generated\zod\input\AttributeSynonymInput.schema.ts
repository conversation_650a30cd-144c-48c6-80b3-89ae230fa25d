/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
;
import type { Prisma } from '@prisma/client';

;
import { AttributeSynonymSelectObjectSchema } from '../objects/AttributeSynonymSelect.schema';
import { AttributeSynonymIncludeObjectSchema } from '../objects/AttributeSynonymInclude.schema';
import { AttributeSynonymWhereUniqueInputObjectSchema } from '../objects/AttributeSynonymWhereUniqueInput.schema';
import { AttributeSynonymWhereInputObjectSchema } from '../objects/AttributeSynonymWhereInput.schema';
import { AttributeSynonymOrderByWithRelationInputObjectSchema } from '../objects/AttributeSynonymOrderByWithRelationInput.schema';
import { AttributeSynonymScalarFieldEnumSchema } from '../enums/AttributeSynonymScalarFieldEnum.schema';
import { AttributeSynonymCreateInputObjectSchema } from '../objects/AttributeSynonymCreateInput.schema';
import { AttributeSynonymUncheckedCreateInputObjectSchema } from '../objects/AttributeSynonymUncheckedCreateInput.schema';
import { AttributeSynonymCreateManyInputObjectSchema } from '../objects/AttributeSynonymCreateManyInput.schema';
import { AttributeSynonymUpdateInputObjectSchema } from '../objects/AttributeSynonymUpdateInput.schema';
import { AttributeSynonymUncheckedUpdateInputObjectSchema } from '../objects/AttributeSynonymUncheckedUpdateInput.schema';
import { AttributeSynonymUpdateManyMutationInputObjectSchema } from '../objects/AttributeSynonymUpdateManyMutationInput.schema';
import { AttributeSynonymUncheckedUpdateManyInputObjectSchema } from '../objects/AttributeSynonymUncheckedUpdateManyInput.schema';
import { AttributeSynonymCountAggregateInputObjectSchema } from '../objects/AttributeSynonymCountAggregateInput.schema';
import { AttributeSynonymMinAggregateInputObjectSchema } from '../objects/AttributeSynonymMinAggregateInput.schema';
import { AttributeSynonymMaxAggregateInputObjectSchema } from '../objects/AttributeSynonymMaxAggregateInput.schema';
import { AttributeSynonymAvgAggregateInputObjectSchema } from '../objects/AttributeSynonymAvgAggregateInput.schema';
import { AttributeSynonymSumAggregateInputObjectSchema } from '../objects/AttributeSynonymSumAggregateInput.schema';
import { AttributeSynonymOrderByWithAggregationInputObjectSchema } from '../objects/AttributeSynonymOrderByWithAggregationInput.schema';
import { AttributeSynonymScalarWhereWithAggregatesInputObjectSchema } from '../objects/AttributeSynonymScalarWhereWithAggregatesInput.schema'

type AttributeSynonymInputSchemaType = {
    findUnique: z.ZodType<Prisma.AttributeSynonymFindUniqueArgs>,
    findFirst: z.ZodType<Prisma.AttributeSynonymFindFirstArgs>,
    findMany: z.ZodType<Prisma.AttributeSynonymFindManyArgs>,
    create: z.ZodType<Prisma.AttributeSynonymCreateArgs>,
    createMany: z.ZodType<Prisma.AttributeSynonymCreateManyArgs>,
    delete: z.ZodType<Prisma.AttributeSynonymDeleteArgs>,
    deleteMany: z.ZodType<Prisma.AttributeSynonymDeleteManyArgs>,
    update: z.ZodType<Prisma.AttributeSynonymUpdateArgs>,
    updateMany: z.ZodType<Prisma.AttributeSynonymUpdateManyArgs>,
    upsert: z.ZodType<Prisma.AttributeSynonymUpsertArgs>,
    aggregate: z.ZodType<Prisma.AttributeSynonymAggregateArgs>,
    groupBy: z.ZodType<Prisma.AttributeSynonymGroupByArgs>,
    count: z.ZodType<Prisma.AttributeSynonymCountArgs>
}

export const AttributeSynonymInputSchema = {
    findUnique: z.object({
        select: z.lazy(() => AttributeSynonymSelectObjectSchema.optional()), include: z.lazy(() => AttributeSynonymIncludeObjectSchema.optional()), where: AttributeSynonymWhereUniqueInputObjectSchema
    }).strict(), findFirst: z.object({
        select: z.lazy(() => AttributeSynonymSelectObjectSchema.optional()), include: z.lazy(() => AttributeSynonymIncludeObjectSchema.optional()), where: AttributeSynonymWhereInputObjectSchema.optional(), orderBy: z.union([AttributeSynonymOrderByWithRelationInputObjectSchema, AttributeSynonymOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: AttributeSynonymWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(AttributeSynonymScalarFieldEnumSchema).optional()
    }).strict(), findMany: z.object({
        select: z.lazy(() => AttributeSynonymSelectObjectSchema.optional()), include: z.lazy(() => AttributeSynonymIncludeObjectSchema.optional()), where: AttributeSynonymWhereInputObjectSchema.optional(), orderBy: z.union([AttributeSynonymOrderByWithRelationInputObjectSchema, AttributeSynonymOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: AttributeSynonymWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(AttributeSynonymScalarFieldEnumSchema).optional()
    }).strict(), create: z.object({
        select: z.lazy(() => AttributeSynonymSelectObjectSchema.optional()), include: z.lazy(() => AttributeSynonymIncludeObjectSchema.optional()), data: z.union([AttributeSynonymCreateInputObjectSchema, AttributeSynonymUncheckedCreateInputObjectSchema])
    }).strict(), createMany: z.object({
        data: z.union([AttributeSynonymCreateManyInputObjectSchema, z.array(AttributeSynonymCreateManyInputObjectSchema)]), skipDuplicates: z.boolean().optional()
    }).strict(), 'delete': z.object({
        select: z.lazy(() => AttributeSynonymSelectObjectSchema.optional()), include: z.lazy(() => AttributeSynonymIncludeObjectSchema.optional()), where: AttributeSynonymWhereUniqueInputObjectSchema
    }).strict(), deleteMany: z.object({
        where: AttributeSynonymWhereInputObjectSchema.optional()
    }).strict(), update: z.object({
        select: z.lazy(() => AttributeSynonymSelectObjectSchema.optional()), include: z.lazy(() => AttributeSynonymIncludeObjectSchema.optional()), data: z.union([AttributeSynonymUpdateInputObjectSchema, AttributeSynonymUncheckedUpdateInputObjectSchema]), where: AttributeSynonymWhereUniqueInputObjectSchema
    }).strict(), updateMany: z.object({
        data: z.union([AttributeSynonymUpdateManyMutationInputObjectSchema, AttributeSynonymUncheckedUpdateManyInputObjectSchema]), where: AttributeSynonymWhereInputObjectSchema.optional()
    }).strict(), upsert: z.object({
        select: z.lazy(() => AttributeSynonymSelectObjectSchema.optional()), include: z.lazy(() => AttributeSynonymIncludeObjectSchema.optional()), where: AttributeSynonymWhereUniqueInputObjectSchema, create: z.union([AttributeSynonymCreateInputObjectSchema, AttributeSynonymUncheckedCreateInputObjectSchema]), update: z.union([AttributeSynonymUpdateInputObjectSchema, AttributeSynonymUncheckedUpdateInputObjectSchema])
    }).strict(), aggregate: z.object({
        where: AttributeSynonymWhereInputObjectSchema.optional(), orderBy: z.union([AttributeSynonymOrderByWithRelationInputObjectSchema, AttributeSynonymOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: AttributeSynonymWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), _count: z.union([z.literal(true), AttributeSynonymCountAggregateInputObjectSchema]).optional(), _min: AttributeSynonymMinAggregateInputObjectSchema.optional(), _max: AttributeSynonymMaxAggregateInputObjectSchema.optional(), _avg: AttributeSynonymAvgAggregateInputObjectSchema.optional(), _sum: AttributeSynonymSumAggregateInputObjectSchema.optional()
    }).strict(), groupBy: z.object({
        where: AttributeSynonymWhereInputObjectSchema.optional(), orderBy: z.union([AttributeSynonymOrderByWithAggregationInputObjectSchema, AttributeSynonymOrderByWithAggregationInputObjectSchema.array()]).optional(), having: AttributeSynonymScalarWhereWithAggregatesInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), by: z.array(AttributeSynonymScalarFieldEnumSchema), _count: z.union([z.literal(true), AttributeSynonymCountAggregateInputObjectSchema]).optional(), _min: AttributeSynonymMinAggregateInputObjectSchema.optional(), _max: AttributeSynonymMaxAggregateInputObjectSchema.optional(), _avg: AttributeSynonymAvgAggregateInputObjectSchema.optional(), _sum: AttributeSynonymSumAggregateInputObjectSchema.optional()
    }).strict(), count: z.object({
        where: AttributeSynonymWhereInputObjectSchema.optional(), orderBy: z.union([AttributeSynonymOrderByWithRelationInputObjectSchema, AttributeSynonymOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: AttributeSynonymWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(AttributeSynonymScalarFieldEnumSchema).optional(), select: z.union([z.literal(true), AttributeSynonymCountAggregateInputObjectSchema]).optional()
    }).strict(),
} as AttributeSynonymInputSchemaType;
