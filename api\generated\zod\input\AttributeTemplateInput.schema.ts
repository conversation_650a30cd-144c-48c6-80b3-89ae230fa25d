/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
;
import type { Prisma } from '@prisma/client';

;
import { AttributeTemplateSelectObjectSchema } from '../objects/AttributeTemplateSelect.schema';
import { AttributeTemplateIncludeObjectSchema } from '../objects/AttributeTemplateInclude.schema';
import { AttributeTemplateWhereUniqueInputObjectSchema } from '../objects/AttributeTemplateWhereUniqueInput.schema';
import { AttributeTemplateWhereInputObjectSchema } from '../objects/AttributeTemplateWhereInput.schema';
import { AttributeTemplateOrderByWithRelationInputObjectSchema } from '../objects/AttributeTemplateOrderByWithRelationInput.schema';
import { AttributeTemplateScalarFieldEnumSchema } from '../enums/AttributeTemplateScalarFieldEnum.schema';
import { AttributeTemplateCreateInputObjectSchema } from '../objects/AttributeTemplateCreateInput.schema';
import { AttributeTemplateUncheckedCreateInputObjectSchema } from '../objects/AttributeTemplateUncheckedCreateInput.schema';
import { AttributeTemplateCreateManyInputObjectSchema } from '../objects/AttributeTemplateCreateManyInput.schema';
import { AttributeTemplateUpdateInputObjectSchema } from '../objects/AttributeTemplateUpdateInput.schema';
import { AttributeTemplateUncheckedUpdateInputObjectSchema } from '../objects/AttributeTemplateUncheckedUpdateInput.schema';
import { AttributeTemplateUpdateManyMutationInputObjectSchema } from '../objects/AttributeTemplateUpdateManyMutationInput.schema';
import { AttributeTemplateUncheckedUpdateManyInputObjectSchema } from '../objects/AttributeTemplateUncheckedUpdateManyInput.schema';
import { AttributeTemplateCountAggregateInputObjectSchema } from '../objects/AttributeTemplateCountAggregateInput.schema';
import { AttributeTemplateMinAggregateInputObjectSchema } from '../objects/AttributeTemplateMinAggregateInput.schema';
import { AttributeTemplateMaxAggregateInputObjectSchema } from '../objects/AttributeTemplateMaxAggregateInput.schema';
import { AttributeTemplateAvgAggregateInputObjectSchema } from '../objects/AttributeTemplateAvgAggregateInput.schema';
import { AttributeTemplateSumAggregateInputObjectSchema } from '../objects/AttributeTemplateSumAggregateInput.schema';
import { AttributeTemplateOrderByWithAggregationInputObjectSchema } from '../objects/AttributeTemplateOrderByWithAggregationInput.schema';
import { AttributeTemplateScalarWhereWithAggregatesInputObjectSchema } from '../objects/AttributeTemplateScalarWhereWithAggregatesInput.schema'

type AttributeTemplateInputSchemaType = {
    findUnique: z.ZodType<Prisma.AttributeTemplateFindUniqueArgs>,
    findFirst: z.ZodType<Prisma.AttributeTemplateFindFirstArgs>,
    findMany: z.ZodType<Prisma.AttributeTemplateFindManyArgs>,
    create: z.ZodType<Prisma.AttributeTemplateCreateArgs>,
    createMany: z.ZodType<Prisma.AttributeTemplateCreateManyArgs>,
    delete: z.ZodType<Prisma.AttributeTemplateDeleteArgs>,
    deleteMany: z.ZodType<Prisma.AttributeTemplateDeleteManyArgs>,
    update: z.ZodType<Prisma.AttributeTemplateUpdateArgs>,
    updateMany: z.ZodType<Prisma.AttributeTemplateUpdateManyArgs>,
    upsert: z.ZodType<Prisma.AttributeTemplateUpsertArgs>,
    aggregate: z.ZodType<Prisma.AttributeTemplateAggregateArgs>,
    groupBy: z.ZodType<Prisma.AttributeTemplateGroupByArgs>,
    count: z.ZodType<Prisma.AttributeTemplateCountArgs>
}

export const AttributeTemplateInputSchema = {
    findUnique: z.object({
        select: z.lazy(() => AttributeTemplateSelectObjectSchema.optional()), include: z.lazy(() => AttributeTemplateIncludeObjectSchema.optional()), where: AttributeTemplateWhereUniqueInputObjectSchema
    }).strict(), findFirst: z.object({
        select: z.lazy(() => AttributeTemplateSelectObjectSchema.optional()), include: z.lazy(() => AttributeTemplateIncludeObjectSchema.optional()), where: AttributeTemplateWhereInputObjectSchema.optional(), orderBy: z.union([AttributeTemplateOrderByWithRelationInputObjectSchema, AttributeTemplateOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: AttributeTemplateWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(AttributeTemplateScalarFieldEnumSchema).optional()
    }).strict(), findMany: z.object({
        select: z.lazy(() => AttributeTemplateSelectObjectSchema.optional()), include: z.lazy(() => AttributeTemplateIncludeObjectSchema.optional()), where: AttributeTemplateWhereInputObjectSchema.optional(), orderBy: z.union([AttributeTemplateOrderByWithRelationInputObjectSchema, AttributeTemplateOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: AttributeTemplateWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(AttributeTemplateScalarFieldEnumSchema).optional()
    }).strict(), create: z.object({
        select: z.lazy(() => AttributeTemplateSelectObjectSchema.optional()), include: z.lazy(() => AttributeTemplateIncludeObjectSchema.optional()), data: z.union([AttributeTemplateCreateInputObjectSchema, AttributeTemplateUncheckedCreateInputObjectSchema])
    }).strict(), createMany: z.object({
        data: z.union([AttributeTemplateCreateManyInputObjectSchema, z.array(AttributeTemplateCreateManyInputObjectSchema)]), skipDuplicates: z.boolean().optional()
    }).strict(), 'delete': z.object({
        select: z.lazy(() => AttributeTemplateSelectObjectSchema.optional()), include: z.lazy(() => AttributeTemplateIncludeObjectSchema.optional()), where: AttributeTemplateWhereUniqueInputObjectSchema
    }).strict(), deleteMany: z.object({
        where: AttributeTemplateWhereInputObjectSchema.optional()
    }).strict(), update: z.object({
        select: z.lazy(() => AttributeTemplateSelectObjectSchema.optional()), include: z.lazy(() => AttributeTemplateIncludeObjectSchema.optional()), data: z.union([AttributeTemplateUpdateInputObjectSchema, AttributeTemplateUncheckedUpdateInputObjectSchema]), where: AttributeTemplateWhereUniqueInputObjectSchema
    }).strict(), updateMany: z.object({
        data: z.union([AttributeTemplateUpdateManyMutationInputObjectSchema, AttributeTemplateUncheckedUpdateManyInputObjectSchema]), where: AttributeTemplateWhereInputObjectSchema.optional()
    }).strict(), upsert: z.object({
        select: z.lazy(() => AttributeTemplateSelectObjectSchema.optional()), include: z.lazy(() => AttributeTemplateIncludeObjectSchema.optional()), where: AttributeTemplateWhereUniqueInputObjectSchema, create: z.union([AttributeTemplateCreateInputObjectSchema, AttributeTemplateUncheckedCreateInputObjectSchema]), update: z.union([AttributeTemplateUpdateInputObjectSchema, AttributeTemplateUncheckedUpdateInputObjectSchema])
    }).strict(), aggregate: z.object({
        where: AttributeTemplateWhereInputObjectSchema.optional(), orderBy: z.union([AttributeTemplateOrderByWithRelationInputObjectSchema, AttributeTemplateOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: AttributeTemplateWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), _count: z.union([z.literal(true), AttributeTemplateCountAggregateInputObjectSchema]).optional(), _min: AttributeTemplateMinAggregateInputObjectSchema.optional(), _max: AttributeTemplateMaxAggregateInputObjectSchema.optional(), _avg: AttributeTemplateAvgAggregateInputObjectSchema.optional(), _sum: AttributeTemplateSumAggregateInputObjectSchema.optional()
    }).strict(), groupBy: z.object({
        where: AttributeTemplateWhereInputObjectSchema.optional(), orderBy: z.union([AttributeTemplateOrderByWithAggregationInputObjectSchema, AttributeTemplateOrderByWithAggregationInputObjectSchema.array()]).optional(), having: AttributeTemplateScalarWhereWithAggregatesInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), by: z.array(AttributeTemplateScalarFieldEnumSchema), _count: z.union([z.literal(true), AttributeTemplateCountAggregateInputObjectSchema]).optional(), _min: AttributeTemplateMinAggregateInputObjectSchema.optional(), _max: AttributeTemplateMaxAggregateInputObjectSchema.optional(), _avg: AttributeTemplateAvgAggregateInputObjectSchema.optional(), _sum: AttributeTemplateSumAggregateInputObjectSchema.optional()
    }).strict(), count: z.object({
        where: AttributeTemplateWhereInputObjectSchema.optional(), orderBy: z.union([AttributeTemplateOrderByWithRelationInputObjectSchema, AttributeTemplateOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: AttributeTemplateWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(AttributeTemplateScalarFieldEnumSchema).optional(), select: z.union([z.literal(true), AttributeTemplateCountAggregateInputObjectSchema]).optional()
    }).strict(),
} as AttributeTemplateInputSchemaType;
