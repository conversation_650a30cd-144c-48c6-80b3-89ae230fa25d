/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
;
import type { Prisma } from '@prisma/client';

;
import { BrandSelectObjectSchema } from '../objects/BrandSelect.schema';
import { BrandIncludeObjectSchema } from '../objects/BrandInclude.schema';
import { BrandWhereUniqueInputObjectSchema } from '../objects/BrandWhereUniqueInput.schema';
import { BrandWhereInputObjectSchema } from '../objects/BrandWhereInput.schema';
import { BrandOrderByWithRelationInputObjectSchema } from '../objects/BrandOrderByWithRelationInput.schema';
import { BrandScalarFieldEnumSchema } from '../enums/BrandScalarFieldEnum.schema';
import { BrandCreateInputObjectSchema } from '../objects/BrandCreateInput.schema';
import { BrandUncheckedCreateInputObjectSchema } from '../objects/BrandUncheckedCreateInput.schema';
import { BrandCreateManyInputObjectSchema } from '../objects/BrandCreateManyInput.schema';
import { BrandUpdateInputObjectSchema } from '../objects/BrandUpdateInput.schema';
import { BrandUncheckedUpdateInputObjectSchema } from '../objects/BrandUncheckedUpdateInput.schema';
import { BrandUpdateManyMutationInputObjectSchema } from '../objects/BrandUpdateManyMutationInput.schema';
import { BrandUncheckedUpdateManyInputObjectSchema } from '../objects/BrandUncheckedUpdateManyInput.schema';
import { BrandCountAggregateInputObjectSchema } from '../objects/BrandCountAggregateInput.schema';
import { BrandMinAggregateInputObjectSchema } from '../objects/BrandMinAggregateInput.schema';
import { BrandMaxAggregateInputObjectSchema } from '../objects/BrandMaxAggregateInput.schema';
import { BrandAvgAggregateInputObjectSchema } from '../objects/BrandAvgAggregateInput.schema';
import { BrandSumAggregateInputObjectSchema } from '../objects/BrandSumAggregateInput.schema';
import { BrandOrderByWithAggregationInputObjectSchema } from '../objects/BrandOrderByWithAggregationInput.schema';
import { BrandScalarWhereWithAggregatesInputObjectSchema } from '../objects/BrandScalarWhereWithAggregatesInput.schema'

type BrandInputSchemaType = {
    findUnique: z.ZodType<Prisma.BrandFindUniqueArgs>,
    findFirst: z.ZodType<Prisma.BrandFindFirstArgs>,
    findMany: z.ZodType<Prisma.BrandFindManyArgs>,
    create: z.ZodType<Prisma.BrandCreateArgs>,
    createMany: z.ZodType<Prisma.BrandCreateManyArgs>,
    delete: z.ZodType<Prisma.BrandDeleteArgs>,
    deleteMany: z.ZodType<Prisma.BrandDeleteManyArgs>,
    update: z.ZodType<Prisma.BrandUpdateArgs>,
    updateMany: z.ZodType<Prisma.BrandUpdateManyArgs>,
    upsert: z.ZodType<Prisma.BrandUpsertArgs>,
    aggregate: z.ZodType<Prisma.BrandAggregateArgs>,
    groupBy: z.ZodType<Prisma.BrandGroupByArgs>,
    count: z.ZodType<Prisma.BrandCountArgs>
}

export const BrandInputSchema = {
    findUnique: z.object({
        select: z.lazy(() => BrandSelectObjectSchema.optional()), include: z.lazy(() => BrandIncludeObjectSchema.optional()), where: BrandWhereUniqueInputObjectSchema
    }).strict(), findFirst: z.object({
        select: z.lazy(() => BrandSelectObjectSchema.optional()), include: z.lazy(() => BrandIncludeObjectSchema.optional()), where: BrandWhereInputObjectSchema.optional(), orderBy: z.union([BrandOrderByWithRelationInputObjectSchema, BrandOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: BrandWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(BrandScalarFieldEnumSchema).optional()
    }).strict(), findMany: z.object({
        select: z.lazy(() => BrandSelectObjectSchema.optional()), include: z.lazy(() => BrandIncludeObjectSchema.optional()), where: BrandWhereInputObjectSchema.optional(), orderBy: z.union([BrandOrderByWithRelationInputObjectSchema, BrandOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: BrandWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(BrandScalarFieldEnumSchema).optional()
    }).strict(), create: z.object({
        select: z.lazy(() => BrandSelectObjectSchema.optional()), include: z.lazy(() => BrandIncludeObjectSchema.optional()), data: z.union([BrandCreateInputObjectSchema, BrandUncheckedCreateInputObjectSchema])
    }).strict(), createMany: z.object({
        data: z.union([BrandCreateManyInputObjectSchema, z.array(BrandCreateManyInputObjectSchema)]), skipDuplicates: z.boolean().optional()
    }).strict(), 'delete': z.object({
        select: z.lazy(() => BrandSelectObjectSchema.optional()), include: z.lazy(() => BrandIncludeObjectSchema.optional()), where: BrandWhereUniqueInputObjectSchema
    }).strict(), deleteMany: z.object({
        where: BrandWhereInputObjectSchema.optional()
    }).strict(), update: z.object({
        select: z.lazy(() => BrandSelectObjectSchema.optional()), include: z.lazy(() => BrandIncludeObjectSchema.optional()), data: z.union([BrandUpdateInputObjectSchema, BrandUncheckedUpdateInputObjectSchema]), where: BrandWhereUniqueInputObjectSchema
    }).strict(), updateMany: z.object({
        data: z.union([BrandUpdateManyMutationInputObjectSchema, BrandUncheckedUpdateManyInputObjectSchema]), where: BrandWhereInputObjectSchema.optional()
    }).strict(), upsert: z.object({
        select: z.lazy(() => BrandSelectObjectSchema.optional()), include: z.lazy(() => BrandIncludeObjectSchema.optional()), where: BrandWhereUniqueInputObjectSchema, create: z.union([BrandCreateInputObjectSchema, BrandUncheckedCreateInputObjectSchema]), update: z.union([BrandUpdateInputObjectSchema, BrandUncheckedUpdateInputObjectSchema])
    }).strict(), aggregate: z.object({
        where: BrandWhereInputObjectSchema.optional(), orderBy: z.union([BrandOrderByWithRelationInputObjectSchema, BrandOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: BrandWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), _count: z.union([z.literal(true), BrandCountAggregateInputObjectSchema]).optional(), _min: BrandMinAggregateInputObjectSchema.optional(), _max: BrandMaxAggregateInputObjectSchema.optional(), _avg: BrandAvgAggregateInputObjectSchema.optional(), _sum: BrandSumAggregateInputObjectSchema.optional()
    }).strict(), groupBy: z.object({
        where: BrandWhereInputObjectSchema.optional(), orderBy: z.union([BrandOrderByWithAggregationInputObjectSchema, BrandOrderByWithAggregationInputObjectSchema.array()]).optional(), having: BrandScalarWhereWithAggregatesInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), by: z.array(BrandScalarFieldEnumSchema), _count: z.union([z.literal(true), BrandCountAggregateInputObjectSchema]).optional(), _min: BrandMinAggregateInputObjectSchema.optional(), _max: BrandMaxAggregateInputObjectSchema.optional(), _avg: BrandAvgAggregateInputObjectSchema.optional(), _sum: BrandSumAggregateInputObjectSchema.optional()
    }).strict(), count: z.object({
        where: BrandWhereInputObjectSchema.optional(), orderBy: z.union([BrandOrderByWithRelationInputObjectSchema, BrandOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: BrandWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(BrandScalarFieldEnumSchema).optional(), select: z.union([z.literal(true), BrandCountAggregateInputObjectSchema]).optional()
    }).strict(),
} as BrandInputSchemaType;
