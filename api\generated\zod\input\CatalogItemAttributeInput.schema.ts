/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
;
import type { Prisma } from '@prisma/client';

;
import { CatalogItemAttributeSelectObjectSchema } from '../objects/CatalogItemAttributeSelect.schema';
import { CatalogItemAttributeIncludeObjectSchema } from '../objects/CatalogItemAttributeInclude.schema';
import { CatalogItemAttributeWhereUniqueInputObjectSchema } from '../objects/CatalogItemAttributeWhereUniqueInput.schema';
import { CatalogItemAttributeWhereInputObjectSchema } from '../objects/CatalogItemAttributeWhereInput.schema';
import { CatalogItemAttributeOrderByWithRelationInputObjectSchema } from '../objects/CatalogItemAttributeOrderByWithRelationInput.schema';
import { CatalogItemAttributeScalarFieldEnumSchema } from '../enums/CatalogItemAttributeScalarFieldEnum.schema';
import { CatalogItemAttributeCreateInputObjectSchema } from '../objects/CatalogItemAttributeCreateInput.schema';
import { CatalogItemAttributeUncheckedCreateInputObjectSchema } from '../objects/CatalogItemAttributeUncheckedCreateInput.schema';
import { CatalogItemAttributeCreateManyInputObjectSchema } from '../objects/CatalogItemAttributeCreateManyInput.schema';
import { CatalogItemAttributeUpdateInputObjectSchema } from '../objects/CatalogItemAttributeUpdateInput.schema';
import { CatalogItemAttributeUncheckedUpdateInputObjectSchema } from '../objects/CatalogItemAttributeUncheckedUpdateInput.schema';
import { CatalogItemAttributeUpdateManyMutationInputObjectSchema } from '../objects/CatalogItemAttributeUpdateManyMutationInput.schema';
import { CatalogItemAttributeUncheckedUpdateManyInputObjectSchema } from '../objects/CatalogItemAttributeUncheckedUpdateManyInput.schema';
import { CatalogItemAttributeCountAggregateInputObjectSchema } from '../objects/CatalogItemAttributeCountAggregateInput.schema';
import { CatalogItemAttributeMinAggregateInputObjectSchema } from '../objects/CatalogItemAttributeMinAggregateInput.schema';
import { CatalogItemAttributeMaxAggregateInputObjectSchema } from '../objects/CatalogItemAttributeMaxAggregateInput.schema';
import { CatalogItemAttributeAvgAggregateInputObjectSchema } from '../objects/CatalogItemAttributeAvgAggregateInput.schema';
import { CatalogItemAttributeSumAggregateInputObjectSchema } from '../objects/CatalogItemAttributeSumAggregateInput.schema';
import { CatalogItemAttributeOrderByWithAggregationInputObjectSchema } from '../objects/CatalogItemAttributeOrderByWithAggregationInput.schema';
import { CatalogItemAttributeScalarWhereWithAggregatesInputObjectSchema } from '../objects/CatalogItemAttributeScalarWhereWithAggregatesInput.schema'

type CatalogItemAttributeInputSchemaType = {
    findUnique: z.ZodType<Prisma.CatalogItemAttributeFindUniqueArgs>,
    findFirst: z.ZodType<Prisma.CatalogItemAttributeFindFirstArgs>,
    findMany: z.ZodType<Prisma.CatalogItemAttributeFindManyArgs>,
    create: z.ZodType<Prisma.CatalogItemAttributeCreateArgs>,
    createMany: z.ZodType<Prisma.CatalogItemAttributeCreateManyArgs>,
    delete: z.ZodType<Prisma.CatalogItemAttributeDeleteArgs>,
    deleteMany: z.ZodType<Prisma.CatalogItemAttributeDeleteManyArgs>,
    update: z.ZodType<Prisma.CatalogItemAttributeUpdateArgs>,
    updateMany: z.ZodType<Prisma.CatalogItemAttributeUpdateManyArgs>,
    upsert: z.ZodType<Prisma.CatalogItemAttributeUpsertArgs>,
    aggregate: z.ZodType<Prisma.CatalogItemAttributeAggregateArgs>,
    groupBy: z.ZodType<Prisma.CatalogItemAttributeGroupByArgs>,
    count: z.ZodType<Prisma.CatalogItemAttributeCountArgs>
}

export const CatalogItemAttributeInputSchema = {
    findUnique: z.object({
        select: z.lazy(() => CatalogItemAttributeSelectObjectSchema.optional()), include: z.lazy(() => CatalogItemAttributeIncludeObjectSchema.optional()), where: CatalogItemAttributeWhereUniqueInputObjectSchema
    }).strict(), findFirst: z.object({
        select: z.lazy(() => CatalogItemAttributeSelectObjectSchema.optional()), include: z.lazy(() => CatalogItemAttributeIncludeObjectSchema.optional()), where: CatalogItemAttributeWhereInputObjectSchema.optional(), orderBy: z.union([CatalogItemAttributeOrderByWithRelationInputObjectSchema, CatalogItemAttributeOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: CatalogItemAttributeWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(CatalogItemAttributeScalarFieldEnumSchema).optional()
    }).strict(), findMany: z.object({
        select: z.lazy(() => CatalogItemAttributeSelectObjectSchema.optional()), include: z.lazy(() => CatalogItemAttributeIncludeObjectSchema.optional()), where: CatalogItemAttributeWhereInputObjectSchema.optional(), orderBy: z.union([CatalogItemAttributeOrderByWithRelationInputObjectSchema, CatalogItemAttributeOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: CatalogItemAttributeWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(CatalogItemAttributeScalarFieldEnumSchema).optional()
    }).strict(), create: z.object({
        select: z.lazy(() => CatalogItemAttributeSelectObjectSchema.optional()), include: z.lazy(() => CatalogItemAttributeIncludeObjectSchema.optional()), data: z.union([CatalogItemAttributeCreateInputObjectSchema, CatalogItemAttributeUncheckedCreateInputObjectSchema])
    }).strict(), createMany: z.object({
        data: z.union([CatalogItemAttributeCreateManyInputObjectSchema, z.array(CatalogItemAttributeCreateManyInputObjectSchema)]), skipDuplicates: z.boolean().optional()
    }).strict(), 'delete': z.object({
        select: z.lazy(() => CatalogItemAttributeSelectObjectSchema.optional()), include: z.lazy(() => CatalogItemAttributeIncludeObjectSchema.optional()), where: CatalogItemAttributeWhereUniqueInputObjectSchema
    }).strict(), deleteMany: z.object({
        where: CatalogItemAttributeWhereInputObjectSchema.optional()
    }).strict(), update: z.object({
        select: z.lazy(() => CatalogItemAttributeSelectObjectSchema.optional()), include: z.lazy(() => CatalogItemAttributeIncludeObjectSchema.optional()), data: z.union([CatalogItemAttributeUpdateInputObjectSchema, CatalogItemAttributeUncheckedUpdateInputObjectSchema]), where: CatalogItemAttributeWhereUniqueInputObjectSchema
    }).strict(), updateMany: z.object({
        data: z.union([CatalogItemAttributeUpdateManyMutationInputObjectSchema, CatalogItemAttributeUncheckedUpdateManyInputObjectSchema]), where: CatalogItemAttributeWhereInputObjectSchema.optional()
    }).strict(), upsert: z.object({
        select: z.lazy(() => CatalogItemAttributeSelectObjectSchema.optional()), include: z.lazy(() => CatalogItemAttributeIncludeObjectSchema.optional()), where: CatalogItemAttributeWhereUniqueInputObjectSchema, create: z.union([CatalogItemAttributeCreateInputObjectSchema, CatalogItemAttributeUncheckedCreateInputObjectSchema]), update: z.union([CatalogItemAttributeUpdateInputObjectSchema, CatalogItemAttributeUncheckedUpdateInputObjectSchema])
    }).strict(), aggregate: z.object({
        where: CatalogItemAttributeWhereInputObjectSchema.optional(), orderBy: z.union([CatalogItemAttributeOrderByWithRelationInputObjectSchema, CatalogItemAttributeOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: CatalogItemAttributeWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), _count: z.union([z.literal(true), CatalogItemAttributeCountAggregateInputObjectSchema]).optional(), _min: CatalogItemAttributeMinAggregateInputObjectSchema.optional(), _max: CatalogItemAttributeMaxAggregateInputObjectSchema.optional(), _avg: CatalogItemAttributeAvgAggregateInputObjectSchema.optional(), _sum: CatalogItemAttributeSumAggregateInputObjectSchema.optional()
    }).strict(), groupBy: z.object({
        where: CatalogItemAttributeWhereInputObjectSchema.optional(), orderBy: z.union([CatalogItemAttributeOrderByWithAggregationInputObjectSchema, CatalogItemAttributeOrderByWithAggregationInputObjectSchema.array()]).optional(), having: CatalogItemAttributeScalarWhereWithAggregatesInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), by: z.array(CatalogItemAttributeScalarFieldEnumSchema), _count: z.union([z.literal(true), CatalogItemAttributeCountAggregateInputObjectSchema]).optional(), _min: CatalogItemAttributeMinAggregateInputObjectSchema.optional(), _max: CatalogItemAttributeMaxAggregateInputObjectSchema.optional(), _avg: CatalogItemAttributeAvgAggregateInputObjectSchema.optional(), _sum: CatalogItemAttributeSumAggregateInputObjectSchema.optional()
    }).strict(), count: z.object({
        where: CatalogItemAttributeWhereInputObjectSchema.optional(), orderBy: z.union([CatalogItemAttributeOrderByWithRelationInputObjectSchema, CatalogItemAttributeOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: CatalogItemAttributeWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(CatalogItemAttributeScalarFieldEnumSchema).optional(), select: z.union([z.literal(true), CatalogItemAttributeCountAggregateInputObjectSchema]).optional()
    }).strict(),
} as CatalogItemAttributeInputSchemaType;
