/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
;
import type { Prisma } from '@prisma/client';

;
import { CatalogItemSelectObjectSchema } from '../objects/CatalogItemSelect.schema';
import { CatalogItemIncludeObjectSchema } from '../objects/CatalogItemInclude.schema';
import { CatalogItemWhereUniqueInputObjectSchema } from '../objects/CatalogItemWhereUniqueInput.schema';
import { CatalogItemWhereInputObjectSchema } from '../objects/CatalogItemWhereInput.schema';
import { CatalogItemOrderByWithRelationInputObjectSchema } from '../objects/CatalogItemOrderByWithRelationInput.schema';
import { CatalogItemScalarFieldEnumSchema } from '../enums/CatalogItemScalarFieldEnum.schema';
import { CatalogItemCreateInputObjectSchema } from '../objects/CatalogItemCreateInput.schema';
import { CatalogItemUncheckedCreateInputObjectSchema } from '../objects/CatalogItemUncheckedCreateInput.schema';
import { CatalogItemCreateManyInputObjectSchema } from '../objects/CatalogItemCreateManyInput.schema';
import { CatalogItemUpdateInputObjectSchema } from '../objects/CatalogItemUpdateInput.schema';
import { CatalogItemUncheckedUpdateInputObjectSchema } from '../objects/CatalogItemUncheckedUpdateInput.schema';
import { CatalogItemUpdateManyMutationInputObjectSchema } from '../objects/CatalogItemUpdateManyMutationInput.schema';
import { CatalogItemUncheckedUpdateManyInputObjectSchema } from '../objects/CatalogItemUncheckedUpdateManyInput.schema';
import { CatalogItemCountAggregateInputObjectSchema } from '../objects/CatalogItemCountAggregateInput.schema';
import { CatalogItemMinAggregateInputObjectSchema } from '../objects/CatalogItemMinAggregateInput.schema';
import { CatalogItemMaxAggregateInputObjectSchema } from '../objects/CatalogItemMaxAggregateInput.schema';
import { CatalogItemAvgAggregateInputObjectSchema } from '../objects/CatalogItemAvgAggregateInput.schema';
import { CatalogItemSumAggregateInputObjectSchema } from '../objects/CatalogItemSumAggregateInput.schema';
import { CatalogItemOrderByWithAggregationInputObjectSchema } from '../objects/CatalogItemOrderByWithAggregationInput.schema';
import { CatalogItemScalarWhereWithAggregatesInputObjectSchema } from '../objects/CatalogItemScalarWhereWithAggregatesInput.schema'

type CatalogItemInputSchemaType = {
    findUnique: z.ZodType<Prisma.CatalogItemFindUniqueArgs>,
    findFirst: z.ZodType<Prisma.CatalogItemFindFirstArgs>,
    findMany: z.ZodType<Prisma.CatalogItemFindManyArgs>,
    create: z.ZodType<Prisma.CatalogItemCreateArgs>,
    createMany: z.ZodType<Prisma.CatalogItemCreateManyArgs>,
    delete: z.ZodType<Prisma.CatalogItemDeleteArgs>,
    deleteMany: z.ZodType<Prisma.CatalogItemDeleteManyArgs>,
    update: z.ZodType<Prisma.CatalogItemUpdateArgs>,
    updateMany: z.ZodType<Prisma.CatalogItemUpdateManyArgs>,
    upsert: z.ZodType<Prisma.CatalogItemUpsertArgs>,
    aggregate: z.ZodType<Prisma.CatalogItemAggregateArgs>,
    groupBy: z.ZodType<Prisma.CatalogItemGroupByArgs>,
    count: z.ZodType<Prisma.CatalogItemCountArgs>
}

export const CatalogItemInputSchema = {
    findUnique: z.object({
        select: z.lazy(() => CatalogItemSelectObjectSchema.optional()), include: z.lazy(() => CatalogItemIncludeObjectSchema.optional()), where: CatalogItemWhereUniqueInputObjectSchema
    }).strict(), findFirst: z.object({
        select: z.lazy(() => CatalogItemSelectObjectSchema.optional()), include: z.lazy(() => CatalogItemIncludeObjectSchema.optional()), where: CatalogItemWhereInputObjectSchema.optional(), orderBy: z.union([CatalogItemOrderByWithRelationInputObjectSchema, CatalogItemOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: CatalogItemWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(CatalogItemScalarFieldEnumSchema).optional()
    }).strict(), findMany: z.object({
        select: z.lazy(() => CatalogItemSelectObjectSchema.optional()), include: z.lazy(() => CatalogItemIncludeObjectSchema.optional()), where: CatalogItemWhereInputObjectSchema.optional(), orderBy: z.union([CatalogItemOrderByWithRelationInputObjectSchema, CatalogItemOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: CatalogItemWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(CatalogItemScalarFieldEnumSchema).optional()
    }).strict(), create: z.object({
        select: z.lazy(() => CatalogItemSelectObjectSchema.optional()), include: z.lazy(() => CatalogItemIncludeObjectSchema.optional()), data: z.union([CatalogItemCreateInputObjectSchema, CatalogItemUncheckedCreateInputObjectSchema])
    }).strict(), createMany: z.object({
        data: z.union([CatalogItemCreateManyInputObjectSchema, z.array(CatalogItemCreateManyInputObjectSchema)]), skipDuplicates: z.boolean().optional()
    }).strict(), 'delete': z.object({
        select: z.lazy(() => CatalogItemSelectObjectSchema.optional()), include: z.lazy(() => CatalogItemIncludeObjectSchema.optional()), where: CatalogItemWhereUniqueInputObjectSchema
    }).strict(), deleteMany: z.object({
        where: CatalogItemWhereInputObjectSchema.optional()
    }).strict(), update: z.object({
        select: z.lazy(() => CatalogItemSelectObjectSchema.optional()), include: z.lazy(() => CatalogItemIncludeObjectSchema.optional()), data: z.union([CatalogItemUpdateInputObjectSchema, CatalogItemUncheckedUpdateInputObjectSchema]), where: CatalogItemWhereUniqueInputObjectSchema
    }).strict(), updateMany: z.object({
        data: z.union([CatalogItemUpdateManyMutationInputObjectSchema, CatalogItemUncheckedUpdateManyInputObjectSchema]), where: CatalogItemWhereInputObjectSchema.optional()
    }).strict(), upsert: z.object({
        select: z.lazy(() => CatalogItemSelectObjectSchema.optional()), include: z.lazy(() => CatalogItemIncludeObjectSchema.optional()), where: CatalogItemWhereUniqueInputObjectSchema, create: z.union([CatalogItemCreateInputObjectSchema, CatalogItemUncheckedCreateInputObjectSchema]), update: z.union([CatalogItemUpdateInputObjectSchema, CatalogItemUncheckedUpdateInputObjectSchema])
    }).strict(), aggregate: z.object({
        where: CatalogItemWhereInputObjectSchema.optional(), orderBy: z.union([CatalogItemOrderByWithRelationInputObjectSchema, CatalogItemOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: CatalogItemWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), _count: z.union([z.literal(true), CatalogItemCountAggregateInputObjectSchema]).optional(), _min: CatalogItemMinAggregateInputObjectSchema.optional(), _max: CatalogItemMaxAggregateInputObjectSchema.optional(), _avg: CatalogItemAvgAggregateInputObjectSchema.optional(), _sum: CatalogItemSumAggregateInputObjectSchema.optional()
    }).strict(), groupBy: z.object({
        where: CatalogItemWhereInputObjectSchema.optional(), orderBy: z.union([CatalogItemOrderByWithAggregationInputObjectSchema, CatalogItemOrderByWithAggregationInputObjectSchema.array()]).optional(), having: CatalogItemScalarWhereWithAggregatesInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), by: z.array(CatalogItemScalarFieldEnumSchema), _count: z.union([z.literal(true), CatalogItemCountAggregateInputObjectSchema]).optional(), _min: CatalogItemMinAggregateInputObjectSchema.optional(), _max: CatalogItemMaxAggregateInputObjectSchema.optional(), _avg: CatalogItemAvgAggregateInputObjectSchema.optional(), _sum: CatalogItemSumAggregateInputObjectSchema.optional()
    }).strict(), count: z.object({
        where: CatalogItemWhereInputObjectSchema.optional(), orderBy: z.union([CatalogItemOrderByWithRelationInputObjectSchema, CatalogItemOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: CatalogItemWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(CatalogItemScalarFieldEnumSchema).optional(), select: z.union([z.literal(true), CatalogItemCountAggregateInputObjectSchema]).optional()
    }).strict(),
} as CatalogItemInputSchemaType;
