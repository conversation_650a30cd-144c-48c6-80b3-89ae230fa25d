/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
;
import type { Prisma } from '@prisma/client';

;
import { EquipmentApplicabilitySelectObjectSchema } from '../objects/EquipmentApplicabilitySelect.schema';
import { EquipmentApplicabilityIncludeObjectSchema } from '../objects/EquipmentApplicabilityInclude.schema';
import { EquipmentApplicabilityWhereUniqueInputObjectSchema } from '../objects/EquipmentApplicabilityWhereUniqueInput.schema';
import { EquipmentApplicabilityWhereInputObjectSchema } from '../objects/EquipmentApplicabilityWhereInput.schema';
import { EquipmentApplicabilityOrderByWithRelationInputObjectSchema } from '../objects/EquipmentApplicabilityOrderByWithRelationInput.schema';
import { EquipmentApplicabilityScalarFieldEnumSchema } from '../enums/EquipmentApplicabilityScalarFieldEnum.schema';
import { EquipmentApplicabilityCreateInputObjectSchema } from '../objects/EquipmentApplicabilityCreateInput.schema';
import { EquipmentApplicabilityUncheckedCreateInputObjectSchema } from '../objects/EquipmentApplicabilityUncheckedCreateInput.schema';
import { EquipmentApplicabilityCreateManyInputObjectSchema } from '../objects/EquipmentApplicabilityCreateManyInput.schema';
import { EquipmentApplicabilityUpdateInputObjectSchema } from '../objects/EquipmentApplicabilityUpdateInput.schema';
import { EquipmentApplicabilityUncheckedUpdateInputObjectSchema } from '../objects/EquipmentApplicabilityUncheckedUpdateInput.schema';
import { EquipmentApplicabilityUpdateManyMutationInputObjectSchema } from '../objects/EquipmentApplicabilityUpdateManyMutationInput.schema';
import { EquipmentApplicabilityUncheckedUpdateManyInputObjectSchema } from '../objects/EquipmentApplicabilityUncheckedUpdateManyInput.schema';
import { EquipmentApplicabilityCountAggregateInputObjectSchema } from '../objects/EquipmentApplicabilityCountAggregateInput.schema';
import { EquipmentApplicabilityMinAggregateInputObjectSchema } from '../objects/EquipmentApplicabilityMinAggregateInput.schema';
import { EquipmentApplicabilityMaxAggregateInputObjectSchema } from '../objects/EquipmentApplicabilityMaxAggregateInput.schema';
import { EquipmentApplicabilityAvgAggregateInputObjectSchema } from '../objects/EquipmentApplicabilityAvgAggregateInput.schema';
import { EquipmentApplicabilitySumAggregateInputObjectSchema } from '../objects/EquipmentApplicabilitySumAggregateInput.schema';
import { EquipmentApplicabilityOrderByWithAggregationInputObjectSchema } from '../objects/EquipmentApplicabilityOrderByWithAggregationInput.schema';
import { EquipmentApplicabilityScalarWhereWithAggregatesInputObjectSchema } from '../objects/EquipmentApplicabilityScalarWhereWithAggregatesInput.schema'

type EquipmentApplicabilityInputSchemaType = {
    findUnique: z.ZodType<Prisma.EquipmentApplicabilityFindUniqueArgs>,
    findFirst: z.ZodType<Prisma.EquipmentApplicabilityFindFirstArgs>,
    findMany: z.ZodType<Prisma.EquipmentApplicabilityFindManyArgs>,
    create: z.ZodType<Prisma.EquipmentApplicabilityCreateArgs>,
    createMany: z.ZodType<Prisma.EquipmentApplicabilityCreateManyArgs>,
    delete: z.ZodType<Prisma.EquipmentApplicabilityDeleteArgs>,
    deleteMany: z.ZodType<Prisma.EquipmentApplicabilityDeleteManyArgs>,
    update: z.ZodType<Prisma.EquipmentApplicabilityUpdateArgs>,
    updateMany: z.ZodType<Prisma.EquipmentApplicabilityUpdateManyArgs>,
    upsert: z.ZodType<Prisma.EquipmentApplicabilityUpsertArgs>,
    aggregate: z.ZodType<Prisma.EquipmentApplicabilityAggregateArgs>,
    groupBy: z.ZodType<Prisma.EquipmentApplicabilityGroupByArgs>,
    count: z.ZodType<Prisma.EquipmentApplicabilityCountArgs>
}

export const EquipmentApplicabilityInputSchema = {
    findUnique: z.object({
        select: z.lazy(() => EquipmentApplicabilitySelectObjectSchema.optional()), include: z.lazy(() => EquipmentApplicabilityIncludeObjectSchema.optional()), where: EquipmentApplicabilityWhereUniqueInputObjectSchema
    }).strict(), findFirst: z.object({
        select: z.lazy(() => EquipmentApplicabilitySelectObjectSchema.optional()), include: z.lazy(() => EquipmentApplicabilityIncludeObjectSchema.optional()), where: EquipmentApplicabilityWhereInputObjectSchema.optional(), orderBy: z.union([EquipmentApplicabilityOrderByWithRelationInputObjectSchema, EquipmentApplicabilityOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: EquipmentApplicabilityWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(EquipmentApplicabilityScalarFieldEnumSchema).optional()
    }).strict(), findMany: z.object({
        select: z.lazy(() => EquipmentApplicabilitySelectObjectSchema.optional()), include: z.lazy(() => EquipmentApplicabilityIncludeObjectSchema.optional()), where: EquipmentApplicabilityWhereInputObjectSchema.optional(), orderBy: z.union([EquipmentApplicabilityOrderByWithRelationInputObjectSchema, EquipmentApplicabilityOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: EquipmentApplicabilityWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(EquipmentApplicabilityScalarFieldEnumSchema).optional()
    }).strict(), create: z.object({
        select: z.lazy(() => EquipmentApplicabilitySelectObjectSchema.optional()), include: z.lazy(() => EquipmentApplicabilityIncludeObjectSchema.optional()), data: z.union([EquipmentApplicabilityCreateInputObjectSchema, EquipmentApplicabilityUncheckedCreateInputObjectSchema])
    }).strict(), createMany: z.object({
        data: z.union([EquipmentApplicabilityCreateManyInputObjectSchema, z.array(EquipmentApplicabilityCreateManyInputObjectSchema)]), skipDuplicates: z.boolean().optional()
    }).strict(), 'delete': z.object({
        select: z.lazy(() => EquipmentApplicabilitySelectObjectSchema.optional()), include: z.lazy(() => EquipmentApplicabilityIncludeObjectSchema.optional()), where: EquipmentApplicabilityWhereUniqueInputObjectSchema
    }).strict(), deleteMany: z.object({
        where: EquipmentApplicabilityWhereInputObjectSchema.optional()
    }).strict(), update: z.object({
        select: z.lazy(() => EquipmentApplicabilitySelectObjectSchema.optional()), include: z.lazy(() => EquipmentApplicabilityIncludeObjectSchema.optional()), data: z.union([EquipmentApplicabilityUpdateInputObjectSchema, EquipmentApplicabilityUncheckedUpdateInputObjectSchema]), where: EquipmentApplicabilityWhereUniqueInputObjectSchema
    }).strict(), updateMany: z.object({
        data: z.union([EquipmentApplicabilityUpdateManyMutationInputObjectSchema, EquipmentApplicabilityUncheckedUpdateManyInputObjectSchema]), where: EquipmentApplicabilityWhereInputObjectSchema.optional()
    }).strict(), upsert: z.object({
        select: z.lazy(() => EquipmentApplicabilitySelectObjectSchema.optional()), include: z.lazy(() => EquipmentApplicabilityIncludeObjectSchema.optional()), where: EquipmentApplicabilityWhereUniqueInputObjectSchema, create: z.union([EquipmentApplicabilityCreateInputObjectSchema, EquipmentApplicabilityUncheckedCreateInputObjectSchema]), update: z.union([EquipmentApplicabilityUpdateInputObjectSchema, EquipmentApplicabilityUncheckedUpdateInputObjectSchema])
    }).strict(), aggregate: z.object({
        where: EquipmentApplicabilityWhereInputObjectSchema.optional(), orderBy: z.union([EquipmentApplicabilityOrderByWithRelationInputObjectSchema, EquipmentApplicabilityOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: EquipmentApplicabilityWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), _count: z.union([z.literal(true), EquipmentApplicabilityCountAggregateInputObjectSchema]).optional(), _min: EquipmentApplicabilityMinAggregateInputObjectSchema.optional(), _max: EquipmentApplicabilityMaxAggregateInputObjectSchema.optional(), _avg: EquipmentApplicabilityAvgAggregateInputObjectSchema.optional(), _sum: EquipmentApplicabilitySumAggregateInputObjectSchema.optional()
    }).strict(), groupBy: z.object({
        where: EquipmentApplicabilityWhereInputObjectSchema.optional(), orderBy: z.union([EquipmentApplicabilityOrderByWithAggregationInputObjectSchema, EquipmentApplicabilityOrderByWithAggregationInputObjectSchema.array()]).optional(), having: EquipmentApplicabilityScalarWhereWithAggregatesInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), by: z.array(EquipmentApplicabilityScalarFieldEnumSchema), _count: z.union([z.literal(true), EquipmentApplicabilityCountAggregateInputObjectSchema]).optional(), _min: EquipmentApplicabilityMinAggregateInputObjectSchema.optional(), _max: EquipmentApplicabilityMaxAggregateInputObjectSchema.optional(), _avg: EquipmentApplicabilityAvgAggregateInputObjectSchema.optional(), _sum: EquipmentApplicabilitySumAggregateInputObjectSchema.optional()
    }).strict(), count: z.object({
        where: EquipmentApplicabilityWhereInputObjectSchema.optional(), orderBy: z.union([EquipmentApplicabilityOrderByWithRelationInputObjectSchema, EquipmentApplicabilityOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: EquipmentApplicabilityWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(EquipmentApplicabilityScalarFieldEnumSchema).optional(), select: z.union([z.literal(true), EquipmentApplicabilityCountAggregateInputObjectSchema]).optional()
    }).strict(),
} as EquipmentApplicabilityInputSchemaType;
