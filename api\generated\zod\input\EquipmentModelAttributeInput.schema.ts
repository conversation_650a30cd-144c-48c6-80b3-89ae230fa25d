/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
;
import type { Prisma } from '@prisma/client';

;
import { EquipmentModelAttributeSelectObjectSchema } from '../objects/EquipmentModelAttributeSelect.schema';
import { EquipmentModelAttributeIncludeObjectSchema } from '../objects/EquipmentModelAttributeInclude.schema';
import { EquipmentModelAttributeWhereUniqueInputObjectSchema } from '../objects/EquipmentModelAttributeWhereUniqueInput.schema';
import { EquipmentModelAttributeWhereInputObjectSchema } from '../objects/EquipmentModelAttributeWhereInput.schema';
import { EquipmentModelAttributeOrderByWithRelationInputObjectSchema } from '../objects/EquipmentModelAttributeOrderByWithRelationInput.schema';
import { EquipmentModelAttributeScalarFieldEnumSchema } from '../enums/EquipmentModelAttributeScalarFieldEnum.schema';
import { EquipmentModelAttributeCreateInputObjectSchema } from '../objects/EquipmentModelAttributeCreateInput.schema';
import { EquipmentModelAttributeUncheckedCreateInputObjectSchema } from '../objects/EquipmentModelAttributeUncheckedCreateInput.schema';
import { EquipmentModelAttributeCreateManyInputObjectSchema } from '../objects/EquipmentModelAttributeCreateManyInput.schema';
import { EquipmentModelAttributeUpdateInputObjectSchema } from '../objects/EquipmentModelAttributeUpdateInput.schema';
import { EquipmentModelAttributeUncheckedUpdateInputObjectSchema } from '../objects/EquipmentModelAttributeUncheckedUpdateInput.schema';
import { EquipmentModelAttributeUpdateManyMutationInputObjectSchema } from '../objects/EquipmentModelAttributeUpdateManyMutationInput.schema';
import { EquipmentModelAttributeUncheckedUpdateManyInputObjectSchema } from '../objects/EquipmentModelAttributeUncheckedUpdateManyInput.schema';
import { EquipmentModelAttributeCountAggregateInputObjectSchema } from '../objects/EquipmentModelAttributeCountAggregateInput.schema';
import { EquipmentModelAttributeMinAggregateInputObjectSchema } from '../objects/EquipmentModelAttributeMinAggregateInput.schema';
import { EquipmentModelAttributeMaxAggregateInputObjectSchema } from '../objects/EquipmentModelAttributeMaxAggregateInput.schema';
import { EquipmentModelAttributeAvgAggregateInputObjectSchema } from '../objects/EquipmentModelAttributeAvgAggregateInput.schema';
import { EquipmentModelAttributeSumAggregateInputObjectSchema } from '../objects/EquipmentModelAttributeSumAggregateInput.schema';
import { EquipmentModelAttributeOrderByWithAggregationInputObjectSchema } from '../objects/EquipmentModelAttributeOrderByWithAggregationInput.schema';
import { EquipmentModelAttributeScalarWhereWithAggregatesInputObjectSchema } from '../objects/EquipmentModelAttributeScalarWhereWithAggregatesInput.schema'

type EquipmentModelAttributeInputSchemaType = {
    findUnique: z.ZodType<Prisma.EquipmentModelAttributeFindUniqueArgs>,
    findFirst: z.ZodType<Prisma.EquipmentModelAttributeFindFirstArgs>,
    findMany: z.ZodType<Prisma.EquipmentModelAttributeFindManyArgs>,
    create: z.ZodType<Prisma.EquipmentModelAttributeCreateArgs>,
    createMany: z.ZodType<Prisma.EquipmentModelAttributeCreateManyArgs>,
    delete: z.ZodType<Prisma.EquipmentModelAttributeDeleteArgs>,
    deleteMany: z.ZodType<Prisma.EquipmentModelAttributeDeleteManyArgs>,
    update: z.ZodType<Prisma.EquipmentModelAttributeUpdateArgs>,
    updateMany: z.ZodType<Prisma.EquipmentModelAttributeUpdateManyArgs>,
    upsert: z.ZodType<Prisma.EquipmentModelAttributeUpsertArgs>,
    aggregate: z.ZodType<Prisma.EquipmentModelAttributeAggregateArgs>,
    groupBy: z.ZodType<Prisma.EquipmentModelAttributeGroupByArgs>,
    count: z.ZodType<Prisma.EquipmentModelAttributeCountArgs>
}

export const EquipmentModelAttributeInputSchema = {
    findUnique: z.object({
        select: z.lazy(() => EquipmentModelAttributeSelectObjectSchema.optional()), include: z.lazy(() => EquipmentModelAttributeIncludeObjectSchema.optional()), where: EquipmentModelAttributeWhereUniqueInputObjectSchema
    }).strict(), findFirst: z.object({
        select: z.lazy(() => EquipmentModelAttributeSelectObjectSchema.optional()), include: z.lazy(() => EquipmentModelAttributeIncludeObjectSchema.optional()), where: EquipmentModelAttributeWhereInputObjectSchema.optional(), orderBy: z.union([EquipmentModelAttributeOrderByWithRelationInputObjectSchema, EquipmentModelAttributeOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: EquipmentModelAttributeWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(EquipmentModelAttributeScalarFieldEnumSchema).optional()
    }).strict(), findMany: z.object({
        select: z.lazy(() => EquipmentModelAttributeSelectObjectSchema.optional()), include: z.lazy(() => EquipmentModelAttributeIncludeObjectSchema.optional()), where: EquipmentModelAttributeWhereInputObjectSchema.optional(), orderBy: z.union([EquipmentModelAttributeOrderByWithRelationInputObjectSchema, EquipmentModelAttributeOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: EquipmentModelAttributeWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(EquipmentModelAttributeScalarFieldEnumSchema).optional()
    }).strict(), create: z.object({
        select: z.lazy(() => EquipmentModelAttributeSelectObjectSchema.optional()), include: z.lazy(() => EquipmentModelAttributeIncludeObjectSchema.optional()), data: z.union([EquipmentModelAttributeCreateInputObjectSchema, EquipmentModelAttributeUncheckedCreateInputObjectSchema])
    }).strict(), createMany: z.object({
        data: z.union([EquipmentModelAttributeCreateManyInputObjectSchema, z.array(EquipmentModelAttributeCreateManyInputObjectSchema)]), skipDuplicates: z.boolean().optional()
    }).strict(), 'delete': z.object({
        select: z.lazy(() => EquipmentModelAttributeSelectObjectSchema.optional()), include: z.lazy(() => EquipmentModelAttributeIncludeObjectSchema.optional()), where: EquipmentModelAttributeWhereUniqueInputObjectSchema
    }).strict(), deleteMany: z.object({
        where: EquipmentModelAttributeWhereInputObjectSchema.optional()
    }).strict(), update: z.object({
        select: z.lazy(() => EquipmentModelAttributeSelectObjectSchema.optional()), include: z.lazy(() => EquipmentModelAttributeIncludeObjectSchema.optional()), data: z.union([EquipmentModelAttributeUpdateInputObjectSchema, EquipmentModelAttributeUncheckedUpdateInputObjectSchema]), where: EquipmentModelAttributeWhereUniqueInputObjectSchema
    }).strict(), updateMany: z.object({
        data: z.union([EquipmentModelAttributeUpdateManyMutationInputObjectSchema, EquipmentModelAttributeUncheckedUpdateManyInputObjectSchema]), where: EquipmentModelAttributeWhereInputObjectSchema.optional()
    }).strict(), upsert: z.object({
        select: z.lazy(() => EquipmentModelAttributeSelectObjectSchema.optional()), include: z.lazy(() => EquipmentModelAttributeIncludeObjectSchema.optional()), where: EquipmentModelAttributeWhereUniqueInputObjectSchema, create: z.union([EquipmentModelAttributeCreateInputObjectSchema, EquipmentModelAttributeUncheckedCreateInputObjectSchema]), update: z.union([EquipmentModelAttributeUpdateInputObjectSchema, EquipmentModelAttributeUncheckedUpdateInputObjectSchema])
    }).strict(), aggregate: z.object({
        where: EquipmentModelAttributeWhereInputObjectSchema.optional(), orderBy: z.union([EquipmentModelAttributeOrderByWithRelationInputObjectSchema, EquipmentModelAttributeOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: EquipmentModelAttributeWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), _count: z.union([z.literal(true), EquipmentModelAttributeCountAggregateInputObjectSchema]).optional(), _min: EquipmentModelAttributeMinAggregateInputObjectSchema.optional(), _max: EquipmentModelAttributeMaxAggregateInputObjectSchema.optional(), _avg: EquipmentModelAttributeAvgAggregateInputObjectSchema.optional(), _sum: EquipmentModelAttributeSumAggregateInputObjectSchema.optional()
    }).strict(), groupBy: z.object({
        where: EquipmentModelAttributeWhereInputObjectSchema.optional(), orderBy: z.union([EquipmentModelAttributeOrderByWithAggregationInputObjectSchema, EquipmentModelAttributeOrderByWithAggregationInputObjectSchema.array()]).optional(), having: EquipmentModelAttributeScalarWhereWithAggregatesInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), by: z.array(EquipmentModelAttributeScalarFieldEnumSchema), _count: z.union([z.literal(true), EquipmentModelAttributeCountAggregateInputObjectSchema]).optional(), _min: EquipmentModelAttributeMinAggregateInputObjectSchema.optional(), _max: EquipmentModelAttributeMaxAggregateInputObjectSchema.optional(), _avg: EquipmentModelAttributeAvgAggregateInputObjectSchema.optional(), _sum: EquipmentModelAttributeSumAggregateInputObjectSchema.optional()
    }).strict(), count: z.object({
        where: EquipmentModelAttributeWhereInputObjectSchema.optional(), orderBy: z.union([EquipmentModelAttributeOrderByWithRelationInputObjectSchema, EquipmentModelAttributeOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: EquipmentModelAttributeWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(EquipmentModelAttributeScalarFieldEnumSchema).optional(), select: z.union([z.literal(true), EquipmentModelAttributeCountAggregateInputObjectSchema]).optional()
    }).strict(),
} as EquipmentModelAttributeInputSchemaType;
