/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
;
import type { Prisma } from '@prisma/client';

;
import { EquipmentModelSelectObjectSchema } from '../objects/EquipmentModelSelect.schema';
import { EquipmentModelIncludeObjectSchema } from '../objects/EquipmentModelInclude.schema';
import { EquipmentModelWhereUniqueInputObjectSchema } from '../objects/EquipmentModelWhereUniqueInput.schema';
import { EquipmentModelWhereInputObjectSchema } from '../objects/EquipmentModelWhereInput.schema';
import { EquipmentModelOrderByWithRelationInputObjectSchema } from '../objects/EquipmentModelOrderByWithRelationInput.schema';
import { EquipmentModelScalarFieldEnumSchema } from '../enums/EquipmentModelScalarFieldEnum.schema';
import { EquipmentModelCreateInputObjectSchema } from '../objects/EquipmentModelCreateInput.schema';
import { EquipmentModelUncheckedCreateInputObjectSchema } from '../objects/EquipmentModelUncheckedCreateInput.schema';
import { EquipmentModelCreateManyInputObjectSchema } from '../objects/EquipmentModelCreateManyInput.schema';
import { EquipmentModelUpdateInputObjectSchema } from '../objects/EquipmentModelUpdateInput.schema';
import { EquipmentModelUncheckedUpdateInputObjectSchema } from '../objects/EquipmentModelUncheckedUpdateInput.schema';
import { EquipmentModelUpdateManyMutationInputObjectSchema } from '../objects/EquipmentModelUpdateManyMutationInput.schema';
import { EquipmentModelUncheckedUpdateManyInputObjectSchema } from '../objects/EquipmentModelUncheckedUpdateManyInput.schema';
import { EquipmentModelCountAggregateInputObjectSchema } from '../objects/EquipmentModelCountAggregateInput.schema';
import { EquipmentModelMinAggregateInputObjectSchema } from '../objects/EquipmentModelMinAggregateInput.schema';
import { EquipmentModelMaxAggregateInputObjectSchema } from '../objects/EquipmentModelMaxAggregateInput.schema';
import { EquipmentModelAvgAggregateInputObjectSchema } from '../objects/EquipmentModelAvgAggregateInput.schema';
import { EquipmentModelSumAggregateInputObjectSchema } from '../objects/EquipmentModelSumAggregateInput.schema';
import { EquipmentModelOrderByWithAggregationInputObjectSchema } from '../objects/EquipmentModelOrderByWithAggregationInput.schema';
import { EquipmentModelScalarWhereWithAggregatesInputObjectSchema } from '../objects/EquipmentModelScalarWhereWithAggregatesInput.schema'

type EquipmentModelInputSchemaType = {
    findUnique: z.ZodType<Prisma.EquipmentModelFindUniqueArgs>,
    findFirst: z.ZodType<Prisma.EquipmentModelFindFirstArgs>,
    findMany: z.ZodType<Prisma.EquipmentModelFindManyArgs>,
    create: z.ZodType<Prisma.EquipmentModelCreateArgs>,
    createMany: z.ZodType<Prisma.EquipmentModelCreateManyArgs>,
    delete: z.ZodType<Prisma.EquipmentModelDeleteArgs>,
    deleteMany: z.ZodType<Prisma.EquipmentModelDeleteManyArgs>,
    update: z.ZodType<Prisma.EquipmentModelUpdateArgs>,
    updateMany: z.ZodType<Prisma.EquipmentModelUpdateManyArgs>,
    upsert: z.ZodType<Prisma.EquipmentModelUpsertArgs>,
    aggregate: z.ZodType<Prisma.EquipmentModelAggregateArgs>,
    groupBy: z.ZodType<Prisma.EquipmentModelGroupByArgs>,
    count: z.ZodType<Prisma.EquipmentModelCountArgs>
}

export const EquipmentModelInputSchema = {
    findUnique: z.object({
        select: z.lazy(() => EquipmentModelSelectObjectSchema.optional()), include: z.lazy(() => EquipmentModelIncludeObjectSchema.optional()), where: EquipmentModelWhereUniqueInputObjectSchema
    }).strict(), findFirst: z.object({
        select: z.lazy(() => EquipmentModelSelectObjectSchema.optional()), include: z.lazy(() => EquipmentModelIncludeObjectSchema.optional()), where: EquipmentModelWhereInputObjectSchema.optional(), orderBy: z.union([EquipmentModelOrderByWithRelationInputObjectSchema, EquipmentModelOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: EquipmentModelWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(EquipmentModelScalarFieldEnumSchema).optional()
    }).strict(), findMany: z.object({
        select: z.lazy(() => EquipmentModelSelectObjectSchema.optional()), include: z.lazy(() => EquipmentModelIncludeObjectSchema.optional()), where: EquipmentModelWhereInputObjectSchema.optional(), orderBy: z.union([EquipmentModelOrderByWithRelationInputObjectSchema, EquipmentModelOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: EquipmentModelWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(EquipmentModelScalarFieldEnumSchema).optional()
    }).strict(), create: z.object({
        select: z.lazy(() => EquipmentModelSelectObjectSchema.optional()), include: z.lazy(() => EquipmentModelIncludeObjectSchema.optional()), data: z.union([EquipmentModelCreateInputObjectSchema, EquipmentModelUncheckedCreateInputObjectSchema])
    }).strict(), createMany: z.object({
        data: z.union([EquipmentModelCreateManyInputObjectSchema, z.array(EquipmentModelCreateManyInputObjectSchema)]), skipDuplicates: z.boolean().optional()
    }).strict(), 'delete': z.object({
        select: z.lazy(() => EquipmentModelSelectObjectSchema.optional()), include: z.lazy(() => EquipmentModelIncludeObjectSchema.optional()), where: EquipmentModelWhereUniqueInputObjectSchema
    }).strict(), deleteMany: z.object({
        where: EquipmentModelWhereInputObjectSchema.optional()
    }).strict(), update: z.object({
        select: z.lazy(() => EquipmentModelSelectObjectSchema.optional()), include: z.lazy(() => EquipmentModelIncludeObjectSchema.optional()), data: z.union([EquipmentModelUpdateInputObjectSchema, EquipmentModelUncheckedUpdateInputObjectSchema]), where: EquipmentModelWhereUniqueInputObjectSchema
    }).strict(), updateMany: z.object({
        data: z.union([EquipmentModelUpdateManyMutationInputObjectSchema, EquipmentModelUncheckedUpdateManyInputObjectSchema]), where: EquipmentModelWhereInputObjectSchema.optional()
    }).strict(), upsert: z.object({
        select: z.lazy(() => EquipmentModelSelectObjectSchema.optional()), include: z.lazy(() => EquipmentModelIncludeObjectSchema.optional()), where: EquipmentModelWhereUniqueInputObjectSchema, create: z.union([EquipmentModelCreateInputObjectSchema, EquipmentModelUncheckedCreateInputObjectSchema]), update: z.union([EquipmentModelUpdateInputObjectSchema, EquipmentModelUncheckedUpdateInputObjectSchema])
    }).strict(), aggregate: z.object({
        where: EquipmentModelWhereInputObjectSchema.optional(), orderBy: z.union([EquipmentModelOrderByWithRelationInputObjectSchema, EquipmentModelOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: EquipmentModelWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), _count: z.union([z.literal(true), EquipmentModelCountAggregateInputObjectSchema]).optional(), _min: EquipmentModelMinAggregateInputObjectSchema.optional(), _max: EquipmentModelMaxAggregateInputObjectSchema.optional(), _avg: EquipmentModelAvgAggregateInputObjectSchema.optional(), _sum: EquipmentModelSumAggregateInputObjectSchema.optional()
    }).strict(), groupBy: z.object({
        where: EquipmentModelWhereInputObjectSchema.optional(), orderBy: z.union([EquipmentModelOrderByWithAggregationInputObjectSchema, EquipmentModelOrderByWithAggregationInputObjectSchema.array()]).optional(), having: EquipmentModelScalarWhereWithAggregatesInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), by: z.array(EquipmentModelScalarFieldEnumSchema), _count: z.union([z.literal(true), EquipmentModelCountAggregateInputObjectSchema]).optional(), _min: EquipmentModelMinAggregateInputObjectSchema.optional(), _max: EquipmentModelMaxAggregateInputObjectSchema.optional(), _avg: EquipmentModelAvgAggregateInputObjectSchema.optional(), _sum: EquipmentModelSumAggregateInputObjectSchema.optional()
    }).strict(), count: z.object({
        where: EquipmentModelWhereInputObjectSchema.optional(), orderBy: z.union([EquipmentModelOrderByWithRelationInputObjectSchema, EquipmentModelOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: EquipmentModelWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(EquipmentModelScalarFieldEnumSchema).optional(), select: z.union([z.literal(true), EquipmentModelCountAggregateInputObjectSchema]).optional()
    }).strict(),
} as EquipmentModelInputSchemaType;
