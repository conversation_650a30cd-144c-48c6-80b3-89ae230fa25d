/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
;
import type { Prisma } from '@prisma/client';

;
import { MatchingProposalSelectObjectSchema } from '../objects/MatchingProposalSelect.schema';
import { MatchingProposalIncludeObjectSchema } from '../objects/MatchingProposalInclude.schema';
import { MatchingProposalWhereUniqueInputObjectSchema } from '../objects/MatchingProposalWhereUniqueInput.schema';
import { MatchingProposalWhereInputObjectSchema } from '../objects/MatchingProposalWhereInput.schema';
import { MatchingProposalOrderByWithRelationInputObjectSchema } from '../objects/MatchingProposalOrderByWithRelationInput.schema';
import { MatchingProposalScalarFieldEnumSchema } from '../enums/MatchingProposalScalarFieldEnum.schema';
import { MatchingProposalCreateInputObjectSchema } from '../objects/MatchingProposalCreateInput.schema';
import { MatchingProposalUncheckedCreateInputObjectSchema } from '../objects/MatchingProposalUncheckedCreateInput.schema';
import { MatchingProposalCreateManyInputObjectSchema } from '../objects/MatchingProposalCreateManyInput.schema';
import { MatchingProposalUpdateInputObjectSchema } from '../objects/MatchingProposalUpdateInput.schema';
import { MatchingProposalUncheckedUpdateInputObjectSchema } from '../objects/MatchingProposalUncheckedUpdateInput.schema';
import { MatchingProposalUpdateManyMutationInputObjectSchema } from '../objects/MatchingProposalUpdateManyMutationInput.schema';
import { MatchingProposalUncheckedUpdateManyInputObjectSchema } from '../objects/MatchingProposalUncheckedUpdateManyInput.schema';
import { MatchingProposalCountAggregateInputObjectSchema } from '../objects/MatchingProposalCountAggregateInput.schema';
import { MatchingProposalMinAggregateInputObjectSchema } from '../objects/MatchingProposalMinAggregateInput.schema';
import { MatchingProposalMaxAggregateInputObjectSchema } from '../objects/MatchingProposalMaxAggregateInput.schema';
import { MatchingProposalAvgAggregateInputObjectSchema } from '../objects/MatchingProposalAvgAggregateInput.schema';
import { MatchingProposalSumAggregateInputObjectSchema } from '../objects/MatchingProposalSumAggregateInput.schema';
import { MatchingProposalOrderByWithAggregationInputObjectSchema } from '../objects/MatchingProposalOrderByWithAggregationInput.schema';
import { MatchingProposalScalarWhereWithAggregatesInputObjectSchema } from '../objects/MatchingProposalScalarWhereWithAggregatesInput.schema'

type MatchingProposalInputSchemaType = {
    findUnique: z.ZodType<Prisma.MatchingProposalFindUniqueArgs>,
    findFirst: z.ZodType<Prisma.MatchingProposalFindFirstArgs>,
    findMany: z.ZodType<Prisma.MatchingProposalFindManyArgs>,
    create: z.ZodType<Prisma.MatchingProposalCreateArgs>,
    createMany: z.ZodType<Prisma.MatchingProposalCreateManyArgs>,
    delete: z.ZodType<Prisma.MatchingProposalDeleteArgs>,
    deleteMany: z.ZodType<Prisma.MatchingProposalDeleteManyArgs>,
    update: z.ZodType<Prisma.MatchingProposalUpdateArgs>,
    updateMany: z.ZodType<Prisma.MatchingProposalUpdateManyArgs>,
    upsert: z.ZodType<Prisma.MatchingProposalUpsertArgs>,
    aggregate: z.ZodType<Prisma.MatchingProposalAggregateArgs>,
    groupBy: z.ZodType<Prisma.MatchingProposalGroupByArgs>,
    count: z.ZodType<Prisma.MatchingProposalCountArgs>
}

export const MatchingProposalInputSchema = {
    findUnique: z.object({
        select: z.lazy(() => MatchingProposalSelectObjectSchema.optional()), include: z.lazy(() => MatchingProposalIncludeObjectSchema.optional()), where: MatchingProposalWhereUniqueInputObjectSchema
    }).strict(), findFirst: z.object({
        select: z.lazy(() => MatchingProposalSelectObjectSchema.optional()), include: z.lazy(() => MatchingProposalIncludeObjectSchema.optional()), where: MatchingProposalWhereInputObjectSchema.optional(), orderBy: z.union([MatchingProposalOrderByWithRelationInputObjectSchema, MatchingProposalOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: MatchingProposalWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(MatchingProposalScalarFieldEnumSchema).optional()
    }).strict(), findMany: z.object({
        select: z.lazy(() => MatchingProposalSelectObjectSchema.optional()), include: z.lazy(() => MatchingProposalIncludeObjectSchema.optional()), where: MatchingProposalWhereInputObjectSchema.optional(), orderBy: z.union([MatchingProposalOrderByWithRelationInputObjectSchema, MatchingProposalOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: MatchingProposalWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(MatchingProposalScalarFieldEnumSchema).optional()
    }).strict(), create: z.object({
        select: z.lazy(() => MatchingProposalSelectObjectSchema.optional()), include: z.lazy(() => MatchingProposalIncludeObjectSchema.optional()), data: z.union([MatchingProposalCreateInputObjectSchema, MatchingProposalUncheckedCreateInputObjectSchema])
    }).strict(), createMany: z.object({
        data: z.union([MatchingProposalCreateManyInputObjectSchema, z.array(MatchingProposalCreateManyInputObjectSchema)]), skipDuplicates: z.boolean().optional()
    }).strict(), 'delete': z.object({
        select: z.lazy(() => MatchingProposalSelectObjectSchema.optional()), include: z.lazy(() => MatchingProposalIncludeObjectSchema.optional()), where: MatchingProposalWhereUniqueInputObjectSchema
    }).strict(), deleteMany: z.object({
        where: MatchingProposalWhereInputObjectSchema.optional()
    }).strict(), update: z.object({
        select: z.lazy(() => MatchingProposalSelectObjectSchema.optional()), include: z.lazy(() => MatchingProposalIncludeObjectSchema.optional()), data: z.union([MatchingProposalUpdateInputObjectSchema, MatchingProposalUncheckedUpdateInputObjectSchema]), where: MatchingProposalWhereUniqueInputObjectSchema
    }).strict(), updateMany: z.object({
        data: z.union([MatchingProposalUpdateManyMutationInputObjectSchema, MatchingProposalUncheckedUpdateManyInputObjectSchema]), where: MatchingProposalWhereInputObjectSchema.optional()
    }).strict(), upsert: z.object({
        select: z.lazy(() => MatchingProposalSelectObjectSchema.optional()), include: z.lazy(() => MatchingProposalIncludeObjectSchema.optional()), where: MatchingProposalWhereUniqueInputObjectSchema, create: z.union([MatchingProposalCreateInputObjectSchema, MatchingProposalUncheckedCreateInputObjectSchema]), update: z.union([MatchingProposalUpdateInputObjectSchema, MatchingProposalUncheckedUpdateInputObjectSchema])
    }).strict(), aggregate: z.object({
        where: MatchingProposalWhereInputObjectSchema.optional(), orderBy: z.union([MatchingProposalOrderByWithRelationInputObjectSchema, MatchingProposalOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: MatchingProposalWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), _count: z.union([z.literal(true), MatchingProposalCountAggregateInputObjectSchema]).optional(), _min: MatchingProposalMinAggregateInputObjectSchema.optional(), _max: MatchingProposalMaxAggregateInputObjectSchema.optional(), _avg: MatchingProposalAvgAggregateInputObjectSchema.optional(), _sum: MatchingProposalSumAggregateInputObjectSchema.optional()
    }).strict(), groupBy: z.object({
        where: MatchingProposalWhereInputObjectSchema.optional(), orderBy: z.union([MatchingProposalOrderByWithAggregationInputObjectSchema, MatchingProposalOrderByWithAggregationInputObjectSchema.array()]).optional(), having: MatchingProposalScalarWhereWithAggregatesInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), by: z.array(MatchingProposalScalarFieldEnumSchema), _count: z.union([z.literal(true), MatchingProposalCountAggregateInputObjectSchema]).optional(), _min: MatchingProposalMinAggregateInputObjectSchema.optional(), _max: MatchingProposalMaxAggregateInputObjectSchema.optional(), _avg: MatchingProposalAvgAggregateInputObjectSchema.optional(), _sum: MatchingProposalSumAggregateInputObjectSchema.optional()
    }).strict(), count: z.object({
        where: MatchingProposalWhereInputObjectSchema.optional(), orderBy: z.union([MatchingProposalOrderByWithRelationInputObjectSchema, MatchingProposalOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: MatchingProposalWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(MatchingProposalScalarFieldEnumSchema).optional(), select: z.union([z.literal(true), MatchingProposalCountAggregateInputObjectSchema]).optional()
    }).strict(),
} as MatchingProposalInputSchemaType;
