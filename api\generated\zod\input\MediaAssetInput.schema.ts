/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
;
import type { Prisma } from '@prisma/client';

;
import { MediaAssetSelectObjectSchema } from '../objects/MediaAssetSelect.schema';
import { MediaAssetIncludeObjectSchema } from '../objects/MediaAssetInclude.schema';
import { MediaAssetWhereUniqueInputObjectSchema } from '../objects/MediaAssetWhereUniqueInput.schema';
import { MediaAssetWhereInputObjectSchema } from '../objects/MediaAssetWhereInput.schema';
import { MediaAssetOrderByWithRelationInputObjectSchema } from '../objects/MediaAssetOrderByWithRelationInput.schema';
import { MediaAssetScalarFieldEnumSchema } from '../enums/MediaAssetScalarFieldEnum.schema';
import { MediaAssetCreateInputObjectSchema } from '../objects/MediaAssetCreateInput.schema';
import { MediaAssetUncheckedCreateInputObjectSchema } from '../objects/MediaAssetUncheckedCreateInput.schema';
import { MediaAssetCreateManyInputObjectSchema } from '../objects/MediaAssetCreateManyInput.schema';
import { MediaAssetUpdateInputObjectSchema } from '../objects/MediaAssetUpdateInput.schema';
import { MediaAssetUncheckedUpdateInputObjectSchema } from '../objects/MediaAssetUncheckedUpdateInput.schema';
import { MediaAssetUpdateManyMutationInputObjectSchema } from '../objects/MediaAssetUpdateManyMutationInput.schema';
import { MediaAssetUncheckedUpdateManyInputObjectSchema } from '../objects/MediaAssetUncheckedUpdateManyInput.schema';
import { MediaAssetCountAggregateInputObjectSchema } from '../objects/MediaAssetCountAggregateInput.schema';
import { MediaAssetMinAggregateInputObjectSchema } from '../objects/MediaAssetMinAggregateInput.schema';
import { MediaAssetMaxAggregateInputObjectSchema } from '../objects/MediaAssetMaxAggregateInput.schema';
import { MediaAssetAvgAggregateInputObjectSchema } from '../objects/MediaAssetAvgAggregateInput.schema';
import { MediaAssetSumAggregateInputObjectSchema } from '../objects/MediaAssetSumAggregateInput.schema';
import { MediaAssetOrderByWithAggregationInputObjectSchema } from '../objects/MediaAssetOrderByWithAggregationInput.schema';
import { MediaAssetScalarWhereWithAggregatesInputObjectSchema } from '../objects/MediaAssetScalarWhereWithAggregatesInput.schema'

type MediaAssetInputSchemaType = {
    findUnique: z.ZodType<Prisma.MediaAssetFindUniqueArgs>,
    findFirst: z.ZodType<Prisma.MediaAssetFindFirstArgs>,
    findMany: z.ZodType<Prisma.MediaAssetFindManyArgs>,
    create: z.ZodType<Prisma.MediaAssetCreateArgs>,
    createMany: z.ZodType<Prisma.MediaAssetCreateManyArgs>,
    delete: z.ZodType<Prisma.MediaAssetDeleteArgs>,
    deleteMany: z.ZodType<Prisma.MediaAssetDeleteManyArgs>,
    update: z.ZodType<Prisma.MediaAssetUpdateArgs>,
    updateMany: z.ZodType<Prisma.MediaAssetUpdateManyArgs>,
    upsert: z.ZodType<Prisma.MediaAssetUpsertArgs>,
    aggregate: z.ZodType<Prisma.MediaAssetAggregateArgs>,
    groupBy: z.ZodType<Prisma.MediaAssetGroupByArgs>,
    count: z.ZodType<Prisma.MediaAssetCountArgs>
}

export const MediaAssetInputSchema = {
    findUnique: z.object({
        select: z.lazy(() => MediaAssetSelectObjectSchema.optional()), include: z.lazy(() => MediaAssetIncludeObjectSchema.optional()), where: MediaAssetWhereUniqueInputObjectSchema
    }).strict(), findFirst: z.object({
        select: z.lazy(() => MediaAssetSelectObjectSchema.optional()), include: z.lazy(() => MediaAssetIncludeObjectSchema.optional()), where: MediaAssetWhereInputObjectSchema.optional(), orderBy: z.union([MediaAssetOrderByWithRelationInputObjectSchema, MediaAssetOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: MediaAssetWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(MediaAssetScalarFieldEnumSchema).optional()
    }).strict(), findMany: z.object({
        select: z.lazy(() => MediaAssetSelectObjectSchema.optional()), include: z.lazy(() => MediaAssetIncludeObjectSchema.optional()), where: MediaAssetWhereInputObjectSchema.optional(), orderBy: z.union([MediaAssetOrderByWithRelationInputObjectSchema, MediaAssetOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: MediaAssetWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(MediaAssetScalarFieldEnumSchema).optional()
    }).strict(), create: z.object({
        select: z.lazy(() => MediaAssetSelectObjectSchema.optional()), include: z.lazy(() => MediaAssetIncludeObjectSchema.optional()), data: z.union([MediaAssetCreateInputObjectSchema, MediaAssetUncheckedCreateInputObjectSchema])
    }).strict(), createMany: z.object({
        data: z.union([MediaAssetCreateManyInputObjectSchema, z.array(MediaAssetCreateManyInputObjectSchema)]), skipDuplicates: z.boolean().optional()
    }).strict(), 'delete': z.object({
        select: z.lazy(() => MediaAssetSelectObjectSchema.optional()), include: z.lazy(() => MediaAssetIncludeObjectSchema.optional()), where: MediaAssetWhereUniqueInputObjectSchema
    }).strict(), deleteMany: z.object({
        where: MediaAssetWhereInputObjectSchema.optional()
    }).strict(), update: z.object({
        select: z.lazy(() => MediaAssetSelectObjectSchema.optional()), include: z.lazy(() => MediaAssetIncludeObjectSchema.optional()), data: z.union([MediaAssetUpdateInputObjectSchema, MediaAssetUncheckedUpdateInputObjectSchema]), where: MediaAssetWhereUniqueInputObjectSchema
    }).strict(), updateMany: z.object({
        data: z.union([MediaAssetUpdateManyMutationInputObjectSchema, MediaAssetUncheckedUpdateManyInputObjectSchema]), where: MediaAssetWhereInputObjectSchema.optional()
    }).strict(), upsert: z.object({
        select: z.lazy(() => MediaAssetSelectObjectSchema.optional()), include: z.lazy(() => MediaAssetIncludeObjectSchema.optional()), where: MediaAssetWhereUniqueInputObjectSchema, create: z.union([MediaAssetCreateInputObjectSchema, MediaAssetUncheckedCreateInputObjectSchema]), update: z.union([MediaAssetUpdateInputObjectSchema, MediaAssetUncheckedUpdateInputObjectSchema])
    }).strict(), aggregate: z.object({
        where: MediaAssetWhereInputObjectSchema.optional(), orderBy: z.union([MediaAssetOrderByWithRelationInputObjectSchema, MediaAssetOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: MediaAssetWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), _count: z.union([z.literal(true), MediaAssetCountAggregateInputObjectSchema]).optional(), _min: MediaAssetMinAggregateInputObjectSchema.optional(), _max: MediaAssetMaxAggregateInputObjectSchema.optional(), _avg: MediaAssetAvgAggregateInputObjectSchema.optional(), _sum: MediaAssetSumAggregateInputObjectSchema.optional()
    }).strict(), groupBy: z.object({
        where: MediaAssetWhereInputObjectSchema.optional(), orderBy: z.union([MediaAssetOrderByWithAggregationInputObjectSchema, MediaAssetOrderByWithAggregationInputObjectSchema.array()]).optional(), having: MediaAssetScalarWhereWithAggregatesInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), by: z.array(MediaAssetScalarFieldEnumSchema), _count: z.union([z.literal(true), MediaAssetCountAggregateInputObjectSchema]).optional(), _min: MediaAssetMinAggregateInputObjectSchema.optional(), _max: MediaAssetMaxAggregateInputObjectSchema.optional(), _avg: MediaAssetAvgAggregateInputObjectSchema.optional(), _sum: MediaAssetSumAggregateInputObjectSchema.optional()
    }).strict(), count: z.object({
        where: MediaAssetWhereInputObjectSchema.optional(), orderBy: z.union([MediaAssetOrderByWithRelationInputObjectSchema, MediaAssetOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: MediaAssetWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(MediaAssetScalarFieldEnumSchema).optional(), select: z.union([z.literal(true), MediaAssetCountAggregateInputObjectSchema]).optional()
    }).strict(),
} as MediaAssetInputSchemaType;
