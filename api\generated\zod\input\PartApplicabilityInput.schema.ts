/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
;
import type { Prisma } from '@prisma/client';

;
import { PartApplicabilitySelectObjectSchema } from '../objects/PartApplicabilitySelect.schema';
import { PartApplicabilityIncludeObjectSchema } from '../objects/PartApplicabilityInclude.schema';
import { PartApplicabilityWhereUniqueInputObjectSchema } from '../objects/PartApplicabilityWhereUniqueInput.schema';
import { PartApplicabilityWhereInputObjectSchema } from '../objects/PartApplicabilityWhereInput.schema';
import { PartApplicabilityOrderByWithRelationInputObjectSchema } from '../objects/PartApplicabilityOrderByWithRelationInput.schema';
import { PartApplicabilityScalarFieldEnumSchema } from '../enums/PartApplicabilityScalarFieldEnum.schema';
import { PartApplicabilityCreateInputObjectSchema } from '../objects/PartApplicabilityCreateInput.schema';
import { PartApplicabilityUncheckedCreateInputObjectSchema } from '../objects/PartApplicabilityUncheckedCreateInput.schema';
import { PartApplicabilityCreateManyInputObjectSchema } from '../objects/PartApplicabilityCreateManyInput.schema';
import { PartApplicabilityUpdateInputObjectSchema } from '../objects/PartApplicabilityUpdateInput.schema';
import { PartApplicabilityUncheckedUpdateInputObjectSchema } from '../objects/PartApplicabilityUncheckedUpdateInput.schema';
import { PartApplicabilityUpdateManyMutationInputObjectSchema } from '../objects/PartApplicabilityUpdateManyMutationInput.schema';
import { PartApplicabilityUncheckedUpdateManyInputObjectSchema } from '../objects/PartApplicabilityUncheckedUpdateManyInput.schema';
import { PartApplicabilityCountAggregateInputObjectSchema } from '../objects/PartApplicabilityCountAggregateInput.schema';
import { PartApplicabilityMinAggregateInputObjectSchema } from '../objects/PartApplicabilityMinAggregateInput.schema';
import { PartApplicabilityMaxAggregateInputObjectSchema } from '../objects/PartApplicabilityMaxAggregateInput.schema';
import { PartApplicabilityAvgAggregateInputObjectSchema } from '../objects/PartApplicabilityAvgAggregateInput.schema';
import { PartApplicabilitySumAggregateInputObjectSchema } from '../objects/PartApplicabilitySumAggregateInput.schema';
import { PartApplicabilityOrderByWithAggregationInputObjectSchema } from '../objects/PartApplicabilityOrderByWithAggregationInput.schema';
import { PartApplicabilityScalarWhereWithAggregatesInputObjectSchema } from '../objects/PartApplicabilityScalarWhereWithAggregatesInput.schema'

type PartApplicabilityInputSchemaType = {
    findUnique: z.ZodType<Prisma.PartApplicabilityFindUniqueArgs>,
    findFirst: z.ZodType<Prisma.PartApplicabilityFindFirstArgs>,
    findMany: z.ZodType<Prisma.PartApplicabilityFindManyArgs>,
    create: z.ZodType<Prisma.PartApplicabilityCreateArgs>,
    createMany: z.ZodType<Prisma.PartApplicabilityCreateManyArgs>,
    delete: z.ZodType<Prisma.PartApplicabilityDeleteArgs>,
    deleteMany: z.ZodType<Prisma.PartApplicabilityDeleteManyArgs>,
    update: z.ZodType<Prisma.PartApplicabilityUpdateArgs>,
    updateMany: z.ZodType<Prisma.PartApplicabilityUpdateManyArgs>,
    upsert: z.ZodType<Prisma.PartApplicabilityUpsertArgs>,
    aggregate: z.ZodType<Prisma.PartApplicabilityAggregateArgs>,
    groupBy: z.ZodType<Prisma.PartApplicabilityGroupByArgs>,
    count: z.ZodType<Prisma.PartApplicabilityCountArgs>
}

export const PartApplicabilityInputSchema = {
    findUnique: z.object({
        select: z.lazy(() => PartApplicabilitySelectObjectSchema.optional()), include: z.lazy(() => PartApplicabilityIncludeObjectSchema.optional()), where: PartApplicabilityWhereUniqueInputObjectSchema
    }).strict(), findFirst: z.object({
        select: z.lazy(() => PartApplicabilitySelectObjectSchema.optional()), include: z.lazy(() => PartApplicabilityIncludeObjectSchema.optional()), where: PartApplicabilityWhereInputObjectSchema.optional(), orderBy: z.union([PartApplicabilityOrderByWithRelationInputObjectSchema, PartApplicabilityOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: PartApplicabilityWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(PartApplicabilityScalarFieldEnumSchema).optional()
    }).strict(), findMany: z.object({
        select: z.lazy(() => PartApplicabilitySelectObjectSchema.optional()), include: z.lazy(() => PartApplicabilityIncludeObjectSchema.optional()), where: PartApplicabilityWhereInputObjectSchema.optional(), orderBy: z.union([PartApplicabilityOrderByWithRelationInputObjectSchema, PartApplicabilityOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: PartApplicabilityWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(PartApplicabilityScalarFieldEnumSchema).optional()
    }).strict(), create: z.object({
        select: z.lazy(() => PartApplicabilitySelectObjectSchema.optional()), include: z.lazy(() => PartApplicabilityIncludeObjectSchema.optional()), data: z.union([PartApplicabilityCreateInputObjectSchema, PartApplicabilityUncheckedCreateInputObjectSchema])
    }).strict(), createMany: z.object({
        data: z.union([PartApplicabilityCreateManyInputObjectSchema, z.array(PartApplicabilityCreateManyInputObjectSchema)]), skipDuplicates: z.boolean().optional()
    }).strict(), 'delete': z.object({
        select: z.lazy(() => PartApplicabilitySelectObjectSchema.optional()), include: z.lazy(() => PartApplicabilityIncludeObjectSchema.optional()), where: PartApplicabilityWhereUniqueInputObjectSchema
    }).strict(), deleteMany: z.object({
        where: PartApplicabilityWhereInputObjectSchema.optional()
    }).strict(), update: z.object({
        select: z.lazy(() => PartApplicabilitySelectObjectSchema.optional()), include: z.lazy(() => PartApplicabilityIncludeObjectSchema.optional()), data: z.union([PartApplicabilityUpdateInputObjectSchema, PartApplicabilityUncheckedUpdateInputObjectSchema]), where: PartApplicabilityWhereUniqueInputObjectSchema
    }).strict(), updateMany: z.object({
        data: z.union([PartApplicabilityUpdateManyMutationInputObjectSchema, PartApplicabilityUncheckedUpdateManyInputObjectSchema]), where: PartApplicabilityWhereInputObjectSchema.optional()
    }).strict(), upsert: z.object({
        select: z.lazy(() => PartApplicabilitySelectObjectSchema.optional()), include: z.lazy(() => PartApplicabilityIncludeObjectSchema.optional()), where: PartApplicabilityWhereUniqueInputObjectSchema, create: z.union([PartApplicabilityCreateInputObjectSchema, PartApplicabilityUncheckedCreateInputObjectSchema]), update: z.union([PartApplicabilityUpdateInputObjectSchema, PartApplicabilityUncheckedUpdateInputObjectSchema])
    }).strict(), aggregate: z.object({
        where: PartApplicabilityWhereInputObjectSchema.optional(), orderBy: z.union([PartApplicabilityOrderByWithRelationInputObjectSchema, PartApplicabilityOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: PartApplicabilityWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), _count: z.union([z.literal(true), PartApplicabilityCountAggregateInputObjectSchema]).optional(), _min: PartApplicabilityMinAggregateInputObjectSchema.optional(), _max: PartApplicabilityMaxAggregateInputObjectSchema.optional(), _avg: PartApplicabilityAvgAggregateInputObjectSchema.optional(), _sum: PartApplicabilitySumAggregateInputObjectSchema.optional()
    }).strict(), groupBy: z.object({
        where: PartApplicabilityWhereInputObjectSchema.optional(), orderBy: z.union([PartApplicabilityOrderByWithAggregationInputObjectSchema, PartApplicabilityOrderByWithAggregationInputObjectSchema.array()]).optional(), having: PartApplicabilityScalarWhereWithAggregatesInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), by: z.array(PartApplicabilityScalarFieldEnumSchema), _count: z.union([z.literal(true), PartApplicabilityCountAggregateInputObjectSchema]).optional(), _min: PartApplicabilityMinAggregateInputObjectSchema.optional(), _max: PartApplicabilityMaxAggregateInputObjectSchema.optional(), _avg: PartApplicabilityAvgAggregateInputObjectSchema.optional(), _sum: PartApplicabilitySumAggregateInputObjectSchema.optional()
    }).strict(), count: z.object({
        where: PartApplicabilityWhereInputObjectSchema.optional(), orderBy: z.union([PartApplicabilityOrderByWithRelationInputObjectSchema, PartApplicabilityOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: PartApplicabilityWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(PartApplicabilityScalarFieldEnumSchema).optional(), select: z.union([z.literal(true), PartApplicabilityCountAggregateInputObjectSchema]).optional()
    }).strict(),
} as PartApplicabilityInputSchemaType;
