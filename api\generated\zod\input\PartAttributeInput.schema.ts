/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
;
import type { Prisma } from '@prisma/client';

;
import { PartAttributeSelectObjectSchema } from '../objects/PartAttributeSelect.schema';
import { PartAttributeIncludeObjectSchema } from '../objects/PartAttributeInclude.schema';
import { PartAttributeWhereUniqueInputObjectSchema } from '../objects/PartAttributeWhereUniqueInput.schema';
import { PartAttributeWhereInputObjectSchema } from '../objects/PartAttributeWhereInput.schema';
import { PartAttributeOrderByWithRelationInputObjectSchema } from '../objects/PartAttributeOrderByWithRelationInput.schema';
import { PartAttributeScalarFieldEnumSchema } from '../enums/PartAttributeScalarFieldEnum.schema';
import { PartAttributeCreateInputObjectSchema } from '../objects/PartAttributeCreateInput.schema';
import { PartAttributeUncheckedCreateInputObjectSchema } from '../objects/PartAttributeUncheckedCreateInput.schema';
import { PartAttributeCreateManyInputObjectSchema } from '../objects/PartAttributeCreateManyInput.schema';
import { PartAttributeUpdateInputObjectSchema } from '../objects/PartAttributeUpdateInput.schema';
import { PartAttributeUncheckedUpdateInputObjectSchema } from '../objects/PartAttributeUncheckedUpdateInput.schema';
import { PartAttributeUpdateManyMutationInputObjectSchema } from '../objects/PartAttributeUpdateManyMutationInput.schema';
import { PartAttributeUncheckedUpdateManyInputObjectSchema } from '../objects/PartAttributeUncheckedUpdateManyInput.schema';
import { PartAttributeCountAggregateInputObjectSchema } from '../objects/PartAttributeCountAggregateInput.schema';
import { PartAttributeMinAggregateInputObjectSchema } from '../objects/PartAttributeMinAggregateInput.schema';
import { PartAttributeMaxAggregateInputObjectSchema } from '../objects/PartAttributeMaxAggregateInput.schema';
import { PartAttributeAvgAggregateInputObjectSchema } from '../objects/PartAttributeAvgAggregateInput.schema';
import { PartAttributeSumAggregateInputObjectSchema } from '../objects/PartAttributeSumAggregateInput.schema';
import { PartAttributeOrderByWithAggregationInputObjectSchema } from '../objects/PartAttributeOrderByWithAggregationInput.schema';
import { PartAttributeScalarWhereWithAggregatesInputObjectSchema } from '../objects/PartAttributeScalarWhereWithAggregatesInput.schema'

type PartAttributeInputSchemaType = {
    findUnique: z.ZodType<Prisma.PartAttributeFindUniqueArgs>,
    findFirst: z.ZodType<Prisma.PartAttributeFindFirstArgs>,
    findMany: z.ZodType<Prisma.PartAttributeFindManyArgs>,
    create: z.ZodType<Prisma.PartAttributeCreateArgs>,
    createMany: z.ZodType<Prisma.PartAttributeCreateManyArgs>,
    delete: z.ZodType<Prisma.PartAttributeDeleteArgs>,
    deleteMany: z.ZodType<Prisma.PartAttributeDeleteManyArgs>,
    update: z.ZodType<Prisma.PartAttributeUpdateArgs>,
    updateMany: z.ZodType<Prisma.PartAttributeUpdateManyArgs>,
    upsert: z.ZodType<Prisma.PartAttributeUpsertArgs>,
    aggregate: z.ZodType<Prisma.PartAttributeAggregateArgs>,
    groupBy: z.ZodType<Prisma.PartAttributeGroupByArgs>,
    count: z.ZodType<Prisma.PartAttributeCountArgs>
}

export const PartAttributeInputSchema = {
    findUnique: z.object({
        select: z.lazy(() => PartAttributeSelectObjectSchema.optional()), include: z.lazy(() => PartAttributeIncludeObjectSchema.optional()), where: PartAttributeWhereUniqueInputObjectSchema
    }).strict(), findFirst: z.object({
        select: z.lazy(() => PartAttributeSelectObjectSchema.optional()), include: z.lazy(() => PartAttributeIncludeObjectSchema.optional()), where: PartAttributeWhereInputObjectSchema.optional(), orderBy: z.union([PartAttributeOrderByWithRelationInputObjectSchema, PartAttributeOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: PartAttributeWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(PartAttributeScalarFieldEnumSchema).optional()
    }).strict(), findMany: z.object({
        select: z.lazy(() => PartAttributeSelectObjectSchema.optional()), include: z.lazy(() => PartAttributeIncludeObjectSchema.optional()), where: PartAttributeWhereInputObjectSchema.optional(), orderBy: z.union([PartAttributeOrderByWithRelationInputObjectSchema, PartAttributeOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: PartAttributeWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(PartAttributeScalarFieldEnumSchema).optional()
    }).strict(), create: z.object({
        select: z.lazy(() => PartAttributeSelectObjectSchema.optional()), include: z.lazy(() => PartAttributeIncludeObjectSchema.optional()), data: z.union([PartAttributeCreateInputObjectSchema, PartAttributeUncheckedCreateInputObjectSchema])
    }).strict(), createMany: z.object({
        data: z.union([PartAttributeCreateManyInputObjectSchema, z.array(PartAttributeCreateManyInputObjectSchema)]), skipDuplicates: z.boolean().optional()
    }).strict(), 'delete': z.object({
        select: z.lazy(() => PartAttributeSelectObjectSchema.optional()), include: z.lazy(() => PartAttributeIncludeObjectSchema.optional()), where: PartAttributeWhereUniqueInputObjectSchema
    }).strict(), deleteMany: z.object({
        where: PartAttributeWhereInputObjectSchema.optional()
    }).strict(), update: z.object({
        select: z.lazy(() => PartAttributeSelectObjectSchema.optional()), include: z.lazy(() => PartAttributeIncludeObjectSchema.optional()), data: z.union([PartAttributeUpdateInputObjectSchema, PartAttributeUncheckedUpdateInputObjectSchema]), where: PartAttributeWhereUniqueInputObjectSchema
    }).strict(), updateMany: z.object({
        data: z.union([PartAttributeUpdateManyMutationInputObjectSchema, PartAttributeUncheckedUpdateManyInputObjectSchema]), where: PartAttributeWhereInputObjectSchema.optional()
    }).strict(), upsert: z.object({
        select: z.lazy(() => PartAttributeSelectObjectSchema.optional()), include: z.lazy(() => PartAttributeIncludeObjectSchema.optional()), where: PartAttributeWhereUniqueInputObjectSchema, create: z.union([PartAttributeCreateInputObjectSchema, PartAttributeUncheckedCreateInputObjectSchema]), update: z.union([PartAttributeUpdateInputObjectSchema, PartAttributeUncheckedUpdateInputObjectSchema])
    }).strict(), aggregate: z.object({
        where: PartAttributeWhereInputObjectSchema.optional(), orderBy: z.union([PartAttributeOrderByWithRelationInputObjectSchema, PartAttributeOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: PartAttributeWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), _count: z.union([z.literal(true), PartAttributeCountAggregateInputObjectSchema]).optional(), _min: PartAttributeMinAggregateInputObjectSchema.optional(), _max: PartAttributeMaxAggregateInputObjectSchema.optional(), _avg: PartAttributeAvgAggregateInputObjectSchema.optional(), _sum: PartAttributeSumAggregateInputObjectSchema.optional()
    }).strict(), groupBy: z.object({
        where: PartAttributeWhereInputObjectSchema.optional(), orderBy: z.union([PartAttributeOrderByWithAggregationInputObjectSchema, PartAttributeOrderByWithAggregationInputObjectSchema.array()]).optional(), having: PartAttributeScalarWhereWithAggregatesInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), by: z.array(PartAttributeScalarFieldEnumSchema), _count: z.union([z.literal(true), PartAttributeCountAggregateInputObjectSchema]).optional(), _min: PartAttributeMinAggregateInputObjectSchema.optional(), _max: PartAttributeMaxAggregateInputObjectSchema.optional(), _avg: PartAttributeAvgAggregateInputObjectSchema.optional(), _sum: PartAttributeSumAggregateInputObjectSchema.optional()
    }).strict(), count: z.object({
        where: PartAttributeWhereInputObjectSchema.optional(), orderBy: z.union([PartAttributeOrderByWithRelationInputObjectSchema, PartAttributeOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: PartAttributeWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(PartAttributeScalarFieldEnumSchema).optional(), select: z.union([z.literal(true), PartAttributeCountAggregateInputObjectSchema]).optional()
    }).strict(),
} as PartAttributeInputSchemaType;
