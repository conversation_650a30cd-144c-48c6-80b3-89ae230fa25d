/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
;
import type { Prisma } from '@prisma/client';

;
import { PartCategorySelectObjectSchema } from '../objects/PartCategorySelect.schema';
import { PartCategoryIncludeObjectSchema } from '../objects/PartCategoryInclude.schema';
import { PartCategoryWhereUniqueInputObjectSchema } from '../objects/PartCategoryWhereUniqueInput.schema';
import { PartCategoryWhereInputObjectSchema } from '../objects/PartCategoryWhereInput.schema';
import { PartCategoryOrderByWithRelationInputObjectSchema } from '../objects/PartCategoryOrderByWithRelationInput.schema';
import { PartCategoryScalarFieldEnumSchema } from '../enums/PartCategoryScalarFieldEnum.schema';
import { PartCategoryCreateInputObjectSchema } from '../objects/PartCategoryCreateInput.schema';
import { PartCategoryUncheckedCreateInputObjectSchema } from '../objects/PartCategoryUncheckedCreateInput.schema';
import { PartCategoryCreateManyInputObjectSchema } from '../objects/PartCategoryCreateManyInput.schema';
import { PartCategoryUpdateInputObjectSchema } from '../objects/PartCategoryUpdateInput.schema';
import { PartCategoryUncheckedUpdateInputObjectSchema } from '../objects/PartCategoryUncheckedUpdateInput.schema';
import { PartCategoryUpdateManyMutationInputObjectSchema } from '../objects/PartCategoryUpdateManyMutationInput.schema';
import { PartCategoryUncheckedUpdateManyInputObjectSchema } from '../objects/PartCategoryUncheckedUpdateManyInput.schema';
import { PartCategoryCountAggregateInputObjectSchema } from '../objects/PartCategoryCountAggregateInput.schema';
import { PartCategoryMinAggregateInputObjectSchema } from '../objects/PartCategoryMinAggregateInput.schema';
import { PartCategoryMaxAggregateInputObjectSchema } from '../objects/PartCategoryMaxAggregateInput.schema';
import { PartCategoryAvgAggregateInputObjectSchema } from '../objects/PartCategoryAvgAggregateInput.schema';
import { PartCategorySumAggregateInputObjectSchema } from '../objects/PartCategorySumAggregateInput.schema';
import { PartCategoryOrderByWithAggregationInputObjectSchema } from '../objects/PartCategoryOrderByWithAggregationInput.schema';
import { PartCategoryScalarWhereWithAggregatesInputObjectSchema } from '../objects/PartCategoryScalarWhereWithAggregatesInput.schema'

type PartCategoryInputSchemaType = {
    findUnique: z.ZodType<Prisma.PartCategoryFindUniqueArgs>,
    findFirst: z.ZodType<Prisma.PartCategoryFindFirstArgs>,
    findMany: z.ZodType<Prisma.PartCategoryFindManyArgs>,
    create: z.ZodType<Prisma.PartCategoryCreateArgs>,
    createMany: z.ZodType<Prisma.PartCategoryCreateManyArgs>,
    delete: z.ZodType<Prisma.PartCategoryDeleteArgs>,
    deleteMany: z.ZodType<Prisma.PartCategoryDeleteManyArgs>,
    update: z.ZodType<Prisma.PartCategoryUpdateArgs>,
    updateMany: z.ZodType<Prisma.PartCategoryUpdateManyArgs>,
    upsert: z.ZodType<Prisma.PartCategoryUpsertArgs>,
    aggregate: z.ZodType<Prisma.PartCategoryAggregateArgs>,
    groupBy: z.ZodType<Prisma.PartCategoryGroupByArgs>,
    count: z.ZodType<Prisma.PartCategoryCountArgs>
}

export const PartCategoryInputSchema = {
    findUnique: z.object({
        select: z.lazy(() => PartCategorySelectObjectSchema.optional()), include: z.lazy(() => PartCategoryIncludeObjectSchema.optional()), where: PartCategoryWhereUniqueInputObjectSchema
    }).strict(), findFirst: z.object({
        select: z.lazy(() => PartCategorySelectObjectSchema.optional()), include: z.lazy(() => PartCategoryIncludeObjectSchema.optional()), where: PartCategoryWhereInputObjectSchema.optional(), orderBy: z.union([PartCategoryOrderByWithRelationInputObjectSchema, PartCategoryOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: PartCategoryWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(PartCategoryScalarFieldEnumSchema).optional()
    }).strict(), findMany: z.object({
        select: z.lazy(() => PartCategorySelectObjectSchema.optional()), include: z.lazy(() => PartCategoryIncludeObjectSchema.optional()), where: PartCategoryWhereInputObjectSchema.optional(), orderBy: z.union([PartCategoryOrderByWithRelationInputObjectSchema, PartCategoryOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: PartCategoryWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(PartCategoryScalarFieldEnumSchema).optional()
    }).strict(), create: z.object({
        select: z.lazy(() => PartCategorySelectObjectSchema.optional()), include: z.lazy(() => PartCategoryIncludeObjectSchema.optional()), data: z.union([PartCategoryCreateInputObjectSchema, PartCategoryUncheckedCreateInputObjectSchema])
    }).strict(), createMany: z.object({
        data: z.union([PartCategoryCreateManyInputObjectSchema, z.array(PartCategoryCreateManyInputObjectSchema)]), skipDuplicates: z.boolean().optional()
    }).strict(), 'delete': z.object({
        select: z.lazy(() => PartCategorySelectObjectSchema.optional()), include: z.lazy(() => PartCategoryIncludeObjectSchema.optional()), where: PartCategoryWhereUniqueInputObjectSchema
    }).strict(), deleteMany: z.object({
        where: PartCategoryWhereInputObjectSchema.optional()
    }).strict(), update: z.object({
        select: z.lazy(() => PartCategorySelectObjectSchema.optional()), include: z.lazy(() => PartCategoryIncludeObjectSchema.optional()), data: z.union([PartCategoryUpdateInputObjectSchema, PartCategoryUncheckedUpdateInputObjectSchema]), where: PartCategoryWhereUniqueInputObjectSchema
    }).strict(), updateMany: z.object({
        data: z.union([PartCategoryUpdateManyMutationInputObjectSchema, PartCategoryUncheckedUpdateManyInputObjectSchema]), where: PartCategoryWhereInputObjectSchema.optional()
    }).strict(), upsert: z.object({
        select: z.lazy(() => PartCategorySelectObjectSchema.optional()), include: z.lazy(() => PartCategoryIncludeObjectSchema.optional()), where: PartCategoryWhereUniqueInputObjectSchema, create: z.union([PartCategoryCreateInputObjectSchema, PartCategoryUncheckedCreateInputObjectSchema]), update: z.union([PartCategoryUpdateInputObjectSchema, PartCategoryUncheckedUpdateInputObjectSchema])
    }).strict(), aggregate: z.object({
        where: PartCategoryWhereInputObjectSchema.optional(), orderBy: z.union([PartCategoryOrderByWithRelationInputObjectSchema, PartCategoryOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: PartCategoryWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), _count: z.union([z.literal(true), PartCategoryCountAggregateInputObjectSchema]).optional(), _min: PartCategoryMinAggregateInputObjectSchema.optional(), _max: PartCategoryMaxAggregateInputObjectSchema.optional(), _avg: PartCategoryAvgAggregateInputObjectSchema.optional(), _sum: PartCategorySumAggregateInputObjectSchema.optional()
    }).strict(), groupBy: z.object({
        where: PartCategoryWhereInputObjectSchema.optional(), orderBy: z.union([PartCategoryOrderByWithAggregationInputObjectSchema, PartCategoryOrderByWithAggregationInputObjectSchema.array()]).optional(), having: PartCategoryScalarWhereWithAggregatesInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), by: z.array(PartCategoryScalarFieldEnumSchema), _count: z.union([z.literal(true), PartCategoryCountAggregateInputObjectSchema]).optional(), _min: PartCategoryMinAggregateInputObjectSchema.optional(), _max: PartCategoryMaxAggregateInputObjectSchema.optional(), _avg: PartCategoryAvgAggregateInputObjectSchema.optional(), _sum: PartCategorySumAggregateInputObjectSchema.optional()
    }).strict(), count: z.object({
        where: PartCategoryWhereInputObjectSchema.optional(), orderBy: z.union([PartCategoryOrderByWithRelationInputObjectSchema, PartCategoryOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: PartCategoryWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(PartCategoryScalarFieldEnumSchema).optional(), select: z.union([z.literal(true), PartCategoryCountAggregateInputObjectSchema]).optional()
    }).strict(),
} as PartCategoryInputSchemaType;
