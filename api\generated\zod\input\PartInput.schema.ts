/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
;
import type { Prisma } from '@prisma/client';

;
import { PartSelectObjectSchema } from '../objects/PartSelect.schema';
import { PartIncludeObjectSchema } from '../objects/PartInclude.schema';
import { PartWhereUniqueInputObjectSchema } from '../objects/PartWhereUniqueInput.schema';
import { PartWhereInputObjectSchema } from '../objects/PartWhereInput.schema';
import { PartOrderByWithRelationInputObjectSchema } from '../objects/PartOrderByWithRelationInput.schema';
import { PartScalarFieldEnumSchema } from '../enums/PartScalarFieldEnum.schema';
import { PartCreateInputObjectSchema } from '../objects/PartCreateInput.schema';
import { PartUncheckedCreateInputObjectSchema } from '../objects/PartUncheckedCreateInput.schema';
import { PartCreateManyInputObjectSchema } from '../objects/PartCreateManyInput.schema';
import { PartUpdateInputObjectSchema } from '../objects/PartUpdateInput.schema';
import { PartUncheckedUpdateInputObjectSchema } from '../objects/PartUncheckedUpdateInput.schema';
import { PartUpdateManyMutationInputObjectSchema } from '../objects/PartUpdateManyMutationInput.schema';
import { PartUncheckedUpdateManyInputObjectSchema } from '../objects/PartUncheckedUpdateManyInput.schema';
import { PartCountAggregateInputObjectSchema } from '../objects/PartCountAggregateInput.schema';
import { PartMinAggregateInputObjectSchema } from '../objects/PartMinAggregateInput.schema';
import { PartMaxAggregateInputObjectSchema } from '../objects/PartMaxAggregateInput.schema';
import { PartAvgAggregateInputObjectSchema } from '../objects/PartAvgAggregateInput.schema';
import { PartSumAggregateInputObjectSchema } from '../objects/PartSumAggregateInput.schema';
import { PartOrderByWithAggregationInputObjectSchema } from '../objects/PartOrderByWithAggregationInput.schema';
import { PartScalarWhereWithAggregatesInputObjectSchema } from '../objects/PartScalarWhereWithAggregatesInput.schema'

type PartInputSchemaType = {
    findUnique: z.ZodType<Prisma.PartFindUniqueArgs>,
    findFirst: z.ZodType<Prisma.PartFindFirstArgs>,
    findMany: z.ZodType<Prisma.PartFindManyArgs>,
    create: z.ZodType<Prisma.PartCreateArgs>,
    createMany: z.ZodType<Prisma.PartCreateManyArgs>,
    delete: z.ZodType<Prisma.PartDeleteArgs>,
    deleteMany: z.ZodType<Prisma.PartDeleteManyArgs>,
    update: z.ZodType<Prisma.PartUpdateArgs>,
    updateMany: z.ZodType<Prisma.PartUpdateManyArgs>,
    upsert: z.ZodType<Prisma.PartUpsertArgs>,
    aggregate: z.ZodType<Prisma.PartAggregateArgs>,
    groupBy: z.ZodType<Prisma.PartGroupByArgs>,
    count: z.ZodType<Prisma.PartCountArgs>
}

export const PartInputSchema = {
    findUnique: z.object({
        select: z.lazy(() => PartSelectObjectSchema.optional()), include: z.lazy(() => PartIncludeObjectSchema.optional()), where: PartWhereUniqueInputObjectSchema
    }).strict(), findFirst: z.object({
        select: z.lazy(() => PartSelectObjectSchema.optional()), include: z.lazy(() => PartIncludeObjectSchema.optional()), where: PartWhereInputObjectSchema.optional(), orderBy: z.union([PartOrderByWithRelationInputObjectSchema, PartOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: PartWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(PartScalarFieldEnumSchema).optional()
    }).strict(), findMany: z.object({
        select: z.lazy(() => PartSelectObjectSchema.optional()), include: z.lazy(() => PartIncludeObjectSchema.optional()), where: PartWhereInputObjectSchema.optional(), orderBy: z.union([PartOrderByWithRelationInputObjectSchema, PartOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: PartWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(PartScalarFieldEnumSchema).optional()
    }).strict(), create: z.object({
        select: z.lazy(() => PartSelectObjectSchema.optional()), include: z.lazy(() => PartIncludeObjectSchema.optional()), data: z.union([PartCreateInputObjectSchema, PartUncheckedCreateInputObjectSchema])
    }).strict(), createMany: z.object({
        data: z.union([PartCreateManyInputObjectSchema, z.array(PartCreateManyInputObjectSchema)]), skipDuplicates: z.boolean().optional()
    }).strict(), 'delete': z.object({
        select: z.lazy(() => PartSelectObjectSchema.optional()), include: z.lazy(() => PartIncludeObjectSchema.optional()), where: PartWhereUniqueInputObjectSchema
    }).strict(), deleteMany: z.object({
        where: PartWhereInputObjectSchema.optional()
    }).strict(), update: z.object({
        select: z.lazy(() => PartSelectObjectSchema.optional()), include: z.lazy(() => PartIncludeObjectSchema.optional()), data: z.union([PartUpdateInputObjectSchema, PartUncheckedUpdateInputObjectSchema]), where: PartWhereUniqueInputObjectSchema
    }).strict(), updateMany: z.object({
        data: z.union([PartUpdateManyMutationInputObjectSchema, PartUncheckedUpdateManyInputObjectSchema]), where: PartWhereInputObjectSchema.optional()
    }).strict(), upsert: z.object({
        select: z.lazy(() => PartSelectObjectSchema.optional()), include: z.lazy(() => PartIncludeObjectSchema.optional()), where: PartWhereUniqueInputObjectSchema, create: z.union([PartCreateInputObjectSchema, PartUncheckedCreateInputObjectSchema]), update: z.union([PartUpdateInputObjectSchema, PartUncheckedUpdateInputObjectSchema])
    }).strict(), aggregate: z.object({
        where: PartWhereInputObjectSchema.optional(), orderBy: z.union([PartOrderByWithRelationInputObjectSchema, PartOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: PartWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), _count: z.union([z.literal(true), PartCountAggregateInputObjectSchema]).optional(), _min: PartMinAggregateInputObjectSchema.optional(), _max: PartMaxAggregateInputObjectSchema.optional(), _avg: PartAvgAggregateInputObjectSchema.optional(), _sum: PartSumAggregateInputObjectSchema.optional()
    }).strict(), groupBy: z.object({
        where: PartWhereInputObjectSchema.optional(), orderBy: z.union([PartOrderByWithAggregationInputObjectSchema, PartOrderByWithAggregationInputObjectSchema.array()]).optional(), having: PartScalarWhereWithAggregatesInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), by: z.array(PartScalarFieldEnumSchema), _count: z.union([z.literal(true), PartCountAggregateInputObjectSchema]).optional(), _min: PartMinAggregateInputObjectSchema.optional(), _max: PartMaxAggregateInputObjectSchema.optional(), _avg: PartAvgAggregateInputObjectSchema.optional(), _sum: PartSumAggregateInputObjectSchema.optional()
    }).strict(), count: z.object({
        where: PartWhereInputObjectSchema.optional(), orderBy: z.union([PartOrderByWithRelationInputObjectSchema, PartOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: PartWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(PartScalarFieldEnumSchema).optional(), select: z.union([z.literal(true), PartCountAggregateInputObjectSchema]).optional()
    }).strict(),
} as PartInputSchemaType;
