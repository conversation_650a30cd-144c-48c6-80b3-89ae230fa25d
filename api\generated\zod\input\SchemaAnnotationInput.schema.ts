/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
;
import type { Prisma } from '@prisma/client';

;
import { SchemaAnnotationSelectObjectSchema } from '../objects/SchemaAnnotationSelect.schema';
import { SchemaAnnotationIncludeObjectSchema } from '../objects/SchemaAnnotationInclude.schema';
import { SchemaAnnotationWhereUniqueInputObjectSchema } from '../objects/SchemaAnnotationWhereUniqueInput.schema';
import { SchemaAnnotationWhereInputObjectSchema } from '../objects/SchemaAnnotationWhereInput.schema';
import { SchemaAnnotationOrderByWithRelationInputObjectSchema } from '../objects/SchemaAnnotationOrderByWithRelationInput.schema';
import { SchemaAnnotationScalarFieldEnumSchema } from '../enums/SchemaAnnotationScalarFieldEnum.schema';
import { SchemaAnnotationCreateInputObjectSchema } from '../objects/SchemaAnnotationCreateInput.schema';
import { SchemaAnnotationUncheckedCreateInputObjectSchema } from '../objects/SchemaAnnotationUncheckedCreateInput.schema';
import { SchemaAnnotationCreateManyInputObjectSchema } from '../objects/SchemaAnnotationCreateManyInput.schema';
import { SchemaAnnotationUpdateInputObjectSchema } from '../objects/SchemaAnnotationUpdateInput.schema';
import { SchemaAnnotationUncheckedUpdateInputObjectSchema } from '../objects/SchemaAnnotationUncheckedUpdateInput.schema';
import { SchemaAnnotationUpdateManyMutationInputObjectSchema } from '../objects/SchemaAnnotationUpdateManyMutationInput.schema';
import { SchemaAnnotationUncheckedUpdateManyInputObjectSchema } from '../objects/SchemaAnnotationUncheckedUpdateManyInput.schema';
import { SchemaAnnotationCountAggregateInputObjectSchema } from '../objects/SchemaAnnotationCountAggregateInput.schema';
import { SchemaAnnotationMinAggregateInputObjectSchema } from '../objects/SchemaAnnotationMinAggregateInput.schema';
import { SchemaAnnotationMaxAggregateInputObjectSchema } from '../objects/SchemaAnnotationMaxAggregateInput.schema';
import { SchemaAnnotationAvgAggregateInputObjectSchema } from '../objects/SchemaAnnotationAvgAggregateInput.schema';
import { SchemaAnnotationSumAggregateInputObjectSchema } from '../objects/SchemaAnnotationSumAggregateInput.schema';
import { SchemaAnnotationOrderByWithAggregationInputObjectSchema } from '../objects/SchemaAnnotationOrderByWithAggregationInput.schema';
import { SchemaAnnotationScalarWhereWithAggregatesInputObjectSchema } from '../objects/SchemaAnnotationScalarWhereWithAggregatesInput.schema'

type SchemaAnnotationInputSchemaType = {
    findUnique: z.ZodType<Prisma.SchemaAnnotationFindUniqueArgs>,
    findFirst: z.ZodType<Prisma.SchemaAnnotationFindFirstArgs>,
    findMany: z.ZodType<Prisma.SchemaAnnotationFindManyArgs>,
    create: z.ZodType<Prisma.SchemaAnnotationCreateArgs>,
    createMany: z.ZodType<Prisma.SchemaAnnotationCreateManyArgs>,
    delete: z.ZodType<Prisma.SchemaAnnotationDeleteArgs>,
    deleteMany: z.ZodType<Prisma.SchemaAnnotationDeleteManyArgs>,
    update: z.ZodType<Prisma.SchemaAnnotationUpdateArgs>,
    updateMany: z.ZodType<Prisma.SchemaAnnotationUpdateManyArgs>,
    upsert: z.ZodType<Prisma.SchemaAnnotationUpsertArgs>,
    aggregate: z.ZodType<Prisma.SchemaAnnotationAggregateArgs>,
    groupBy: z.ZodType<Prisma.SchemaAnnotationGroupByArgs>,
    count: z.ZodType<Prisma.SchemaAnnotationCountArgs>
}

export const SchemaAnnotationInputSchema = {
    findUnique: z.object({
        select: z.lazy(() => SchemaAnnotationSelectObjectSchema.optional()), include: z.lazy(() => SchemaAnnotationIncludeObjectSchema.optional()), where: SchemaAnnotationWhereUniqueInputObjectSchema
    }).strict(), findFirst: z.object({
        select: z.lazy(() => SchemaAnnotationSelectObjectSchema.optional()), include: z.lazy(() => SchemaAnnotationIncludeObjectSchema.optional()), where: SchemaAnnotationWhereInputObjectSchema.optional(), orderBy: z.union([SchemaAnnotationOrderByWithRelationInputObjectSchema, SchemaAnnotationOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: SchemaAnnotationWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(SchemaAnnotationScalarFieldEnumSchema).optional()
    }).strict(), findMany: z.object({
        select: z.lazy(() => SchemaAnnotationSelectObjectSchema.optional()), include: z.lazy(() => SchemaAnnotationIncludeObjectSchema.optional()), where: SchemaAnnotationWhereInputObjectSchema.optional(), orderBy: z.union([SchemaAnnotationOrderByWithRelationInputObjectSchema, SchemaAnnotationOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: SchemaAnnotationWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(SchemaAnnotationScalarFieldEnumSchema).optional()
    }).strict(), create: z.object({
        select: z.lazy(() => SchemaAnnotationSelectObjectSchema.optional()), include: z.lazy(() => SchemaAnnotationIncludeObjectSchema.optional()), data: z.union([SchemaAnnotationCreateInputObjectSchema, SchemaAnnotationUncheckedCreateInputObjectSchema])
    }).strict(), createMany: z.object({
        data: z.union([SchemaAnnotationCreateManyInputObjectSchema, z.array(SchemaAnnotationCreateManyInputObjectSchema)]), skipDuplicates: z.boolean().optional()
    }).strict(), 'delete': z.object({
        select: z.lazy(() => SchemaAnnotationSelectObjectSchema.optional()), include: z.lazy(() => SchemaAnnotationIncludeObjectSchema.optional()), where: SchemaAnnotationWhereUniqueInputObjectSchema
    }).strict(), deleteMany: z.object({
        where: SchemaAnnotationWhereInputObjectSchema.optional()
    }).strict(), update: z.object({
        select: z.lazy(() => SchemaAnnotationSelectObjectSchema.optional()), include: z.lazy(() => SchemaAnnotationIncludeObjectSchema.optional()), data: z.union([SchemaAnnotationUpdateInputObjectSchema, SchemaAnnotationUncheckedUpdateInputObjectSchema]), where: SchemaAnnotationWhereUniqueInputObjectSchema
    }).strict(), updateMany: z.object({
        data: z.union([SchemaAnnotationUpdateManyMutationInputObjectSchema, SchemaAnnotationUncheckedUpdateManyInputObjectSchema]), where: SchemaAnnotationWhereInputObjectSchema.optional()
    }).strict(), upsert: z.object({
        select: z.lazy(() => SchemaAnnotationSelectObjectSchema.optional()), include: z.lazy(() => SchemaAnnotationIncludeObjectSchema.optional()), where: SchemaAnnotationWhereUniqueInputObjectSchema, create: z.union([SchemaAnnotationCreateInputObjectSchema, SchemaAnnotationUncheckedCreateInputObjectSchema]), update: z.union([SchemaAnnotationUpdateInputObjectSchema, SchemaAnnotationUncheckedUpdateInputObjectSchema])
    }).strict(), aggregate: z.object({
        where: SchemaAnnotationWhereInputObjectSchema.optional(), orderBy: z.union([SchemaAnnotationOrderByWithRelationInputObjectSchema, SchemaAnnotationOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: SchemaAnnotationWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), _count: z.union([z.literal(true), SchemaAnnotationCountAggregateInputObjectSchema]).optional(), _min: SchemaAnnotationMinAggregateInputObjectSchema.optional(), _max: SchemaAnnotationMaxAggregateInputObjectSchema.optional(), _avg: SchemaAnnotationAvgAggregateInputObjectSchema.optional(), _sum: SchemaAnnotationSumAggregateInputObjectSchema.optional()
    }).strict(), groupBy: z.object({
        where: SchemaAnnotationWhereInputObjectSchema.optional(), orderBy: z.union([SchemaAnnotationOrderByWithAggregationInputObjectSchema, SchemaAnnotationOrderByWithAggregationInputObjectSchema.array()]).optional(), having: SchemaAnnotationScalarWhereWithAggregatesInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), by: z.array(SchemaAnnotationScalarFieldEnumSchema), _count: z.union([z.literal(true), SchemaAnnotationCountAggregateInputObjectSchema]).optional(), _min: SchemaAnnotationMinAggregateInputObjectSchema.optional(), _max: SchemaAnnotationMaxAggregateInputObjectSchema.optional(), _avg: SchemaAnnotationAvgAggregateInputObjectSchema.optional(), _sum: SchemaAnnotationSumAggregateInputObjectSchema.optional()
    }).strict(), count: z.object({
        where: SchemaAnnotationWhereInputObjectSchema.optional(), orderBy: z.union([SchemaAnnotationOrderByWithRelationInputObjectSchema, SchemaAnnotationOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: SchemaAnnotationWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(SchemaAnnotationScalarFieldEnumSchema).optional(), select: z.union([z.literal(true), SchemaAnnotationCountAggregateInputObjectSchema]).optional()
    }).strict(),
} as SchemaAnnotationInputSchemaType;
