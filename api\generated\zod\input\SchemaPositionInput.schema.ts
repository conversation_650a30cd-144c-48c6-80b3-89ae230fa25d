/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
;
import type { Prisma } from '@prisma/client';

;
import { SchemaPositionSelectObjectSchema } from '../objects/SchemaPositionSelect.schema';
import { SchemaPositionIncludeObjectSchema } from '../objects/SchemaPositionInclude.schema';
import { SchemaPositionWhereUniqueInputObjectSchema } from '../objects/SchemaPositionWhereUniqueInput.schema';
import { SchemaPositionWhereInputObjectSchema } from '../objects/SchemaPositionWhereInput.schema';
import { SchemaPositionOrderByWithRelationInputObjectSchema } from '../objects/SchemaPositionOrderByWithRelationInput.schema';
import { SchemaPositionScalarFieldEnumSchema } from '../enums/SchemaPositionScalarFieldEnum.schema';
import { SchemaPositionCreateInputObjectSchema } from '../objects/SchemaPositionCreateInput.schema';
import { SchemaPositionUncheckedCreateInputObjectSchema } from '../objects/SchemaPositionUncheckedCreateInput.schema';
import { SchemaPositionCreateManyInputObjectSchema } from '../objects/SchemaPositionCreateManyInput.schema';
import { SchemaPositionUpdateInputObjectSchema } from '../objects/SchemaPositionUpdateInput.schema';
import { SchemaPositionUncheckedUpdateInputObjectSchema } from '../objects/SchemaPositionUncheckedUpdateInput.schema';
import { SchemaPositionUpdateManyMutationInputObjectSchema } from '../objects/SchemaPositionUpdateManyMutationInput.schema';
import { SchemaPositionUncheckedUpdateManyInputObjectSchema } from '../objects/SchemaPositionUncheckedUpdateManyInput.schema';
import { SchemaPositionCountAggregateInputObjectSchema } from '../objects/SchemaPositionCountAggregateInput.schema';
import { SchemaPositionMinAggregateInputObjectSchema } from '../objects/SchemaPositionMinAggregateInput.schema';
import { SchemaPositionMaxAggregateInputObjectSchema } from '../objects/SchemaPositionMaxAggregateInput.schema';
import { SchemaPositionAvgAggregateInputObjectSchema } from '../objects/SchemaPositionAvgAggregateInput.schema';
import { SchemaPositionSumAggregateInputObjectSchema } from '../objects/SchemaPositionSumAggregateInput.schema';
import { SchemaPositionOrderByWithAggregationInputObjectSchema } from '../objects/SchemaPositionOrderByWithAggregationInput.schema';
import { SchemaPositionScalarWhereWithAggregatesInputObjectSchema } from '../objects/SchemaPositionScalarWhereWithAggregatesInput.schema'

type SchemaPositionInputSchemaType = {
    findUnique: z.ZodType<Prisma.SchemaPositionFindUniqueArgs>,
    findFirst: z.ZodType<Prisma.SchemaPositionFindFirstArgs>,
    findMany: z.ZodType<Prisma.SchemaPositionFindManyArgs>,
    create: z.ZodType<Prisma.SchemaPositionCreateArgs>,
    createMany: z.ZodType<Prisma.SchemaPositionCreateManyArgs>,
    delete: z.ZodType<Prisma.SchemaPositionDeleteArgs>,
    deleteMany: z.ZodType<Prisma.SchemaPositionDeleteManyArgs>,
    update: z.ZodType<Prisma.SchemaPositionUpdateArgs>,
    updateMany: z.ZodType<Prisma.SchemaPositionUpdateManyArgs>,
    upsert: z.ZodType<Prisma.SchemaPositionUpsertArgs>,
    aggregate: z.ZodType<Prisma.SchemaPositionAggregateArgs>,
    groupBy: z.ZodType<Prisma.SchemaPositionGroupByArgs>,
    count: z.ZodType<Prisma.SchemaPositionCountArgs>
}

export const SchemaPositionInputSchema = {
    findUnique: z.object({
        select: z.lazy(() => SchemaPositionSelectObjectSchema.optional()), include: z.lazy(() => SchemaPositionIncludeObjectSchema.optional()), where: SchemaPositionWhereUniqueInputObjectSchema
    }).strict(), findFirst: z.object({
        select: z.lazy(() => SchemaPositionSelectObjectSchema.optional()), include: z.lazy(() => SchemaPositionIncludeObjectSchema.optional()), where: SchemaPositionWhereInputObjectSchema.optional(), orderBy: z.union([SchemaPositionOrderByWithRelationInputObjectSchema, SchemaPositionOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: SchemaPositionWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(SchemaPositionScalarFieldEnumSchema).optional()
    }).strict(), findMany: z.object({
        select: z.lazy(() => SchemaPositionSelectObjectSchema.optional()), include: z.lazy(() => SchemaPositionIncludeObjectSchema.optional()), where: SchemaPositionWhereInputObjectSchema.optional(), orderBy: z.union([SchemaPositionOrderByWithRelationInputObjectSchema, SchemaPositionOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: SchemaPositionWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(SchemaPositionScalarFieldEnumSchema).optional()
    }).strict(), create: z.object({
        select: z.lazy(() => SchemaPositionSelectObjectSchema.optional()), include: z.lazy(() => SchemaPositionIncludeObjectSchema.optional()), data: z.union([SchemaPositionCreateInputObjectSchema, SchemaPositionUncheckedCreateInputObjectSchema])
    }).strict(), createMany: z.object({
        data: z.union([SchemaPositionCreateManyInputObjectSchema, z.array(SchemaPositionCreateManyInputObjectSchema)]), skipDuplicates: z.boolean().optional()
    }).strict(), 'delete': z.object({
        select: z.lazy(() => SchemaPositionSelectObjectSchema.optional()), include: z.lazy(() => SchemaPositionIncludeObjectSchema.optional()), where: SchemaPositionWhereUniqueInputObjectSchema
    }).strict(), deleteMany: z.object({
        where: SchemaPositionWhereInputObjectSchema.optional()
    }).strict(), update: z.object({
        select: z.lazy(() => SchemaPositionSelectObjectSchema.optional()), include: z.lazy(() => SchemaPositionIncludeObjectSchema.optional()), data: z.union([SchemaPositionUpdateInputObjectSchema, SchemaPositionUncheckedUpdateInputObjectSchema]), where: SchemaPositionWhereUniqueInputObjectSchema
    }).strict(), updateMany: z.object({
        data: z.union([SchemaPositionUpdateManyMutationInputObjectSchema, SchemaPositionUncheckedUpdateManyInputObjectSchema]), where: SchemaPositionWhereInputObjectSchema.optional()
    }).strict(), upsert: z.object({
        select: z.lazy(() => SchemaPositionSelectObjectSchema.optional()), include: z.lazy(() => SchemaPositionIncludeObjectSchema.optional()), where: SchemaPositionWhereUniqueInputObjectSchema, create: z.union([SchemaPositionCreateInputObjectSchema, SchemaPositionUncheckedCreateInputObjectSchema]), update: z.union([SchemaPositionUpdateInputObjectSchema, SchemaPositionUncheckedUpdateInputObjectSchema])
    }).strict(), aggregate: z.object({
        where: SchemaPositionWhereInputObjectSchema.optional(), orderBy: z.union([SchemaPositionOrderByWithRelationInputObjectSchema, SchemaPositionOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: SchemaPositionWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), _count: z.union([z.literal(true), SchemaPositionCountAggregateInputObjectSchema]).optional(), _min: SchemaPositionMinAggregateInputObjectSchema.optional(), _max: SchemaPositionMaxAggregateInputObjectSchema.optional(), _avg: SchemaPositionAvgAggregateInputObjectSchema.optional(), _sum: SchemaPositionSumAggregateInputObjectSchema.optional()
    }).strict(), groupBy: z.object({
        where: SchemaPositionWhereInputObjectSchema.optional(), orderBy: z.union([SchemaPositionOrderByWithAggregationInputObjectSchema, SchemaPositionOrderByWithAggregationInputObjectSchema.array()]).optional(), having: SchemaPositionScalarWhereWithAggregatesInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), by: z.array(SchemaPositionScalarFieldEnumSchema), _count: z.union([z.literal(true), SchemaPositionCountAggregateInputObjectSchema]).optional(), _min: SchemaPositionMinAggregateInputObjectSchema.optional(), _max: SchemaPositionMaxAggregateInputObjectSchema.optional(), _avg: SchemaPositionAvgAggregateInputObjectSchema.optional(), _sum: SchemaPositionSumAggregateInputObjectSchema.optional()
    }).strict(), count: z.object({
        where: SchemaPositionWhereInputObjectSchema.optional(), orderBy: z.union([SchemaPositionOrderByWithRelationInputObjectSchema, SchemaPositionOrderByWithRelationInputObjectSchema.array()]).optional(), cursor: SchemaPositionWhereUniqueInputObjectSchema.optional(), take: z.number().optional(), skip: z.number().optional(), distinct: z.array(SchemaPositionScalarFieldEnumSchema).optional(), select: z.union([z.literal(true), SchemaPositionCountAggregateInputObjectSchema]).optional()
    }).strict(),
} as SchemaPositionInputSchemaType;
