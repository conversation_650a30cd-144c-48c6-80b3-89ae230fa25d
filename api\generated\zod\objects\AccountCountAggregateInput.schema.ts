/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AccountCountAggregateInputType>;
export const AccountCountAggregateInputObjectSchema: SchemaType = z.object({
    id: z.literal(true).optional().optional(), accountId: z.literal(true).optional().optional(), providerId: z.literal(true).optional().optional(), userId: z.literal(true).optional().optional(), accessToken: z.literal(true).optional().optional(), refreshToken: z.literal(true).optional().optional(), idToken: z.literal(true).optional().optional(), accessTokenExpiresAt: z.literal(true).optional().optional(), refreshTokenExpiresAt: z.literal(true).optional().optional(), scope: z.literal(true).optional().optional(), password: z.literal(true).optional().optional(), createdAt: z.literal(true).optional().optional(), updatedAt: z.literal(true).optional().optional(), _all: z.literal(true).optional().optional()
}).strict() as SchemaType;
