/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { AccountSelectObjectSchema } from './AccountSelect.schema';
import { AccountIncludeObjectSchema } from './AccountInclude.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AccountDefaultArgs>;
export const AccountDefaultArgsObjectSchema: SchemaType = z.object({
    select: z.lazy(() => AccountSelectObjectSchema).optional().optional(), include: z.lazy(() => AccountIncludeObjectSchema).optional().optional()
}).strict() as SchemaType;
