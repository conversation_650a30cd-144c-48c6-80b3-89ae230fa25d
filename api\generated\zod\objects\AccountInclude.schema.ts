/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { UserDefaultArgsObjectSchema } from './UserDefaultArgs.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AccountInclude>;
export const AccountIncludeObjectSchema: SchemaType = z.object({
    user: z.union([z.boolean(),
    z.lazy(() => UserDefaultArgsObjectSchema)]).optional()
}).strict() as SchemaType;
