/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { UserDefaultArgsObjectSchema } from './UserDefaultArgs.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AccountSelect>;
export const AccountSelectObjectSchema: SchemaType = z.object({
    id: z.boolean().optional().optional(), accountId: z.boolean().optional().optional(), providerId: z.boolean().optional().optional(), userId: z.boolean().optional().optional(), accessToken: z.boolean().optional().optional(), refreshToken: z.boolean().optional().optional(), idToken: z.boolean().optional().optional(), accessTokenExpiresAt: z.boolean().optional().optional(), refreshTokenExpiresAt: z.boolean().optional().optional(), scope: z.boolean().optional().optional(), password: z.boolean().optional().optional(), createdAt: z.boolean().optional().optional(), updatedAt: z.boolean().optional().optional(), user: z.union([z.boolean(),
    z.lazy(() => UserDefaultArgsObjectSchema)]).optional()
}).strict() as SchemaType;
