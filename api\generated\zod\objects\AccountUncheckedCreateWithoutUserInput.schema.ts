/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AccountUncheckedCreateWithoutUserInput>;
export const AccountUncheckedCreateWithoutUserInputObjectSchema: SchemaType = z.object({
    id: z.string(), accountId: z.string(), providerId: z.string(), accessToken: z.union([z.string(),
    z.null()]).optional().nullable(), refreshToken: z.union([z.string(),
    z.null()]).optional().nullable(), idToken: z.union([z.string(),
    z.null()]).optional().nullable(), accessTokenExpiresAt: z.union([z.union([z.date(), z.string().datetime().optional()]),
    z.null()]).optional().nullable(), refreshTokenExpiresAt: z.union([z.union([z.date(), z.string().datetime().optional()]),
    z.null()]).optional().nullable(), scope: z.union([z.string(),
    z.null()]).optional().nullable(), password: z.union([z.string(),
    z.null()]).optional().nullable(), createdAt: z.union([z.date(), z.string().datetime()]), updatedAt: z.union([z.date(), z.string().datetime()])
}).strict() as SchemaType;
