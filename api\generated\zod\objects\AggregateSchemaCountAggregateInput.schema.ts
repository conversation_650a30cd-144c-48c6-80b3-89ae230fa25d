/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AggregateSchemaCountAggregateInputType>;
export const AggregateSchemaCountAggregateInputObjectSchema: SchemaType = z.object({
    id: z.literal(true).optional().optional(), name: z.literal(true).optional().optional(), description: z.literal(true).optional().optional(), partId: z.literal(true).optional().optional(), imageUrl: z.literal(true).optional().optional(), imageWidth: z.literal(true).optional().optional(), imageHeight: z.literal(true).optional().optional(), svgContent: z.literal(true).optional().optional(), isActive: z.literal(true).optional().optional(), sortOrder: z.literal(true).optional().optional(), createdAt: z.literal(true).optional().optional(), updatedAt: z.literal(true).optional().optional(), _all: z.literal(true).optional().optional()
}).strict() as SchemaType;
