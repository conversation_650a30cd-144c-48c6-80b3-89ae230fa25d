/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { AggregateSchemaCountOutputTypeSelectObjectSchema } from './AggregateSchemaCountOutputTypeSelect.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AggregateSchemaCountOutputTypeDefaultArgs>;
export const AggregateSchemaCountOutputTypeDefaultArgsObjectSchema: SchemaType = z.object({
    select: z.lazy(() => AggregateSchemaCountOutputTypeSelectObjectSchema).optional().optional()
}).strict() as SchemaType;
