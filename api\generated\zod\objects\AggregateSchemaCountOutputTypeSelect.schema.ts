/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AggregateSchemaCountOutputTypeSelect>;
export const AggregateSchemaCountOutputTypeSelectObjectSchema: SchemaType = z.object({
    positions: z.boolean().optional().optional(), annotations: z.boolean().optional().optional()
}).strict() as SchemaType;
