/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { AggregateSchemaWhereUniqueInputObjectSchema } from './AggregateSchemaWhereUniqueInput.schema';
import { AggregateSchemaCreateWithoutAnnotationsInputObjectSchema } from './AggregateSchemaCreateWithoutAnnotationsInput.schema';
import { AggregateSchemaUncheckedCreateWithoutAnnotationsInputObjectSchema } from './AggregateSchemaUncheckedCreateWithoutAnnotationsInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AggregateSchemaCreateOrConnectWithoutAnnotationsInput>;
export const AggregateSchemaCreateOrConnectWithoutAnnotationsInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => AggregateSchemaWhereUniqueInputObjectSchema), create: z.union([z.lazy(() => AggregateSchemaCreateWithoutAnnotationsInputObjectSchema), z.lazy(() => AggregateSchemaUncheckedCreateWithoutAnnotationsInputObjectSchema)])
}).strict() as SchemaType;
