/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { AggregateSchemaWhereUniqueInputObjectSchema } from './AggregateSchemaWhereUniqueInput.schema';
import { AggregateSchemaCreateWithoutPositionsInputObjectSchema } from './AggregateSchemaCreateWithoutPositionsInput.schema';
import { AggregateSchemaUncheckedCreateWithoutPositionsInputObjectSchema } from './AggregateSchemaUncheckedCreateWithoutPositionsInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AggregateSchemaCreateOrConnectWithoutPositionsInput>;
export const AggregateSchemaCreateOrConnectWithoutPositionsInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => AggregateSchemaWhereUniqueInputObjectSchema), create: z.union([z.lazy(() => AggregateSchemaCreateWithoutPositionsInputObjectSchema), z.lazy(() => AggregateSchemaUncheckedCreateWithoutPositionsInputObjectSchema)])
}).strict() as SchemaType;
