/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { AggregateSchemaSelectObjectSchema } from './AggregateSchemaSelect.schema';
import { AggregateSchemaIncludeObjectSchema } from './AggregateSchemaInclude.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AggregateSchemaDefaultArgs>;
export const AggregateSchemaDefaultArgsObjectSchema: SchemaType = z.object({
    select: z.lazy(() => AggregateSchemaSelectObjectSchema).optional().optional(), include: z.lazy(() => AggregateSchemaIncludeObjectSchema).optional().optional()
}).strict() as SchemaType;
