/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartDefaultArgsObjectSchema } from './PartDefaultArgs.schema';
import { SchemaPositionInputSchema } from '../input/SchemaPositionInput.schema';
import { SchemaAnnotationInputSchema } from '../input/SchemaAnnotationInput.schema';
import { AggregateSchemaCountOutputTypeDefaultArgsObjectSchema } from './AggregateSchemaCountOutputTypeDefaultArgs.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AggregateSchemaInclude>;
export const AggregateSchemaIncludeObjectSchema: SchemaType = z.object({
    part: z.union([z.boolean(),
    z.lazy(() => PartDefaultArgsObjectSchema)]).optional(), positions: z.union([z.boolean(),
    z.lazy(() => SchemaPositionInputSchema.findMany)]).optional(), annotations: z.union([z.boolean(),
    z.lazy(() => SchemaAnnotationInputSchema.findMany)]).optional(), _count: z.union([z.boolean(),
    z.lazy(() => AggregateSchemaCountOutputTypeDefaultArgsObjectSchema)]).optional()
}).strict() as SchemaType;
