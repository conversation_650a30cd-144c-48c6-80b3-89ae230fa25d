/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartDefaultArgsObjectSchema } from './PartDefaultArgs.schema';
import { SchemaPositionInputSchema } from '../input/SchemaPositionInput.schema';
import { SchemaAnnotationInputSchema } from '../input/SchemaAnnotationInput.schema';
import { AggregateSchemaCountOutputTypeDefaultArgsObjectSchema } from './AggregateSchemaCountOutputTypeDefaultArgs.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AggregateSchemaSelect>;
export const AggregateSchemaSelectObjectSchema: SchemaType = z.object({
    id: z.boolean().optional().optional(), name: z.boolean().optional().optional(), description: z.boolean().optional().optional(), part: z.union([z.boolean(),
    z.lazy(() => PartDefaultArgsObjectSchema)]).optional(), partId: z.boolean().optional().optional(), imageUrl: z.boolean().optional().optional(), imageWidth: z.boolean().optional().optional(), imageHeight: z.boolean().optional().optional(), svgContent: z.boolean().optional().optional(), isActive: z.boolean().optional().optional(), sortOrder: z.boolean().optional().optional(), positions: z.union([z.boolean(),
    z.lazy(() => SchemaPositionInputSchema.findMany)]).optional(), annotations: z.union([z.boolean(),
    z.lazy(() => SchemaAnnotationInputSchema.findMany)]).optional(), createdAt: z.boolean().optional().optional(), updatedAt: z.boolean().optional().optional(), _count: z.union([z.boolean(),
    z.lazy(() => AggregateSchemaCountOutputTypeDefaultArgsObjectSchema)]).optional()
}).strict() as SchemaType;
