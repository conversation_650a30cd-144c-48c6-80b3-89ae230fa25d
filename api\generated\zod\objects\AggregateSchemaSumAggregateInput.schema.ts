/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AggregateSchemaSumAggregateInputType>;
export const AggregateSchemaSumAggregateInputObjectSchema: SchemaType = z.object({
    partId: z.literal(true).optional().optional(), imageWidth: z.literal(true).optional().optional(), imageHeight: z.literal(true).optional().optional(), sortOrder: z.literal(true).optional().optional()
}).strict() as SchemaType;
