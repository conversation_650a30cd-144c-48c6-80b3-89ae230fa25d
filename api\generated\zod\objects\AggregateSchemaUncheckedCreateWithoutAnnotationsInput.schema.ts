/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { SchemaPositionUncheckedCreateNestedManyWithoutSchemaInputObjectSchema } from './SchemaPositionUncheckedCreateNestedManyWithoutSchemaInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AggregateSchemaUncheckedCreateWithoutAnnotationsInput>;
export const AggregateSchemaUncheckedCreateWithoutAnnotationsInputObjectSchema: SchemaType = z.object({
    id: z.string().optional().optional(), name: z.string(), description: z.union([z.string(),
    z.null()]).optional().nullable(), partId: z.union([z.number(),
    z.null()]).optional().nullable(), imageUrl: z.union([z.string(),
    z.null()]).optional().nullable(), imageWidth: z.union([z.number(),
    z.null()]).optional().nullable(), imageHeight: z.union([z.number(),
    z.null()]).optional().nullable(), svgContent: z.union([z.string(),
    z.null()]).optional().nullable(), isActive: z.boolean().optional().optional(), sortOrder: z.number().optional().optional(), createdAt: z.union([z.date().optional(), z.string().datetime().optional()]).optional(), updatedAt: z.union([z.date().optional(), z.string().datetime().optional()]).optional(), positions: z.lazy(() => SchemaPositionUncheckedCreateNestedManyWithoutSchemaInputObjectSchema).optional().optional()
}).strict() as SchemaType;
