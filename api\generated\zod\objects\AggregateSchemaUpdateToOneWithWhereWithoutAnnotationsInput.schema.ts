/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { AggregateSchemaWhereInputObjectSchema } from './AggregateSchemaWhereInput.schema';
import { AggregateSchemaUpdateWithoutAnnotationsInputObjectSchema } from './AggregateSchemaUpdateWithoutAnnotationsInput.schema';
import { AggregateSchemaUncheckedUpdateWithoutAnnotationsInputObjectSchema } from './AggregateSchemaUncheckedUpdateWithoutAnnotationsInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AggregateSchemaUpdateToOneWithWhereWithoutAnnotationsInput>;
export const AggregateSchemaUpdateToOneWithWhereWithoutAnnotationsInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => AggregateSchemaWhereInputObjectSchema).optional().optional(), data: z.union([z.lazy(() => AggregateSchemaUpdateWithoutAnnotationsInputObjectSchema), z.lazy(() => AggregateSchemaUncheckedUpdateWithoutAnnotationsInputObjectSchema)])
}).strict() as SchemaType;
