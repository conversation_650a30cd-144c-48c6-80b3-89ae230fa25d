/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { AggregateSchemaWhereInputObjectSchema } from './AggregateSchemaWhereInput.schema';
import { AggregateSchemaUpdateWithoutPositionsInputObjectSchema } from './AggregateSchemaUpdateWithoutPositionsInput.schema';
import { AggregateSchemaUncheckedUpdateWithoutPositionsInputObjectSchema } from './AggregateSchemaUncheckedUpdateWithoutPositionsInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AggregateSchemaUpdateToOneWithWhereWithoutPositionsInput>;
export const AggregateSchemaUpdateToOneWithWhereWithoutPositionsInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => AggregateSchemaWhereInputObjectSchema).optional().optional(), data: z.union([z.lazy(() => AggregateSchemaUpdateWithoutPositionsInputObjectSchema), z.lazy(() => AggregateSchemaUncheckedUpdateWithoutPositionsInputObjectSchema)])
}).strict() as SchemaType;
