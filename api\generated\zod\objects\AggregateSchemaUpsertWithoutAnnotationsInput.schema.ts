/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { AggregateSchemaUpdateWithoutAnnotationsInputObjectSchema } from './AggregateSchemaUpdateWithoutAnnotationsInput.schema';
import { AggregateSchemaUncheckedUpdateWithoutAnnotationsInputObjectSchema } from './AggregateSchemaUncheckedUpdateWithoutAnnotationsInput.schema';
import { AggregateSchemaCreateWithoutAnnotationsInputObjectSchema } from './AggregateSchemaCreateWithoutAnnotationsInput.schema';
import { AggregateSchemaUncheckedCreateWithoutAnnotationsInputObjectSchema } from './AggregateSchemaUncheckedCreateWithoutAnnotationsInput.schema';
import { AggregateSchemaWhereInputObjectSchema } from './AggregateSchemaWhereInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AggregateSchemaUpsertWithoutAnnotationsInput>;
export const AggregateSchemaUpsertWithoutAnnotationsInputObjectSchema: SchemaType = z.object({
    update: z.union([z.lazy(() => AggregateSchemaUpdateWithoutAnnotationsInputObjectSchema), z.lazy(() => AggregateSchemaUncheckedUpdateWithoutAnnotationsInputObjectSchema)]), create: z.union([z.lazy(() => AggregateSchemaCreateWithoutAnnotationsInputObjectSchema), z.lazy(() => AggregateSchemaUncheckedCreateWithoutAnnotationsInputObjectSchema)]), where: z.lazy(() => AggregateSchemaWhereInputObjectSchema).optional().optional()
}).strict() as SchemaType;
