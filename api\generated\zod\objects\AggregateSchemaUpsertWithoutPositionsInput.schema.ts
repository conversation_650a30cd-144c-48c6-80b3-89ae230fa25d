/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { AggregateSchemaUpdateWithoutPositionsInputObjectSchema } from './AggregateSchemaUpdateWithoutPositionsInput.schema';
import { AggregateSchemaUncheckedUpdateWithoutPositionsInputObjectSchema } from './AggregateSchemaUncheckedUpdateWithoutPositionsInput.schema';
import { AggregateSchemaCreateWithoutPositionsInputObjectSchema } from './AggregateSchemaCreateWithoutPositionsInput.schema';
import { AggregateSchemaUncheckedCreateWithoutPositionsInputObjectSchema } from './AggregateSchemaUncheckedCreateWithoutPositionsInput.schema';
import { AggregateSchemaWhereInputObjectSchema } from './AggregateSchemaWhereInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AggregateSchemaUpsertWithoutPositionsInput>;
export const AggregateSchemaUpsertWithoutPositionsInputObjectSchema: SchemaType = z.object({
    update: z.union([z.lazy(() => AggregateSchemaUpdateWithoutPositionsInputObjectSchema), z.lazy(() => AggregateSchemaUncheckedUpdateWithoutPositionsInputObjectSchema)]), create: z.union([z.lazy(() => AggregateSchemaCreateWithoutPositionsInputObjectSchema), z.lazy(() => AggregateSchemaUncheckedCreateWithoutPositionsInputObjectSchema)]), where: z.lazy(() => AggregateSchemaWhereInputObjectSchema).optional().optional()
}).strict() as SchemaType;
