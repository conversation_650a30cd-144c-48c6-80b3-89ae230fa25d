/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AttributeGroupAvgAggregateInputType>;
export const AttributeGroupAvgAggregateInputObjectSchema: SchemaType = z.object({
    id: z.literal(true).optional().optional(), parentId: z.literal(true).optional().optional()
}).strict() as SchemaType;
