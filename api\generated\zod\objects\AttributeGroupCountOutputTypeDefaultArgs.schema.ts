/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { AttributeGroupCountOutputTypeSelectObjectSchema } from './AttributeGroupCountOutputTypeSelect.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AttributeGroupCountOutputTypeDefaultArgs>;
export const AttributeGroupCountOutputTypeDefaultArgsObjectSchema: SchemaType = z.object({
    select: z.lazy(() => AttributeGroupCountOutputTypeSelectObjectSchema).optional().optional()
}).strict() as SchemaType;
