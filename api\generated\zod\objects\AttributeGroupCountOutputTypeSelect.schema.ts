/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AttributeGroupCountOutputTypeSelect>;
export const AttributeGroupCountOutputTypeSelectObjectSchema: SchemaType = z.object({
    templates: z.boolean().optional().optional(), children: z.boolean().optional().optional()
}).strict() as SchemaType;
