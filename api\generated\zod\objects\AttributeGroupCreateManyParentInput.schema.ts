/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AttributeGroupCreateManyParentInput>;
export const AttributeGroupCreateManyParentInputObjectSchema: SchemaType = z.object({
    id: z.number().optional().optional(), name: z.string(), description: z.union([z.string(),
    z.null()]).optional().nullable()
}).strict() as SchemaType;
