/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { AttributeGroupSelectObjectSchema } from './AttributeGroupSelect.schema';
import { AttributeGroupIncludeObjectSchema } from './AttributeGroupInclude.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AttributeGroupDefaultArgs>;
export const AttributeGroupDefaultArgsObjectSchema: SchemaType = z.object({
    select: z.lazy(() => AttributeGroupSelectObjectSchema).optional().optional(), include: z.lazy(() => AttributeGroupIncludeObjectSchema).optional().optional()
}).strict() as SchemaType;
