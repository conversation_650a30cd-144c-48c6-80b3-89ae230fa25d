/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { AttributeTemplateInputSchema } from '../input/AttributeTemplateInput.schema';
import { AttributeGroupDefaultArgsObjectSchema } from './AttributeGroupDefaultArgs.schema';
import { AttributeGroupInputSchema } from '../input/AttributeGroupInput.schema';
import { AttributeGroupCountOutputTypeDefaultArgsObjectSchema } from './AttributeGroupCountOutputTypeDefaultArgs.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AttributeGroupInclude>;
export const AttributeGroupIncludeObjectSchema: SchemaType = z.object({
    templates: z.union([z.boolean(),
    z.lazy(() => AttributeTemplateInputSchema.findMany)]).optional(), parent: z.union([z.boolean(),
    z.lazy(() => AttributeGroupDefaultArgsObjectSchema)]).optional(), children: z.union([z.boolean(),
    z.lazy(() => AttributeGroupInputSchema.findMany)]).optional(), _count: z.union([z.boolean(),
    z.lazy(() => AttributeGroupCountOutputTypeDefaultArgsObjectSchema)]).optional()
}).strict() as SchemaType;
