/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { IntFieldUpdateOperationsInputObjectSchema } from './IntFieldUpdateOperationsInput.schema';
import { StringFieldUpdateOperationsInputObjectSchema } from './StringFieldUpdateOperationsInput.schema';
import { NullableStringFieldUpdateOperationsInputObjectSchema } from './NullableStringFieldUpdateOperationsInput.schema';
import { AttributeTemplateUncheckedUpdateManyWithoutGroupNestedInputObjectSchema } from './AttributeTemplateUncheckedUpdateManyWithoutGroupNestedInput.schema';
import { AttributeGroupUncheckedUpdateManyWithoutParentNestedInputObjectSchema } from './AttributeGroupUncheckedUpdateManyWithoutParentNestedInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AttributeGroupUncheckedUpdateWithoutParentInput>;
export const AttributeGroupUncheckedUpdateWithoutParentInputObjectSchema: SchemaType = z.object({
    id: z.union([z.number(),
    z.lazy(() => IntFieldUpdateOperationsInputObjectSchema)]).optional(), name: z.union([z.string(),
    z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(), description: z.union([z.string(),
    z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
    z.null()]).optional().nullable(), templates: z.lazy(() => AttributeTemplateUncheckedUpdateManyWithoutGroupNestedInputObjectSchema).optional().optional(), children: z.lazy(() => AttributeGroupUncheckedUpdateManyWithoutParentNestedInputObjectSchema).optional().optional()
}).strict() as SchemaType;
