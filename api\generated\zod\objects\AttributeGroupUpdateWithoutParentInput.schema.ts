/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { StringFieldUpdateOperationsInputObjectSchema } from './StringFieldUpdateOperationsInput.schema';
import { NullableStringFieldUpdateOperationsInputObjectSchema } from './NullableStringFieldUpdateOperationsInput.schema';
import { AttributeTemplateUpdateManyWithoutGroupNestedInputObjectSchema } from './AttributeTemplateUpdateManyWithoutGroupNestedInput.schema';
import { AttributeGroupUpdateManyWithoutParentNestedInputObjectSchema } from './AttributeGroupUpdateManyWithoutParentNestedInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AttributeGroupUpdateWithoutParentInput>;
export const AttributeGroupUpdateWithoutParentInputObjectSchema: SchemaType = z.object({
    name: z.union([z.string(),
    z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(), description: z.union([z.string(),
    z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
    z.null()]).optional().nullable(), templates: z.lazy(() => AttributeTemplateUpdateManyWithoutGroupNestedInputObjectSchema).optional().optional(), children: z.lazy(() => AttributeGroupUpdateManyWithoutParentNestedInputObjectSchema).optional().optional()
}).strict() as SchemaType;
