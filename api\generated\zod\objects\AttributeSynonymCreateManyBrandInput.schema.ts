/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { SynonymCompatibilityLevelSchema } from '../enums/SynonymCompatibilityLevel.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AttributeSynonymCreateManyBrandInput>;
export const AttributeSynonymCreateManyBrandInputObjectSchema: SchemaType = z.object({
    id: z.number().optional().optional(), value: z.string(), groupId: z.number(), notes: z.union([z.string(),
    z.null()]).optional().nullable(), compatibilityLevel: z.union([z.lazy(() => SynonymCompatibilityLevelSchema),
    z.null()]).optional().nullable()
}).strict() as SchemaType;
