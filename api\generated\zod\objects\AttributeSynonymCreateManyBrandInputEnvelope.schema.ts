/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { AttributeSynonymCreateManyBrandInputObjectSchema } from './AttributeSynonymCreateManyBrandInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AttributeSynonymCreateManyBrandInputEnvelope>;
export const AttributeSynonymCreateManyBrandInputEnvelopeObjectSchema: SchemaType = z.object({
    data: z.union([z.lazy(() => AttributeSynonymCreateManyBrandInputObjectSchema),
    z.lazy(() => AttributeSynonymCreateManyBrandInputObjectSchema).array()]), skipDuplicates: z.boolean().optional().optional()
}).strict() as SchemaType;
