/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { SynonymCompatibilityLevelSchema } from '../enums/SynonymCompatibilityLevel.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AttributeSynonymCreateManyGroupInput>;
export const AttributeSynonymCreateManyGroupInputObjectSchema: SchemaType = z.object({
    id: z.number().optional().optional(), value: z.string(), notes: z.union([z.string(),
    z.null()]).optional().nullable(), brandId: z.union([z.number(),
    z.null()]).optional().nullable(), compatibilityLevel: z.union([z.lazy(() => SynonymCompatibilityLevelSchema),
    z.null()]).optional().nullable()
}).strict() as SchemaType;
