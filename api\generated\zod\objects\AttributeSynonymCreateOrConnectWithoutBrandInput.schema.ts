/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { AttributeSynonymWhereUniqueInputObjectSchema } from './AttributeSynonymWhereUniqueInput.schema';
import { AttributeSynonymCreateWithoutBrandInputObjectSchema } from './AttributeSynonymCreateWithoutBrandInput.schema';
import { AttributeSynonymUncheckedCreateWithoutBrandInputObjectSchema } from './AttributeSynonymUncheckedCreateWithoutBrandInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AttributeSynonymCreateOrConnectWithoutBrandInput>;
export const AttributeSynonymCreateOrConnectWithoutBrandInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => AttributeSynonymWhereUniqueInputObjectSchema), create: z.union([z.lazy(() => AttributeSynonymCreateWithoutBrandInputObjectSchema), z.lazy(() => AttributeSynonymUncheckedCreateWithoutBrandInputObjectSchema)])
}).strict() as SchemaType;
