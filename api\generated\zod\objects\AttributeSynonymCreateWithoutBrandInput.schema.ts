/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { SynonymCompatibilityLevelSchema } from '../enums/SynonymCompatibilityLevel.schema';
import { AttributeSynonymGroupCreateNestedOneWithoutSynonymsInputObjectSchema } from './AttributeSynonymGroupCreateNestedOneWithoutSynonymsInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AttributeSynonymCreateWithoutBrandInput>;
export const AttributeSynonymCreateWithoutBrandInputObjectSchema: SchemaType = z.object({
    value: z.string(), notes: z.union([z.string(),
    z.null()]).optional().nullable(), compatibilityLevel: z.union([z.lazy(() => SynonymCompatibilityLevelSchema),
    z.null()]).optional().nullable(), group: z.lazy(() => AttributeSynonymGroupCreateNestedOneWithoutSynonymsInputObjectSchema)
}).strict() as SchemaType;
