/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { AttributeSynonymSelectObjectSchema } from './AttributeSynonymSelect.schema';
import { AttributeSynonymIncludeObjectSchema } from './AttributeSynonymInclude.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AttributeSynonymDefaultArgs>;
export const AttributeSynonymDefaultArgsObjectSchema: SchemaType = z.object({
    select: z.lazy(() => AttributeSynonymSelectObjectSchema).optional().optional(), include: z.lazy(() => AttributeSynonymIncludeObjectSchema).optional().optional()
}).strict() as SchemaType;
