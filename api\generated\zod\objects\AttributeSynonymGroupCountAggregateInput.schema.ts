/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AttributeSynonymGroupCountAggregateInputType>;
export const AttributeSynonymGroupCountAggregateInputObjectSchema: SchemaType = z.object({
    id: z.literal(true).optional().optional(), name: z.literal(true).optional().optional(), description: z.literal(true).optional().optional(), templateId: z.literal(true).optional().optional(), parentId: z.literal(true).optional().optional(), canonicalValue: z.literal(true).optional().optional(), compatibilityLevel: z.literal(true).optional().optional(), notes: z.literal(true).optional().optional(), _all: z.literal(true).optional().optional()
}).strict() as SchemaType;
