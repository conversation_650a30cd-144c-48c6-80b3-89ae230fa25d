/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { AttributeSynonymGroupCountOutputTypeSelectObjectSchema } from './AttributeSynonymGroupCountOutputTypeSelect.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AttributeSynonymGroupCountOutputTypeDefaultArgs>;
export const AttributeSynonymGroupCountOutputTypeDefaultArgsObjectSchema: SchemaType = z.object({
    select: z.lazy(() => AttributeSynonymGroupCountOutputTypeSelectObjectSchema).optional().optional()
}).strict() as SchemaType;
