/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AttributeSynonymGroupCountOutputTypeSelect>;
export const AttributeSynonymGroupCountOutputTypeSelectObjectSchema: SchemaType = z.object({
    children: z.boolean().optional().optional(), synonyms: z.boolean().optional().optional()
}).strict() as SchemaType;
