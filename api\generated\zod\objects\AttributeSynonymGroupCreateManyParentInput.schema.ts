/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { SynonymCompatibilityLevelSchema } from '../enums/SynonymCompatibilityLevel.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AttributeSynonymGroupCreateManyParentInput>;
export const AttributeSynonymGroupCreateManyParentInputObjectSchema: SchemaType = z.object({
    id: z.number().optional().optional(), name: z.string(), description: z.union([z.string(),
    z.null()]).optional().nullable(), templateId: z.number(), canonicalValue: z.union([z.string(),
    z.null()]).optional().nullable(), compatibilityLevel: z.lazy(() => SynonymCompatibilityLevelSchema).optional().optional(), notes: z.union([z.string(),
    z.null()]).optional().nullable()
}).strict() as SchemaType;
