/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { SynonymCompatibilityLevelSchema } from '../enums/SynonymCompatibilityLevel.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AttributeSynonymGroupCreateManyTemplateInput>;
export const AttributeSynonymGroupCreateManyTemplateInputObjectSchema: SchemaType = z.object({
    id: z.number().optional().optional(), name: z.string(), description: z.union([z.string(),
    z.null()]).optional().nullable(), parentId: z.union([z.number(),
    z.null()]).optional().nullable(), canonicalValue: z.union([z.string(),
    z.null()]).optional().nullable(), compatibilityLevel: z.lazy(() => SynonymCompatibilityLevelSchema).optional().optional(), notes: z.union([z.string(),
    z.null()]).optional().nullable()
}).strict() as SchemaType;
