/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { AttributeSynonymGroupSelectObjectSchema } from './AttributeSynonymGroupSelect.schema';
import { AttributeSynonymGroupIncludeObjectSchema } from './AttributeSynonymGroupInclude.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AttributeSynonymGroupDefaultArgs>;
export const AttributeSynonymGroupDefaultArgsObjectSchema: SchemaType = z.object({
    select: z.lazy(() => AttributeSynonymGroupSelectObjectSchema).optional().optional(), include: z.lazy(() => AttributeSynonymGroupIncludeObjectSchema).optional().optional()
}).strict() as SchemaType;
