/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { AttributeTemplateDefaultArgsObjectSchema } from './AttributeTemplateDefaultArgs.schema';
import { AttributeSynonymGroupDefaultArgsObjectSchema } from './AttributeSynonymGroupDefaultArgs.schema';
import { AttributeSynonymGroupInputSchema } from '../input/AttributeSynonymGroupInput.schema';
import { AttributeSynonymInputSchema } from '../input/AttributeSynonymInput.schema';
import { AttributeSynonymGroupCountOutputTypeDefaultArgsObjectSchema } from './AttributeSynonymGroupCountOutputTypeDefaultArgs.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AttributeSynonymGroupInclude>;
export const AttributeSynonymGroupIncludeObjectSchema: SchemaType = z.object({
    template: z.union([z.boolean(),
    z.lazy(() => AttributeTemplateDefaultArgsObjectSchema)]).optional(), parent: z.union([z.boolean(),
    z.lazy(() => AttributeSynonymGroupDefaultArgsObjectSchema)]).optional(), children: z.union([z.boolean(),
    z.lazy(() => AttributeSynonymGroupInputSchema.findMany)]).optional(), synonyms: z.union([z.boolean(),
    z.lazy(() => AttributeSynonymInputSchema.findMany)]).optional(), _count: z.union([z.boolean(),
    z.lazy(() => AttributeSynonymGroupCountOutputTypeDefaultArgsObjectSchema)]).optional()
}).strict() as SchemaType;
