/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { AttributeTemplateDefaultArgsObjectSchema } from './AttributeTemplateDefaultArgs.schema';
import { AttributeSynonymGroupDefaultArgsObjectSchema } from './AttributeSynonymGroupDefaultArgs.schema';
import { AttributeSynonymGroupInputSchema } from '../input/AttributeSynonymGroupInput.schema';
import { AttributeSynonymInputSchema } from '../input/AttributeSynonymInput.schema';
import { AttributeSynonymGroupCountOutputTypeDefaultArgsObjectSchema } from './AttributeSynonymGroupCountOutputTypeDefaultArgs.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AttributeSynonymGroupSelect>;
export const AttributeSynonymGroupSelectObjectSchema: SchemaType = z.object({
    id: z.boolean().optional().optional(), name: z.boolean().optional().optional(), description: z.boolean().optional().optional(), template: z.union([z.boolean(),
    z.lazy(() => AttributeTemplateDefaultArgsObjectSchema)]).optional(), templateId: z.boolean().optional().optional(), parent: z.union([z.boolean(),
    z.lazy(() => AttributeSynonymGroupDefaultArgsObjectSchema)]).optional(), parentId: z.boolean().optional().optional(), children: z.union([z.boolean(),
    z.lazy(() => AttributeSynonymGroupInputSchema.findMany)]).optional(), canonicalValue: z.boolean().optional().optional(), synonyms: z.union([z.boolean(),
    z.lazy(() => AttributeSynonymInputSchema.findMany)]).optional(), compatibilityLevel: z.boolean().optional().optional(), notes: z.boolean().optional().optional(), _count: z.union([z.boolean(),
    z.lazy(() => AttributeSynonymGroupCountOutputTypeDefaultArgsObjectSchema)]).optional()
}).strict() as SchemaType;
