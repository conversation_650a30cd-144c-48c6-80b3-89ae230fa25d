/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { IntFieldUpdateOperationsInputObjectSchema } from './IntFieldUpdateOperationsInput.schema';
import { StringFieldUpdateOperationsInputObjectSchema } from './StringFieldUpdateOperationsInput.schema';
import { NullableStringFieldUpdateOperationsInputObjectSchema } from './NullableStringFieldUpdateOperationsInput.schema';
import { NullableIntFieldUpdateOperationsInputObjectSchema } from './NullableIntFieldUpdateOperationsInput.schema';
import { SynonymCompatibilityLevelSchema } from '../enums/SynonymCompatibilityLevel.schema';
import { EnumSynonymCompatibilityLevelFieldUpdateOperationsInputObjectSchema } from './EnumSynonymCompatibilityLevelFieldUpdateOperationsInput.schema';
import { AttributeSynonymGroupUncheckedUpdateManyWithoutParentNestedInputObjectSchema } from './AttributeSynonymGroupUncheckedUpdateManyWithoutParentNestedInput.schema';
import { AttributeSynonymUncheckedUpdateManyWithoutGroupNestedInputObjectSchema } from './AttributeSynonymUncheckedUpdateManyWithoutGroupNestedInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AttributeSynonymGroupUncheckedUpdateWithoutTemplateInput>;
export const AttributeSynonymGroupUncheckedUpdateWithoutTemplateInputObjectSchema: SchemaType = z.object({
    id: z.union([z.number(),
    z.lazy(() => IntFieldUpdateOperationsInputObjectSchema)]).optional(), name: z.union([z.string(),
    z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(), description: z.union([z.string(),
    z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
    z.null()]).optional().nullable(), parentId: z.union([z.number(),
    z.lazy(() => NullableIntFieldUpdateOperationsInputObjectSchema),
    z.null()]).optional().nullable(), canonicalValue: z.union([z.string(),
    z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
    z.null()]).optional().nullable(), compatibilityLevel: z.union([z.lazy(() => SynonymCompatibilityLevelSchema),
    z.lazy(() => EnumSynonymCompatibilityLevelFieldUpdateOperationsInputObjectSchema)]).optional(), notes: z.union([z.string(),
    z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
    z.null()]).optional().nullable(), children: z.lazy(() => AttributeSynonymGroupUncheckedUpdateManyWithoutParentNestedInputObjectSchema).optional().optional(), synonyms: z.lazy(() => AttributeSynonymUncheckedUpdateManyWithoutGroupNestedInputObjectSchema).optional().optional()
}).strict() as SchemaType;
