/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { StringFieldUpdateOperationsInputObjectSchema } from './StringFieldUpdateOperationsInput.schema';
import { NullableStringFieldUpdateOperationsInputObjectSchema } from './NullableStringFieldUpdateOperationsInput.schema';
import { SynonymCompatibilityLevelSchema } from '../enums/SynonymCompatibilityLevel.schema';
import { EnumSynonymCompatibilityLevelFieldUpdateOperationsInputObjectSchema } from './EnumSynonymCompatibilityLevelFieldUpdateOperationsInput.schema';
import { AttributeSynonymGroupUpdateOneWithoutChildrenNestedInputObjectSchema } from './AttributeSynonymGroupUpdateOneWithoutChildrenNestedInput.schema';
import { AttributeSynonymGroupUpdateManyWithoutParentNestedInputObjectSchema } from './AttributeSynonymGroupUpdateManyWithoutParentNestedInput.schema';
import { AttributeSynonymUpdateManyWithoutGroupNestedInputObjectSchema } from './AttributeSynonymUpdateManyWithoutGroupNestedInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AttributeSynonymGroupUpdateWithoutTemplateInput>;
export const AttributeSynonymGroupUpdateWithoutTemplateInputObjectSchema: SchemaType = z.object({
    name: z.union([z.string(),
    z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(), description: z.union([z.string(),
    z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
    z.null()]).optional().nullable(), canonicalValue: z.union([z.string(),
    z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
    z.null()]).optional().nullable(), compatibilityLevel: z.union([z.lazy(() => SynonymCompatibilityLevelSchema),
    z.lazy(() => EnumSynonymCompatibilityLevelFieldUpdateOperationsInputObjectSchema)]).optional(), notes: z.union([z.string(),
    z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
    z.null()]).optional().nullable(), parent: z.lazy(() => AttributeSynonymGroupUpdateOneWithoutChildrenNestedInputObjectSchema).optional().optional(), children: z.lazy(() => AttributeSynonymGroupUpdateManyWithoutParentNestedInputObjectSchema).optional().optional(), synonyms: z.lazy(() => AttributeSynonymUpdateManyWithoutGroupNestedInputObjectSchema).optional().optional()
}).strict() as SchemaType;
