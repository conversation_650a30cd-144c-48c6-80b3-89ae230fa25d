/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { AttributeSynonymGroupDefaultArgsObjectSchema } from './AttributeSynonymGroupDefaultArgs.schema';
import { BrandDefaultArgsObjectSchema } from './BrandDefaultArgs.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AttributeSynonymInclude>;
export const AttributeSynonymIncludeObjectSchema: SchemaType = z.object({
    group: z.union([z.boolean(),
    z.lazy(() => AttributeSynonymGroupDefaultArgsObjectSchema)]).optional(), brand: z.union([z.boolean(),
    z.lazy(() => BrandDefaultArgsObjectSchema)]).optional()
}).strict() as SchemaType;
