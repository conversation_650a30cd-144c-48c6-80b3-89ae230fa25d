/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AttributeSynonymMinAggregateInputType>;
export const AttributeSynonymMinAggregateInputObjectSchema: SchemaType = z.object({
    id: z.literal(true).optional().optional(), value: z.literal(true).optional().optional(), groupId: z.literal(true).optional().optional(), notes: z.literal(true).optional().optional(), brandId: z.literal(true).optional().optional(), compatibilityLevel: z.literal(true).optional().optional()
}).strict() as SchemaType;
