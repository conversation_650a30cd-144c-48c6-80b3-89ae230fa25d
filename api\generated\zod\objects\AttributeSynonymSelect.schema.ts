/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { AttributeSynonymGroupDefaultArgsObjectSchema } from './AttributeSynonymGroupDefaultArgs.schema';
import { BrandDefaultArgsObjectSchema } from './BrandDefaultArgs.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AttributeSynonymSelect>;
export const AttributeSynonymSelectObjectSchema: SchemaType = z.object({
    id: z.boolean().optional().optional(), value: z.boolean().optional().optional(), group: z.union([z.boolean(),
    z.lazy(() => AttributeSynonymGroupDefaultArgsObjectSchema)]).optional(), groupId: z.boolean().optional().optional(), notes: z.boolean().optional().optional(), brand: z.union([z.boolean(),
    z.lazy(() => BrandDefaultArgsObjectSchema)]).optional(), brandId: z.boolean().optional().optional(), compatibilityLevel: z.boolean().optional().optional()
}).strict() as SchemaType;
