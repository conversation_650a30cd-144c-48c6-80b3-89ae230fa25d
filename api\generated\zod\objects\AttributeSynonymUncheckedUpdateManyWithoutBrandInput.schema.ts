/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { IntFieldUpdateOperationsInputObjectSchema } from './IntFieldUpdateOperationsInput.schema';
import { StringFieldUpdateOperationsInputObjectSchema } from './StringFieldUpdateOperationsInput.schema';
import { NullableStringFieldUpdateOperationsInputObjectSchema } from './NullableStringFieldUpdateOperationsInput.schema';
import { SynonymCompatibilityLevelSchema } from '../enums/SynonymCompatibilityLevel.schema';
import { NullableEnumSynonymCompatibilityLevelFieldUpdateOperationsInputObjectSchema } from './NullableEnumSynonymCompatibilityLevelFieldUpdateOperationsInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AttributeSynonymUncheckedUpdateManyWithoutBrandInput>;
export const AttributeSynonymUncheckedUpdateManyWithoutBrandInputObjectSchema: SchemaType = z.object({
    id: z.union([z.number(),
    z.lazy(() => IntFieldUpdateOperationsInputObjectSchema)]).optional(), value: z.union([z.string(),
    z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(), groupId: z.union([z.number(),
    z.lazy(() => IntFieldUpdateOperationsInputObjectSchema)]).optional(), notes: z.union([z.string(),
    z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
    z.null()]).optional().nullable(), compatibilityLevel: z.union([z.lazy(() => SynonymCompatibilityLevelSchema),
    z.lazy(() => NullableEnumSynonymCompatibilityLevelFieldUpdateOperationsInputObjectSchema),
    z.null()]).optional().nullable()
}).strict() as SchemaType;
