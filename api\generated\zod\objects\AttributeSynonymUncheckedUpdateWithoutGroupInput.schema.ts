/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { IntFieldUpdateOperationsInputObjectSchema } from './IntFieldUpdateOperationsInput.schema';
import { StringFieldUpdateOperationsInputObjectSchema } from './StringFieldUpdateOperationsInput.schema';
import { NullableStringFieldUpdateOperationsInputObjectSchema } from './NullableStringFieldUpdateOperationsInput.schema';
import { NullableIntFieldUpdateOperationsInputObjectSchema } from './NullableIntFieldUpdateOperationsInput.schema';
import { SynonymCompatibilityLevelSchema } from '../enums/SynonymCompatibilityLevel.schema';
import { NullableEnumSynonymCompatibilityLevelFieldUpdateOperationsInputObjectSchema } from './NullableEnumSynonymCompatibilityLevelFieldUpdateOperationsInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AttributeSynonymUncheckedUpdateWithoutGroupInput>;
export const AttributeSynonymUncheckedUpdateWithoutGroupInputObjectSchema: SchemaType = z.object({
    id: z.union([z.number(),
    z.lazy(() => IntFieldUpdateOperationsInputObjectSchema)]).optional(), value: z.union([z.string(),
    z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(), notes: z.union([z.string(),
    z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
    z.null()]).optional().nullable(), brandId: z.union([z.number(),
    z.lazy(() => NullableIntFieldUpdateOperationsInputObjectSchema),
    z.null()]).optional().nullable(), compatibilityLevel: z.union([z.lazy(() => SynonymCompatibilityLevelSchema),
    z.lazy(() => NullableEnumSynonymCompatibilityLevelFieldUpdateOperationsInputObjectSchema),
    z.null()]).optional().nullable()
}).strict() as SchemaType;
