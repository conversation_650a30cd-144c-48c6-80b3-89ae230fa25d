/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { AttributeSynonymScalarWhereInputObjectSchema } from './AttributeSynonymScalarWhereInput.schema';
import { AttributeSynonymUpdateManyMutationInputObjectSchema } from './AttributeSynonymUpdateManyMutationInput.schema';
import { AttributeSynonymUncheckedUpdateManyWithoutBrandInputObjectSchema } from './AttributeSynonymUncheckedUpdateManyWithoutBrandInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AttributeSynonymUpdateManyWithWhereWithoutBrandInput>;
export const AttributeSynonymUpdateManyWithWhereWithoutBrandInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => AttributeSynonymScalarWhereInputObjectSchema), data: z.union([z.lazy(() => AttributeSynonymUpdateManyMutationInputObjectSchema), z.lazy(() => AttributeSynonymUncheckedUpdateManyWithoutBrandInputObjectSchema)])
}).strict() as SchemaType;
