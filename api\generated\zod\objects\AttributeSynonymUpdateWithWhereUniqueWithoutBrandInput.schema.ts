/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { AttributeSynonymWhereUniqueInputObjectSchema } from './AttributeSynonymWhereUniqueInput.schema';
import { AttributeSynonymUpdateWithoutBrandInputObjectSchema } from './AttributeSynonymUpdateWithoutBrandInput.schema';
import { AttributeSynonymUncheckedUpdateWithoutBrandInputObjectSchema } from './AttributeSynonymUncheckedUpdateWithoutBrandInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AttributeSynonymUpdateWithWhereUniqueWithoutBrandInput>;
export const AttributeSynonymUpdateWithWhereUniqueWithoutBrandInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => AttributeSynonymWhereUniqueInputObjectSchema), data: z.union([z.lazy(() => AttributeSynonymUpdateWithoutBrandInputObjectSchema), z.lazy(() => AttributeSynonymUncheckedUpdateWithoutBrandInputObjectSchema)])
}).strict() as SchemaType;
