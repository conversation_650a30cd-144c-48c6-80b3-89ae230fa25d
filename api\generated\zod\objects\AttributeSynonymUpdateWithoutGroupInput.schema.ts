/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { StringFieldUpdateOperationsInputObjectSchema } from './StringFieldUpdateOperationsInput.schema';
import { NullableStringFieldUpdateOperationsInputObjectSchema } from './NullableStringFieldUpdateOperationsInput.schema';
import { SynonymCompatibilityLevelSchema } from '../enums/SynonymCompatibilityLevel.schema';
import { NullableEnumSynonymCompatibilityLevelFieldUpdateOperationsInputObjectSchema } from './NullableEnumSynonymCompatibilityLevelFieldUpdateOperationsInput.schema';
import { BrandUpdateOneWithoutAttributeSynonymsNestedInputObjectSchema } from './BrandUpdateOneWithoutAttributeSynonymsNestedInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AttributeSynonymUpdateWithoutGroupInput>;
export const AttributeSynonymUpdateWithoutGroupInputObjectSchema: SchemaType = z.object({
    value: z.union([z.string(),
    z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(), notes: z.union([z.string(),
    z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
    z.null()]).optional().nullable(), compatibilityLevel: z.union([z.lazy(() => SynonymCompatibilityLevelSchema),
    z.lazy(() => NullableEnumSynonymCompatibilityLevelFieldUpdateOperationsInputObjectSchema),
    z.null()]).optional().nullable(), brand: z.lazy(() => BrandUpdateOneWithoutAttributeSynonymsNestedInputObjectSchema).optional().optional()
}).strict() as SchemaType;
