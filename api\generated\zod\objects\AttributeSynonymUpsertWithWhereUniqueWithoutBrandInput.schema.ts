/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { AttributeSynonymWhereUniqueInputObjectSchema } from './AttributeSynonymWhereUniqueInput.schema';
import { AttributeSynonymUpdateWithoutBrandInputObjectSchema } from './AttributeSynonymUpdateWithoutBrandInput.schema';
import { AttributeSynonymUncheckedUpdateWithoutBrandInputObjectSchema } from './AttributeSynonymUncheckedUpdateWithoutBrandInput.schema';
import { AttributeSynonymCreateWithoutBrandInputObjectSchema } from './AttributeSynonymCreateWithoutBrandInput.schema';
import { AttributeSynonymUncheckedCreateWithoutBrandInputObjectSchema } from './AttributeSynonymUncheckedCreateWithoutBrandInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AttributeSynonymUpsertWithWhereUniqueWithoutBrandInput>;
export const AttributeSynonymUpsertWithWhereUniqueWithoutBrandInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => AttributeSynonymWhereUniqueInputObjectSchema), update: z.union([z.lazy(() => AttributeSynonymUpdateWithoutBrandInputObjectSchema), z.lazy(() => AttributeSynonymUncheckedUpdateWithoutBrandInputObjectSchema)]), create: z.union([z.lazy(() => AttributeSynonymCreateWithoutBrandInputObjectSchema), z.lazy(() => AttributeSynonymUncheckedCreateWithoutBrandInputObjectSchema)])
}).strict() as SchemaType;
