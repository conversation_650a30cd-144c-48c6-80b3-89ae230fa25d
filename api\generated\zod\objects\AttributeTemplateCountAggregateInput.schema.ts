/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AttributeTemplateCountAggregateInputType>;
export const AttributeTemplateCountAggregateInputObjectSchema: SchemaType = z.object({
    id: z.literal(true).optional().optional(), name: z.literal(true).optional().optional(), title: z.literal(true).optional().optional(), description: z.literal(true).optional().optional(), dataType: z.literal(true).optional().optional(), unit: z.literal(true).optional().optional(), isRequired: z.literal(true).optional().optional(), minValue: z.literal(true).optional().optional(), maxValue: z.literal(true).optional().optional(), allowedValues: z.literal(true).optional().optional(), tolerance: z.literal(true).optional().optional(), groupId: z.literal(true).optional().optional(), createdAt: z.literal(true).optional().optional(), updatedAt: z.literal(true).optional().optional(), _all: z.literal(true).optional().optional()
}).strict() as SchemaType;
