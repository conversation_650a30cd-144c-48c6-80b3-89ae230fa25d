/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { AttributeTemplateCountOutputTypeSelectObjectSchema } from './AttributeTemplateCountOutputTypeSelect.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AttributeTemplateCountOutputTypeDefaultArgs>;
export const AttributeTemplateCountOutputTypeDefaultArgsObjectSchema: SchemaType = z.object({
    select: z.lazy(() => AttributeTemplateCountOutputTypeSelectObjectSchema).optional().optional()
}).strict() as SchemaType;
