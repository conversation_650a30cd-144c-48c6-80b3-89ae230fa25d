/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AttributeTemplateCountOutputTypeSelect>;
export const AttributeTemplateCountOutputTypeSelectObjectSchema: SchemaType = z.object({
    partAttributes: z.boolean().optional().optional(), catalogItemAttributes: z.boolean().optional().optional(), equipmentAttributes: z.boolean().optional().optional(), synonymGroups: z.boolean().optional().optional()
}).strict() as SchemaType;
