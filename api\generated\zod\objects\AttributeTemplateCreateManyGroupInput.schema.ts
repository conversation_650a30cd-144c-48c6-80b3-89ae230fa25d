/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { AttributeDataTypeSchema } from '../enums/AttributeDataType.schema';
import { AttributeUnitSchema } from '../enums/AttributeUnit.schema';
import { AttributeTemplateCreateallowedValuesInputObjectSchema } from './AttributeTemplateCreateallowedValuesInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AttributeTemplateCreateManyGroupInput>;
export const AttributeTemplateCreateManyGroupInputObjectSchema: SchemaType = z.object({
    id: z.number().optional().optional(), name: z.string(), title: z.string(), description: z.union([z.string(),
    z.null()]).optional().nullable(), dataType: z.lazy(() => AttributeDataTypeSchema).optional().optional(), unit: z.union([z.lazy(() => AttributeUnitSchema),
    z.null()]).optional().nullable(), isRequired: z.boolean().optional().optional(), minValue: z.union([z.number(),
    z.null()]).optional().nullable(), maxValue: z.union([z.number(),
    z.null()]).optional().nullable(), allowedValues: z.union([z.lazy(() => AttributeTemplateCreateallowedValuesInputObjectSchema),
    z.string().array()]).optional(), tolerance: z.union([z.number(),
    z.null()]).optional().nullable(), createdAt: z.union([z.date().optional(), z.string().datetime().optional()]).optional(), updatedAt: z.union([z.date().optional(), z.string().datetime().optional()]).optional()
}).strict() as SchemaType;
