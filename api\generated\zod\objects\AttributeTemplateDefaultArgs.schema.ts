/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { AttributeTemplateSelectObjectSchema } from './AttributeTemplateSelect.schema';
import { AttributeTemplateIncludeObjectSchema } from './AttributeTemplateInclude.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AttributeTemplateDefaultArgs>;
export const AttributeTemplateDefaultArgsObjectSchema: SchemaType = z.object({
    select: z.lazy(() => AttributeTemplateSelectObjectSchema).optional().optional(), include: z.lazy(() => AttributeTemplateIncludeObjectSchema).optional().optional()
}).strict() as SchemaType;
