/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { AttributeGroupDefaultArgsObjectSchema } from './AttributeGroupDefaultArgs.schema';
import { PartAttributeInputSchema } from '../input/PartAttributeInput.schema';
import { CatalogItemAttributeInputSchema } from '../input/CatalogItemAttributeInput.schema';
import { EquipmentModelAttributeInputSchema } from '../input/EquipmentModelAttributeInput.schema';
import { AttributeSynonymGroupInputSchema } from '../input/AttributeSynonymGroupInput.schema';
import { AttributeTemplateCountOutputTypeDefaultArgsObjectSchema } from './AttributeTemplateCountOutputTypeDefaultArgs.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AttributeTemplateInclude>;
export const AttributeTemplateIncludeObjectSchema: SchemaType = z.object({
    group: z.union([z.boolean(),
    z.lazy(() => AttributeGroupDefaultArgsObjectSchema)]).optional(), partAttributes: z.union([z.boolean(),
    z.lazy(() => PartAttributeInputSchema.findMany)]).optional(), catalogItemAttributes: z.union([z.boolean(),
    z.lazy(() => CatalogItemAttributeInputSchema.findMany)]).optional(), equipmentAttributes: z.union([z.boolean(),
    z.lazy(() => EquipmentModelAttributeInputSchema.findMany)]).optional(), synonymGroups: z.union([z.boolean(),
    z.lazy(() => AttributeSynonymGroupInputSchema.findMany)]).optional(), _count: z.union([z.boolean(),
    z.lazy(() => AttributeTemplateCountOutputTypeDefaultArgsObjectSchema)]).optional()
}).strict() as SchemaType;
