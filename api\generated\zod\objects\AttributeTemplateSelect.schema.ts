/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { AttributeGroupDefaultArgsObjectSchema } from './AttributeGroupDefaultArgs.schema';
import { PartAttributeInputSchema } from '../input/PartAttributeInput.schema';
import { CatalogItemAttributeInputSchema } from '../input/CatalogItemAttributeInput.schema';
import { EquipmentModelAttributeInputSchema } from '../input/EquipmentModelAttributeInput.schema';
import { AttributeSynonymGroupInputSchema } from '../input/AttributeSynonymGroupInput.schema';
import { AttributeTemplateCountOutputTypeDefaultArgsObjectSchema } from './AttributeTemplateCountOutputTypeDefaultArgs.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AttributeTemplateSelect>;
export const AttributeTemplateSelectObjectSchema: SchemaType = z.object({
    id: z.boolean().optional().optional(), name: z.boolean().optional().optional(), title: z.boolean().optional().optional(), description: z.boolean().optional().optional(), dataType: z.boolean().optional().optional(), unit: z.boolean().optional().optional(), isRequired: z.boolean().optional().optional(), minValue: z.boolean().optional().optional(), maxValue: z.boolean().optional().optional(), allowedValues: z.boolean().optional().optional(), tolerance: z.boolean().optional().optional(), group: z.union([z.boolean(),
    z.lazy(() => AttributeGroupDefaultArgsObjectSchema)]).optional(), groupId: z.boolean().optional().optional(), partAttributes: z.union([z.boolean(),
    z.lazy(() => PartAttributeInputSchema.findMany)]).optional(), catalogItemAttributes: z.union([z.boolean(),
    z.lazy(() => CatalogItemAttributeInputSchema.findMany)]).optional(), equipmentAttributes: z.union([z.boolean(),
    z.lazy(() => EquipmentModelAttributeInputSchema.findMany)]).optional(), synonymGroups: z.union([z.boolean(),
    z.lazy(() => AttributeSynonymGroupInputSchema.findMany)]).optional(), createdAt: z.boolean().optional().optional(), updatedAt: z.boolean().optional().optional(), _count: z.union([z.boolean(),
    z.lazy(() => AttributeTemplateCountOutputTypeDefaultArgsObjectSchema)]).optional()
}).strict() as SchemaType;
