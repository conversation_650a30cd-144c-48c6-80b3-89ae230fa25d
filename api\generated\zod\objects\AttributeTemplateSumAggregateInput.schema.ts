/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AttributeTemplateSumAggregateInputType>;
export const AttributeTemplateSumAggregateInputObjectSchema: SchemaType = z.object({
    id: z.literal(true).optional().optional(), minValue: z.literal(true).optional().optional(), maxValue: z.literal(true).optional().optional(), tolerance: z.literal(true).optional().optional(), groupId: z.literal(true).optional().optional()
}).strict() as SchemaType;
