/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { IntFieldUpdateOperationsInputObjectSchema } from './IntFieldUpdateOperationsInput.schema';
import { StringFieldUpdateOperationsInputObjectSchema } from './StringFieldUpdateOperationsInput.schema';
import { NullableStringFieldUpdateOperationsInputObjectSchema } from './NullableStringFieldUpdateOperationsInput.schema';
import { AttributeDataTypeSchema } from '../enums/AttributeDataType.schema';
import { EnumAttributeDataTypeFieldUpdateOperationsInputObjectSchema } from './EnumAttributeDataTypeFieldUpdateOperationsInput.schema';
import { AttributeUnitSchema } from '../enums/AttributeUnit.schema';
import { NullableEnumAttributeUnitFieldUpdateOperationsInputObjectSchema } from './NullableEnumAttributeUnitFieldUpdateOperationsInput.schema';
import { BoolFieldUpdateOperationsInputObjectSchema } from './BoolFieldUpdateOperationsInput.schema';
import { NullableFloatFieldUpdateOperationsInputObjectSchema } from './NullableFloatFieldUpdateOperationsInput.schema';
import { AttributeTemplateUpdateallowedValuesInputObjectSchema } from './AttributeTemplateUpdateallowedValuesInput.schema';
import { DateTimeFieldUpdateOperationsInputObjectSchema } from './DateTimeFieldUpdateOperationsInput.schema';
import { PartAttributeUncheckedUpdateManyWithoutTemplateNestedInputObjectSchema } from './PartAttributeUncheckedUpdateManyWithoutTemplateNestedInput.schema';
import { CatalogItemAttributeUncheckedUpdateManyWithoutTemplateNestedInputObjectSchema } from './CatalogItemAttributeUncheckedUpdateManyWithoutTemplateNestedInput.schema';
import { EquipmentModelAttributeUncheckedUpdateManyWithoutTemplateNestedInputObjectSchema } from './EquipmentModelAttributeUncheckedUpdateManyWithoutTemplateNestedInput.schema';
import { AttributeSynonymGroupUncheckedUpdateManyWithoutTemplateNestedInputObjectSchema } from './AttributeSynonymGroupUncheckedUpdateManyWithoutTemplateNestedInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.AttributeTemplateUncheckedUpdateWithoutGroupInput>;
export const AttributeTemplateUncheckedUpdateWithoutGroupInputObjectSchema: SchemaType = z.object({
    id: z.union([z.number(),
    z.lazy(() => IntFieldUpdateOperationsInputObjectSchema)]).optional(), name: z.union([z.string(),
    z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(), title: z.union([z.string(),
    z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(), description: z.union([z.string(),
    z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
    z.null()]).optional().nullable(), dataType: z.union([z.lazy(() => AttributeDataTypeSchema),
    z.lazy(() => EnumAttributeDataTypeFieldUpdateOperationsInputObjectSchema)]).optional(), unit: z.union([z.lazy(() => AttributeUnitSchema),
    z.lazy(() => NullableEnumAttributeUnitFieldUpdateOperationsInputObjectSchema),
    z.null()]).optional().nullable(), isRequired: z.union([z.boolean(),
    z.lazy(() => BoolFieldUpdateOperationsInputObjectSchema)]).optional(), minValue: z.union([z.number(),
    z.lazy(() => NullableFloatFieldUpdateOperationsInputObjectSchema),
    z.null()]).optional().nullable(), maxValue: z.union([z.number(),
    z.lazy(() => NullableFloatFieldUpdateOperationsInputObjectSchema),
    z.null()]).optional().nullable(), allowedValues: z.union([z.lazy(() => AttributeTemplateUpdateallowedValuesInputObjectSchema),
    z.string().array()]).optional(), tolerance: z.union([z.number(),
    z.lazy(() => NullableFloatFieldUpdateOperationsInputObjectSchema),
    z.null()]).optional().nullable(), createdAt: z.union([z.union([z.date(), z.string().datetime().optional()]),
    z.lazy(() => DateTimeFieldUpdateOperationsInputObjectSchema)]).optional(), updatedAt: z.union([z.union([z.date(), z.string().datetime().optional()]),
    z.lazy(() => DateTimeFieldUpdateOperationsInputObjectSchema)]).optional(), partAttributes: z.lazy(() => PartAttributeUncheckedUpdateManyWithoutTemplateNestedInputObjectSchema).optional().optional(), catalogItemAttributes: z.lazy(() => CatalogItemAttributeUncheckedUpdateManyWithoutTemplateNestedInputObjectSchema).optional().optional(), equipmentAttributes: z.lazy(() => EquipmentModelAttributeUncheckedUpdateManyWithoutTemplateNestedInputObjectSchema).optional().optional(), synonymGroups: z.lazy(() => AttributeSynonymGroupUncheckedUpdateManyWithoutTemplateNestedInputObjectSchema).optional().optional()
}).strict() as SchemaType;
