/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { BrandCountOutputTypeSelectObjectSchema } from './BrandCountOutputTypeSelect.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.BrandCountOutputTypeDefaultArgs>;
export const BrandCountOutputTypeDefaultArgsObjectSchema: SchemaType = z.object({
    select: z.lazy(() => BrandCountOutputTypeSelectObjectSchema).optional().optional()
}).strict() as SchemaType;
