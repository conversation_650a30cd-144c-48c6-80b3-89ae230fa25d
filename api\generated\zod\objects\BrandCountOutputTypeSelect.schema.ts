/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.BrandCountOutputTypeSelect>;
export const BrandCountOutputTypeSelectObjectSchema: SchemaType = z.object({
    catalogItems: z.boolean().optional().optional(), equipmentModel: z.boolean().optional().optional(), attributeSynonyms: z.boolean().optional().optional()
}).strict() as SchemaType;
