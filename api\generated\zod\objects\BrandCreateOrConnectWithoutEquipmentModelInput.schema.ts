/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { BrandWhereUniqueInputObjectSchema } from './BrandWhereUniqueInput.schema';
import { BrandCreateWithoutEquipmentModelInputObjectSchema } from './BrandCreateWithoutEquipmentModelInput.schema';
import { BrandUncheckedCreateWithoutEquipmentModelInputObjectSchema } from './BrandUncheckedCreateWithoutEquipmentModelInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.BrandCreateOrConnectWithoutEquipmentModelInput>;
export const BrandCreateOrConnectWithoutEquipmentModelInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => BrandWhereUniqueInputObjectSchema), create: z.union([z.lazy(() => BrandCreateWithoutEquipmentModelInputObjectSchema), z.lazy(() => BrandUncheckedCreateWithoutEquipmentModelInputObjectSchema)])
}).strict() as SchemaType;
