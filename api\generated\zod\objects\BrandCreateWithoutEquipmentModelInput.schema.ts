/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { CatalogItemCreateNestedManyWithoutBrandInputObjectSchema } from './CatalogItemCreateNestedManyWithoutBrandInput.schema';
import { AttributeSynonymCreateNestedManyWithoutBrandInputObjectSchema } from './AttributeSynonymCreateNestedManyWithoutBrandInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.BrandCreateWithoutEquipmentModelInput>;
export const BrandCreateWithoutEquipmentModelInputObjectSchema: SchemaType = z.object({
    name: z.string(), slug: z.string(), country: z.union([z.string(),
    z.null()]).optional().nullable(), isOem: z.boolean().optional().optional(), catalogItems: z.lazy(() => CatalogItemCreateNestedManyWithoutBrandInputObjectSchema).optional().optional(), attributeSynonyms: z.lazy(() => AttributeSynonymCreateNestedManyWithoutBrandInputObjectSchema).optional().optional()
}).strict() as SchemaType;
