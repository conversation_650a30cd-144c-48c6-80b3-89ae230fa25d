/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { BrandSelectObjectSchema } from './BrandSelect.schema';
import { BrandIncludeObjectSchema } from './BrandInclude.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.BrandDefaultArgs>;
export const BrandDefaultArgsObjectSchema: SchemaType = z.object({
    select: z.lazy(() => BrandSelectObjectSchema).optional().optional(), include: z.lazy(() => BrandIncludeObjectSchema).optional().optional()
}).strict() as SchemaType;
