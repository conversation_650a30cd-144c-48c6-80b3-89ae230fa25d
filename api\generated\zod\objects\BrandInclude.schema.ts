/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { CatalogItemInputSchema } from '../input/CatalogItemInput.schema';
import { EquipmentModelInputSchema } from '../input/EquipmentModelInput.schema';
import { AttributeSynonymInputSchema } from '../input/AttributeSynonymInput.schema';
import { BrandCountOutputTypeDefaultArgsObjectSchema } from './BrandCountOutputTypeDefaultArgs.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.BrandInclude>;
export const BrandIncludeObjectSchema: SchemaType = z.object({
    catalogItems: z.union([z.boolean(),
    z.lazy(() => CatalogItemInputSchema.findMany)]).optional(), equipmentModel: z.union([z.boolean(),
    z.lazy(() => EquipmentModelInputSchema.findMany)]).optional(), attributeSynonyms: z.union([z.boolean(),
    z.lazy(() => AttributeSynonymInputSchema.findMany)]).optional(), _count: z.union([z.boolean(),
    z.lazy(() => BrandCountOutputTypeDefaultArgsObjectSchema)]).optional()
}).strict() as SchemaType;
