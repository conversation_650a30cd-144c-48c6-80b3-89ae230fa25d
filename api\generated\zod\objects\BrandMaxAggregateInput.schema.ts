/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.BrandMaxAggregateInputType>;
export const BrandMaxAggregateInputObjectSchema: SchemaType = z.object({
    id: z.literal(true).optional().optional(), name: z.literal(true).optional().optional(), slug: z.literal(true).optional().optional(), country: z.literal(true).optional().optional(), isOem: z.literal(true).optional().optional()
}).strict() as SchemaType;
