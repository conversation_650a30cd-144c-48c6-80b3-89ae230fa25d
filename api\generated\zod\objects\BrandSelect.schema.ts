/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { CatalogItemInputSchema } from '../input/CatalogItemInput.schema';
import { EquipmentModelInputSchema } from '../input/EquipmentModelInput.schema';
import { AttributeSynonymInputSchema } from '../input/AttributeSynonymInput.schema';
import { BrandCountOutputTypeDefaultArgsObjectSchema } from './BrandCountOutputTypeDefaultArgs.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.BrandSelect>;
export const BrandSelectObjectSchema: SchemaType = z.object({
    id: z.boolean().optional().optional(), name: z.boolean().optional().optional(), slug: z.boolean().optional().optional(), country: z.boolean().optional().optional(), isOem: z.boolean().optional().optional(), catalogItems: z.union([z.boolean(),
    z.lazy(() => CatalogItemInputSchema.findMany)]).optional(), equipmentModel: z.union([z.boolean(),
    z.lazy(() => EquipmentModelInputSchema.findMany)]).optional(), attributeSynonyms: z.union([z.boolean(),
    z.lazy(() => AttributeSynonymInputSchema.findMany)]).optional(), _count: z.union([z.boolean(),
    z.lazy(() => BrandCountOutputTypeDefaultArgsObjectSchema)]).optional()
}).strict() as SchemaType;
