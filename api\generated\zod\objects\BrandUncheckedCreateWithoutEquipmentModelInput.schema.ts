/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { CatalogItemUncheckedCreateNestedManyWithoutBrandInputObjectSchema } from './CatalogItemUncheckedCreateNestedManyWithoutBrandInput.schema';
import { AttributeSynonymUncheckedCreateNestedManyWithoutBrandInputObjectSchema } from './AttributeSynonymUncheckedCreateNestedManyWithoutBrandInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.BrandUncheckedCreateWithoutEquipmentModelInput>;
export const BrandUncheckedCreateWithoutEquipmentModelInputObjectSchema: SchemaType = z.object({
    id: z.number().optional().optional(), name: z.string(), slug: z.string(), country: z.union([z.string(),
    z.null()]).optional().nullable(), isOem: z.boolean().optional().optional(), catalogItems: z.lazy(() => CatalogItemUncheckedCreateNestedManyWithoutBrandInputObjectSchema).optional().optional(), attributeSynonyms: z.lazy(() => AttributeSynonymUncheckedCreateNestedManyWithoutBrandInputObjectSchema).optional().optional()
}).strict() as SchemaType;
