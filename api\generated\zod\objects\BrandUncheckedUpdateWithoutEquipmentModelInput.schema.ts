/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { IntFieldUpdateOperationsInputObjectSchema } from './IntFieldUpdateOperationsInput.schema';
import { StringFieldUpdateOperationsInputObjectSchema } from './StringFieldUpdateOperationsInput.schema';
import { NullableStringFieldUpdateOperationsInputObjectSchema } from './NullableStringFieldUpdateOperationsInput.schema';
import { BoolFieldUpdateOperationsInputObjectSchema } from './BoolFieldUpdateOperationsInput.schema';
import { CatalogItemUncheckedUpdateManyWithoutBrandNestedInputObjectSchema } from './CatalogItemUncheckedUpdateManyWithoutBrandNestedInput.schema';
import { AttributeSynonymUncheckedUpdateManyWithoutBrandNestedInputObjectSchema } from './AttributeSynonymUncheckedUpdateManyWithoutBrandNestedInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.BrandUncheckedUpdateWithoutEquipmentModelInput>;
export const BrandUncheckedUpdateWithoutEquipmentModelInputObjectSchema: SchemaType = z.object({
    id: z.union([z.number(),
    z.lazy(() => IntFieldUpdateOperationsInputObjectSchema)]).optional(), name: z.union([z.string(),
    z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(), slug: z.union([z.string(),
    z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(), country: z.union([z.string(),
    z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
    z.null()]).optional().nullable(), isOem: z.union([z.boolean(),
    z.lazy(() => BoolFieldUpdateOperationsInputObjectSchema)]).optional(), catalogItems: z.lazy(() => CatalogItemUncheckedUpdateManyWithoutBrandNestedInputObjectSchema).optional().optional(), attributeSynonyms: z.lazy(() => AttributeSynonymUncheckedUpdateManyWithoutBrandNestedInputObjectSchema).optional().optional()
}).strict() as SchemaType;
