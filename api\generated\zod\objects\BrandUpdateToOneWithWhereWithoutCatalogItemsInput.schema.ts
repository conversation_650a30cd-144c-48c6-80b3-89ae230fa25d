/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { BrandWhereInputObjectSchema } from './BrandWhereInput.schema';
import { BrandUpdateWithoutCatalogItemsInputObjectSchema } from './BrandUpdateWithoutCatalogItemsInput.schema';
import { BrandUncheckedUpdateWithoutCatalogItemsInputObjectSchema } from './BrandUncheckedUpdateWithoutCatalogItemsInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.BrandUpdateToOneWithWhereWithoutCatalogItemsInput>;
export const BrandUpdateToOneWithWhereWithoutCatalogItemsInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => BrandWhereInputObjectSchema).optional().optional(), data: z.union([z.lazy(() => BrandUpdateWithoutCatalogItemsInputObjectSchema), z.lazy(() => BrandUncheckedUpdateWithoutCatalogItemsInputObjectSchema)])
}).strict() as SchemaType;
