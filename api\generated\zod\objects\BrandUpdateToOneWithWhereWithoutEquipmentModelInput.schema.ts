/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { BrandWhereInputObjectSchema } from './BrandWhereInput.schema';
import { BrandUpdateWithoutEquipmentModelInputObjectSchema } from './BrandUpdateWithoutEquipmentModelInput.schema';
import { BrandUncheckedUpdateWithoutEquipmentModelInputObjectSchema } from './BrandUncheckedUpdateWithoutEquipmentModelInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.BrandUpdateToOneWithWhereWithoutEquipmentModelInput>;
export const BrandUpdateToOneWithWhereWithoutEquipmentModelInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => BrandWhereInputObjectSchema).optional().optional(), data: z.union([z.lazy(() => BrandUpdateWithoutEquipmentModelInputObjectSchema), z.lazy(() => BrandUncheckedUpdateWithoutEquipmentModelInputObjectSchema)])
}).strict() as SchemaType;
