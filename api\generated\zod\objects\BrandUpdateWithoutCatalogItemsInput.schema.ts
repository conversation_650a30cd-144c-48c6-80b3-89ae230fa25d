/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { StringFieldUpdateOperationsInputObjectSchema } from './StringFieldUpdateOperationsInput.schema';
import { NullableStringFieldUpdateOperationsInputObjectSchema } from './NullableStringFieldUpdateOperationsInput.schema';
import { BoolFieldUpdateOperationsInputObjectSchema } from './BoolFieldUpdateOperationsInput.schema';
import { EquipmentModelUpdateManyWithoutBrandNestedInputObjectSchema } from './EquipmentModelUpdateManyWithoutBrandNestedInput.schema';
import { AttributeSynonymUpdateManyWithoutBrandNestedInputObjectSchema } from './AttributeSynonymUpdateManyWithoutBrandNestedInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.BrandUpdateWithoutCatalogItemsInput>;
export const BrandUpdateWithoutCatalogItemsInputObjectSchema: SchemaType = z.object({
    name: z.union([z.string(),
    z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(), slug: z.union([z.string(),
    z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(), country: z.union([z.string(),
    z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
    z.null()]).optional().nullable(), isOem: z.union([z.boolean(),
    z.lazy(() => BoolFieldUpdateOperationsInputObjectSchema)]).optional(), equipmentModel: z.lazy(() => EquipmentModelUpdateManyWithoutBrandNestedInputObjectSchema).optional().optional(), attributeSynonyms: z.lazy(() => AttributeSynonymUpdateManyWithoutBrandNestedInputObjectSchema).optional().optional()
}).strict() as SchemaType;
