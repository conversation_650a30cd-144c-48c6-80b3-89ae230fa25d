/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { StringFieldUpdateOperationsInputObjectSchema } from './StringFieldUpdateOperationsInput.schema';
import { NullableStringFieldUpdateOperationsInputObjectSchema } from './NullableStringFieldUpdateOperationsInput.schema';
import { BoolFieldUpdateOperationsInputObjectSchema } from './BoolFieldUpdateOperationsInput.schema';
import { CatalogItemUpdateManyWithoutBrandNestedInputObjectSchema } from './CatalogItemUpdateManyWithoutBrandNestedInput.schema';
import { AttributeSynonymUpdateManyWithoutBrandNestedInputObjectSchema } from './AttributeSynonymUpdateManyWithoutBrandNestedInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.BrandUpdateWithoutEquipmentModelInput>;
export const BrandUpdateWithoutEquipmentModelInputObjectSchema: SchemaType = z.object({
    name: z.union([z.string(),
    z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(), slug: z.union([z.string(),
    z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(), country: z.union([z.string(),
    z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
    z.null()]).optional().nullable(), isOem: z.union([z.boolean(),
    z.lazy(() => BoolFieldUpdateOperationsInputObjectSchema)]).optional(), catalogItems: z.lazy(() => CatalogItemUpdateManyWithoutBrandNestedInputObjectSchema).optional().optional(), attributeSynonyms: z.lazy(() => AttributeSynonymUpdateManyWithoutBrandNestedInputObjectSchema).optional().optional()
}).strict() as SchemaType;
