/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { BrandUpdateWithoutCatalogItemsInputObjectSchema } from './BrandUpdateWithoutCatalogItemsInput.schema';
import { BrandUncheckedUpdateWithoutCatalogItemsInputObjectSchema } from './BrandUncheckedUpdateWithoutCatalogItemsInput.schema';
import { BrandCreateWithoutCatalogItemsInputObjectSchema } from './BrandCreateWithoutCatalogItemsInput.schema';
import { BrandUncheckedCreateWithoutCatalogItemsInputObjectSchema } from './BrandUncheckedCreateWithoutCatalogItemsInput.schema';
import { BrandWhereInputObjectSchema } from './BrandWhereInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.BrandUpsertWithoutCatalogItemsInput>;
export const BrandUpsertWithoutCatalogItemsInputObjectSchema: SchemaType = z.object({
    update: z.union([z.lazy(() => BrandUpdateWithoutCatalogItemsInputObjectSchema), z.lazy(() => BrandUncheckedUpdateWithoutCatalogItemsInputObjectSchema)]), create: z.union([z.lazy(() => BrandCreateWithoutCatalogItemsInputObjectSchema), z.lazy(() => BrandUncheckedCreateWithoutCatalogItemsInputObjectSchema)]), where: z.lazy(() => BrandWhereInputObjectSchema).optional().optional()
}).strict() as SchemaType;
