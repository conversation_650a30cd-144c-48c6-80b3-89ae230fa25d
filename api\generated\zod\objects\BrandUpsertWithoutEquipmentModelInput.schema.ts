/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { BrandUpdateWithoutEquipmentModelInputObjectSchema } from './BrandUpdateWithoutEquipmentModelInput.schema';
import { BrandUncheckedUpdateWithoutEquipmentModelInputObjectSchema } from './BrandUncheckedUpdateWithoutEquipmentModelInput.schema';
import { BrandCreateWithoutEquipmentModelInputObjectSchema } from './BrandCreateWithoutEquipmentModelInput.schema';
import { BrandUncheckedCreateWithoutEquipmentModelInputObjectSchema } from './BrandUncheckedCreateWithoutEquipmentModelInput.schema';
import { BrandWhereInputObjectSchema } from './BrandWhereInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.BrandUpsertWithoutEquipmentModelInput>;
export const BrandUpsertWithoutEquipmentModelInputObjectSchema: SchemaType = z.object({
    update: z.union([z.lazy(() => BrandUpdateWithoutEquipmentModelInputObjectSchema), z.lazy(() => BrandUncheckedUpdateWithoutEquipmentModelInputObjectSchema)]), create: z.union([z.lazy(() => BrandCreateWithoutEquipmentModelInputObjectSchema), z.lazy(() => BrandUncheckedCreateWithoutEquipmentModelInputObjectSchema)]), where: z.lazy(() => BrandWhereInputObjectSchema).optional().optional()
}).strict() as SchemaType;
