/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.CatalogItemAttributeCreateManyTemplateInput>;
export const CatalogItemAttributeCreateManyTemplateInputObjectSchema: SchemaType = z.object({
    id: z.number().optional().optional(), value: z.string(), numericValue: z.union([z.number(),
    z.null()]).optional().nullable(), catalogItemId: z.number()
}).strict() as SchemaType;
