/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { CatalogItemAttributeSelectObjectSchema } from './CatalogItemAttributeSelect.schema';
import { CatalogItemAttributeIncludeObjectSchema } from './CatalogItemAttributeInclude.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.CatalogItemAttributeDefaultArgs>;
export const CatalogItemAttributeDefaultArgsObjectSchema: SchemaType = z.object({
    select: z.lazy(() => CatalogItemAttributeSelectObjectSchema).optional().optional(), include: z.lazy(() => CatalogItemAttributeIncludeObjectSchema).optional().optional()
}).strict() as SchemaType;
