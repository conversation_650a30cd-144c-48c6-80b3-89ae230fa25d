/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { CatalogItemDefaultArgsObjectSchema } from './CatalogItemDefaultArgs.schema';
import { AttributeTemplateDefaultArgsObjectSchema } from './AttributeTemplateDefaultArgs.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.CatalogItemAttributeSelect>;
export const CatalogItemAttributeSelectObjectSchema: SchemaType = z.object({
    id: z.boolean().optional().optional(), value: z.boolean().optional().optional(), numericValue: z.boolean().optional().optional(), catalogItem: z.union([z.boolean(),
    z.lazy(() => CatalogItemDefaultArgsObjectSchema)]).optional(), catalogItemId: z.boolean().optional().optional(), template: z.union([z.boolean(),
    z.lazy(() => AttributeTemplateDefaultArgsObjectSchema)]).optional(), templateId: z.boolean().optional().optional()
}).strict() as SchemaType;
