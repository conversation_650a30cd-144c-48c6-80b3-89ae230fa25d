/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.CatalogItemAttributeSumAggregateInputType>;
export const CatalogItemAttributeSumAggregateInputObjectSchema: SchemaType = z.object({
    id: z.literal(true).optional().optional(), numericValue: z.literal(true).optional().optional(), catalogItemId: z.literal(true).optional().optional(), templateId: z.literal(true).optional().optional()
}).strict() as SchemaType;
