/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { CatalogItemAttributeScalarWhereInputObjectSchema } from './CatalogItemAttributeScalarWhereInput.schema';
import { CatalogItemAttributeUpdateManyMutationInputObjectSchema } from './CatalogItemAttributeUpdateManyMutationInput.schema';
import { CatalogItemAttributeUncheckedUpdateManyWithoutCatalogItemInputObjectSchema } from './CatalogItemAttributeUncheckedUpdateManyWithoutCatalogItemInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.CatalogItemAttributeUpdateManyWithWhereWithoutCatalogItemInput>;
export const CatalogItemAttributeUpdateManyWithWhereWithoutCatalogItemInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => CatalogItemAttributeScalarWhereInputObjectSchema), data: z.union([z.lazy(() => CatalogItemAttributeUpdateManyMutationInputObjectSchema), z.lazy(() => CatalogItemAttributeUncheckedUpdateManyWithoutCatalogItemInputObjectSchema)])
}).strict() as SchemaType;
