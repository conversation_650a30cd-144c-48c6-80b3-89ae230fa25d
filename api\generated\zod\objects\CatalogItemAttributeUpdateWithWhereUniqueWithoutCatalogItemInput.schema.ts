/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { CatalogItemAttributeWhereUniqueInputObjectSchema } from './CatalogItemAttributeWhereUniqueInput.schema';
import { CatalogItemAttributeUpdateWithoutCatalogItemInputObjectSchema } from './CatalogItemAttributeUpdateWithoutCatalogItemInput.schema';
import { CatalogItemAttributeUncheckedUpdateWithoutCatalogItemInputObjectSchema } from './CatalogItemAttributeUncheckedUpdateWithoutCatalogItemInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.CatalogItemAttributeUpdateWithWhereUniqueWithoutCatalogItemInput>;
export const CatalogItemAttributeUpdateWithWhereUniqueWithoutCatalogItemInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => CatalogItemAttributeWhereUniqueInputObjectSchema), data: z.union([z.lazy(() => CatalogItemAttributeUpdateWithoutCatalogItemInputObjectSchema), z.lazy(() => CatalogItemAttributeUncheckedUpdateWithoutCatalogItemInputObjectSchema)])
}).strict() as SchemaType;
