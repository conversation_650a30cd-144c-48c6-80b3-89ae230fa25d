/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { StringFieldUpdateOperationsInputObjectSchema } from './StringFieldUpdateOperationsInput.schema';
import { NullableFloatFieldUpdateOperationsInputObjectSchema } from './NullableFloatFieldUpdateOperationsInput.schema';
import { CatalogItemUpdateOneRequiredWithoutAttributesNestedInputObjectSchema } from './CatalogItemUpdateOneRequiredWithoutAttributesNestedInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.CatalogItemAttributeUpdateWithoutTemplateInput>;
export const CatalogItemAttributeUpdateWithoutTemplateInputObjectSchema: SchemaType = z.object({
    value: z.union([z.string(),
    z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(), numericValue: z.union([z.number(),
    z.lazy(() => NullableFloatFieldUpdateOperationsInputObjectSchema),
    z.null()]).optional().nullable(), catalogItem: z.lazy(() => CatalogItemUpdateOneRequiredWithoutAttributesNestedInputObjectSchema).optional().optional()
}).strict() as SchemaType;
