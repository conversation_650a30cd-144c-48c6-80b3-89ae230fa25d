/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { CatalogItemAttributeWhereUniqueInputObjectSchema } from './CatalogItemAttributeWhereUniqueInput.schema';
import { CatalogItemAttributeUpdateWithoutCatalogItemInputObjectSchema } from './CatalogItemAttributeUpdateWithoutCatalogItemInput.schema';
import { CatalogItemAttributeUncheckedUpdateWithoutCatalogItemInputObjectSchema } from './CatalogItemAttributeUncheckedUpdateWithoutCatalogItemInput.schema';
import { CatalogItemAttributeCreateWithoutCatalogItemInputObjectSchema } from './CatalogItemAttributeCreateWithoutCatalogItemInput.schema';
import { CatalogItemAttributeUncheckedCreateWithoutCatalogItemInputObjectSchema } from './CatalogItemAttributeUncheckedCreateWithoutCatalogItemInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.CatalogItemAttributeUpsertWithWhereUniqueWithoutCatalogItemInput>;
export const CatalogItemAttributeUpsertWithWhereUniqueWithoutCatalogItemInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => CatalogItemAttributeWhereUniqueInputObjectSchema), update: z.union([z.lazy(() => CatalogItemAttributeUpdateWithoutCatalogItemInputObjectSchema), z.lazy(() => CatalogItemAttributeUncheckedUpdateWithoutCatalogItemInputObjectSchema)]), create: z.union([z.lazy(() => CatalogItemAttributeCreateWithoutCatalogItemInputObjectSchema), z.lazy(() => CatalogItemAttributeUncheckedCreateWithoutCatalogItemInputObjectSchema)])
}).strict() as SchemaType;
