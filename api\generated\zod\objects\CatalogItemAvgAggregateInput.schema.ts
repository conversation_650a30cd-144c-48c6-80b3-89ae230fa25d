/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.CatalogItemAvgAggregateInputType>;
export const CatalogItemAvgAggregateInputObjectSchema: SchemaType = z.object({
    id: z.literal(true).optional().optional(), brandId: z.literal(true).optional().optional(), imageId: z.literal(true).optional().optional()
}).strict() as SchemaType;
