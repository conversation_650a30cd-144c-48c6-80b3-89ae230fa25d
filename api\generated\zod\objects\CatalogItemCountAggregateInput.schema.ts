/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.CatalogItemCountAggregateInputType>;
export const CatalogItemCountAggregateInputObjectSchema: SchemaType = z.object({
    id: z.literal(true).optional().optional(), sku: z.literal(true).optional().optional(), source: z.literal(true).optional().optional(), description: z.literal(true).optional().optional(), brandId: z.literal(true).optional().optional(), isPublic: z.literal(true).optional().optional(), imageId: z.literal(true).optional().optional(), _all: z.literal(true).optional().optional()
}).strict() as SchemaType;
