/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { CatalogItemCountOutputTypeSelectObjectSchema } from './CatalogItemCountOutputTypeSelect.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.CatalogItemCountOutputTypeDefaultArgs>;
export const CatalogItemCountOutputTypeDefaultArgsObjectSchema: SchemaType = z.object({
    select: z.lazy(() => CatalogItemCountOutputTypeSelectObjectSchema).optional().optional()
}).strict() as SchemaType;
