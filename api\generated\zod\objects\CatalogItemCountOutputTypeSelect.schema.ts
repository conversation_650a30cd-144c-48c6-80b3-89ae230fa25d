/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.CatalogItemCountOutputTypeSelect>;
export const CatalogItemCountOutputTypeSelectObjectSchema: SchemaType = z.object({
    attributes: z.boolean().optional().optional(), applicabilities: z.boolean().optional().optional(), matchingProposals: z.boolean().optional().optional(), mediaAssets: z.boolean().optional().optional()
}).strict() as SchemaType;
