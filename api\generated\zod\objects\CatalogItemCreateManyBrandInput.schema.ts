/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.CatalogItemCreateManyBrandInput>;
export const CatalogItemCreateManyBrandInputObjectSchema: SchemaType = z.object({
    id: z.number().optional().optional(), sku: z.string(), source: z.union([z.string(),
    z.null()]).optional().nullable(), description: z.union([z.string(),
    z.null()]).optional().nullable(), isPublic: z.boolean().optional().optional(), imageId: z.union([z.number(),
    z.null()]).optional().nullable()
}).strict() as SchemaType;
