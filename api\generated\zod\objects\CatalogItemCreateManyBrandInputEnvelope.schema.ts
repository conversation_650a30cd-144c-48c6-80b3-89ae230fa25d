/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { CatalogItemCreateManyBrandInputObjectSchema } from './CatalogItemCreateManyBrandInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.CatalogItemCreateManyBrandInputEnvelope>;
export const CatalogItemCreateManyBrandInputEnvelopeObjectSchema: SchemaType = z.object({
    data: z.union([z.lazy(() => CatalogItemCreateManyBrandInputObjectSchema),
    z.lazy(() => CatalogItemCreateManyBrandInputObjectSchema).array()]), skipDuplicates: z.boolean().optional().optional()
}).strict() as SchemaType;
