/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { CatalogItemWhereUniqueInputObjectSchema } from './CatalogItemWhereUniqueInput.schema';
import { CatalogItemCreateWithoutBrandInputObjectSchema } from './CatalogItemCreateWithoutBrandInput.schema';
import { CatalogItemUncheckedCreateWithoutBrandInputObjectSchema } from './CatalogItemUncheckedCreateWithoutBrandInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.CatalogItemCreateOrConnectWithoutBrandInput>;
export const CatalogItemCreateOrConnectWithoutBrandInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => CatalogItemWhereUniqueInputObjectSchema), create: z.union([z.lazy(() => CatalogItemCreateWithoutBrandInputObjectSchema), z.lazy(() => CatalogItemUncheckedCreateWithoutBrandInputObjectSchema)])
}).strict() as SchemaType;
