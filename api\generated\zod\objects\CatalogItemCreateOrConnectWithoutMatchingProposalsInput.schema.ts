/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { CatalogItemWhereUniqueInputObjectSchema } from './CatalogItemWhereUniqueInput.schema';
import { CatalogItemCreateWithoutMatchingProposalsInputObjectSchema } from './CatalogItemCreateWithoutMatchingProposalsInput.schema';
import { CatalogItemUncheckedCreateWithoutMatchingProposalsInputObjectSchema } from './CatalogItemUncheckedCreateWithoutMatchingProposalsInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.CatalogItemCreateOrConnectWithoutMatchingProposalsInput>;
export const CatalogItemCreateOrConnectWithoutMatchingProposalsInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => CatalogItemWhereUniqueInputObjectSchema), create: z.union([z.lazy(() => CatalogItemCreateWithoutMatchingProposalsInputObjectSchema), z.lazy(() => CatalogItemUncheckedCreateWithoutMatchingProposalsInputObjectSchema)])
}).strict() as SchemaType;
