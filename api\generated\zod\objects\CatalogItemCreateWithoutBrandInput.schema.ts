/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { CatalogItemAttributeCreateNestedManyWithoutCatalogItemInputObjectSchema } from './CatalogItemAttributeCreateNestedManyWithoutCatalogItemInput.schema';
import { PartApplicabilityCreateNestedManyWithoutCatalogItemInputObjectSchema } from './PartApplicabilityCreateNestedManyWithoutCatalogItemInput.schema';
import { MatchingProposalCreateNestedManyWithoutCatalogItemInputObjectSchema } from './MatchingProposalCreateNestedManyWithoutCatalogItemInput.schema';
import { MediaAssetCreateNestedOneWithoutCatalogItemInputObjectSchema } from './MediaAssetCreateNestedOneWithoutCatalogItemInput.schema';
import { MediaAssetCreateNestedManyWithoutCatalogItemsInputObjectSchema } from './MediaAssetCreateNestedManyWithoutCatalogItemsInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.CatalogItemCreateWithoutBrandInput>;
export const CatalogItemCreateWithoutBrandInputObjectSchema: SchemaType = z.object({
    sku: z.string(), source: z.union([z.string(),
    z.null()]).optional().nullable(), description: z.union([z.string(),
    z.null()]).optional().nullable(), isPublic: z.boolean().optional().optional(), attributes: z.lazy(() => CatalogItemAttributeCreateNestedManyWithoutCatalogItemInputObjectSchema).optional().optional(), applicabilities: z.lazy(() => PartApplicabilityCreateNestedManyWithoutCatalogItemInputObjectSchema).optional().optional(), matchingProposals: z.lazy(() => MatchingProposalCreateNestedManyWithoutCatalogItemInputObjectSchema).optional().optional(), image: z.lazy(() => MediaAssetCreateNestedOneWithoutCatalogItemInputObjectSchema).optional().optional(), mediaAssets: z.lazy(() => MediaAssetCreateNestedManyWithoutCatalogItemsInputObjectSchema).optional().optional()
}).strict() as SchemaType;
