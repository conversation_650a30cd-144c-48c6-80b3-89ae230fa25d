/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { CatalogItemSelectObjectSchema } from './CatalogItemSelect.schema';
import { CatalogItemIncludeObjectSchema } from './CatalogItemInclude.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.CatalogItemDefaultArgs>;
export const CatalogItemDefaultArgsObjectSchema: SchemaType = z.object({
    select: z.lazy(() => CatalogItemSelectObjectSchema).optional().optional(), include: z.lazy(() => CatalogItemIncludeObjectSchema).optional().optional()
}).strict() as SchemaType;
