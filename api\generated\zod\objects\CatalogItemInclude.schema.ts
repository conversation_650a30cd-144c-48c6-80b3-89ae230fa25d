/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { BrandDefaultArgsObjectSchema } from './BrandDefaultArgs.schema';
import { CatalogItemAttributeInputSchema } from '../input/CatalogItemAttributeInput.schema';
import { PartApplicabilityInputSchema } from '../input/PartApplicabilityInput.schema';
import { MatchingProposalInputSchema } from '../input/MatchingProposalInput.schema';
import { MediaAssetDefaultArgsObjectSchema } from './MediaAssetDefaultArgs.schema';
import { MediaAssetInputSchema } from '../input/MediaAssetInput.schema';
import { CatalogItemCountOutputTypeDefaultArgsObjectSchema } from './CatalogItemCountOutputTypeDefaultArgs.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.CatalogItemInclude>;
export const CatalogItemIncludeObjectSchema: SchemaType = z.object({
    brand: z.union([z.boolean(),
    z.lazy(() => BrandDefaultArgsObjectSchema)]).optional(), attributes: z.union([z.boolean(),
    z.lazy(() => CatalogItemAttributeInputSchema.findMany)]).optional(), applicabilities: z.union([z.boolean(),
    z.lazy(() => PartApplicabilityInputSchema.findMany)]).optional(), matchingProposals: z.union([z.boolean(),
    z.lazy(() => MatchingProposalInputSchema.findMany)]).optional(), image: z.union([z.boolean(),
    z.lazy(() => MediaAssetDefaultArgsObjectSchema)]).optional(), mediaAssets: z.union([z.boolean(),
    z.lazy(() => MediaAssetInputSchema.findMany)]).optional(), _count: z.union([z.boolean(),
    z.lazy(() => CatalogItemCountOutputTypeDefaultArgsObjectSchema)]).optional()
}).strict() as SchemaType;
