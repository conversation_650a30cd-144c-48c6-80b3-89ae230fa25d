/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { CatalogItemAttributeUncheckedCreateNestedManyWithoutCatalogItemInputObjectSchema } from './CatalogItemAttributeUncheckedCreateNestedManyWithoutCatalogItemInput.schema';
import { PartApplicabilityUncheckedCreateNestedManyWithoutCatalogItemInputObjectSchema } from './PartApplicabilityUncheckedCreateNestedManyWithoutCatalogItemInput.schema';
import { MatchingProposalUncheckedCreateNestedManyWithoutCatalogItemInputObjectSchema } from './MatchingProposalUncheckedCreateNestedManyWithoutCatalogItemInput.schema';
import { MediaAssetUncheckedCreateNestedManyWithoutCatalogItemsInputObjectSchema } from './MediaAssetUncheckedCreateNestedManyWithoutCatalogItemsInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.CatalogItemUncheckedCreateWithoutBrandInput>;
export const CatalogItemUncheckedCreateWithoutBrandInputObjectSchema: SchemaType = z.object({
    id: z.number().optional().optional(), sku: z.string(), source: z.union([z.string(),
    z.null()]).optional().nullable(), description: z.union([z.string(),
    z.null()]).optional().nullable(), isPublic: z.boolean().optional().optional(), imageId: z.union([z.number(),
    z.null()]).optional().nullable(), attributes: z.lazy(() => CatalogItemAttributeUncheckedCreateNestedManyWithoutCatalogItemInputObjectSchema).optional().optional(), applicabilities: z.lazy(() => PartApplicabilityUncheckedCreateNestedManyWithoutCatalogItemInputObjectSchema).optional().optional(), matchingProposals: z.lazy(() => MatchingProposalUncheckedCreateNestedManyWithoutCatalogItemInputObjectSchema).optional().optional(), mediaAssets: z.lazy(() => MediaAssetUncheckedCreateNestedManyWithoutCatalogItemsInputObjectSchema).optional().optional()
}).strict() as SchemaType;
