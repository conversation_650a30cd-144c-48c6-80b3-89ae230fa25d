/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { IntFieldUpdateOperationsInputObjectSchema } from './IntFieldUpdateOperationsInput.schema';
import { StringFieldUpdateOperationsInputObjectSchema } from './StringFieldUpdateOperationsInput.schema';
import { NullableStringFieldUpdateOperationsInputObjectSchema } from './NullableStringFieldUpdateOperationsInput.schema';
import { BoolFieldUpdateOperationsInputObjectSchema } from './BoolFieldUpdateOperationsInput.schema';
import { NullableIntFieldUpdateOperationsInputObjectSchema } from './NullableIntFieldUpdateOperationsInput.schema';
import { CatalogItemAttributeUncheckedUpdateManyWithoutCatalogItemNestedInputObjectSchema } from './CatalogItemAttributeUncheckedUpdateManyWithoutCatalogItemNestedInput.schema';
import { PartApplicabilityUncheckedUpdateManyWithoutCatalogItemNestedInputObjectSchema } from './PartApplicabilityUncheckedUpdateManyWithoutCatalogItemNestedInput.schema';
import { MatchingProposalUncheckedUpdateManyWithoutCatalogItemNestedInputObjectSchema } from './MatchingProposalUncheckedUpdateManyWithoutCatalogItemNestedInput.schema';
import { MediaAssetUncheckedUpdateManyWithoutCatalogItemsNestedInputObjectSchema } from './MediaAssetUncheckedUpdateManyWithoutCatalogItemsNestedInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.CatalogItemUncheckedUpdateWithoutBrandInput>;
export const CatalogItemUncheckedUpdateWithoutBrandInputObjectSchema: SchemaType = z.object({
    id: z.union([z.number(),
    z.lazy(() => IntFieldUpdateOperationsInputObjectSchema)]).optional(), sku: z.union([z.string(),
    z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(), source: z.union([z.string(),
    z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
    z.null()]).optional().nullable(), description: z.union([z.string(),
    z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
    z.null()]).optional().nullable(), isPublic: z.union([z.boolean(),
    z.lazy(() => BoolFieldUpdateOperationsInputObjectSchema)]).optional(), imageId: z.union([z.number(),
    z.lazy(() => NullableIntFieldUpdateOperationsInputObjectSchema),
    z.null()]).optional().nullable(), attributes: z.lazy(() => CatalogItemAttributeUncheckedUpdateManyWithoutCatalogItemNestedInputObjectSchema).optional().optional(), applicabilities: z.lazy(() => PartApplicabilityUncheckedUpdateManyWithoutCatalogItemNestedInputObjectSchema).optional().optional(), matchingProposals: z.lazy(() => MatchingProposalUncheckedUpdateManyWithoutCatalogItemNestedInputObjectSchema).optional().optional(), mediaAssets: z.lazy(() => MediaAssetUncheckedUpdateManyWithoutCatalogItemsNestedInputObjectSchema).optional().optional()
}).strict() as SchemaType;
