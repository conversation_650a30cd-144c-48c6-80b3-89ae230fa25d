/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { CatalogItemScalarWhereInputObjectSchema } from './CatalogItemScalarWhereInput.schema';
import { CatalogItemUpdateManyMutationInputObjectSchema } from './CatalogItemUpdateManyMutationInput.schema';
import { CatalogItemUncheckedUpdateManyWithoutBrandInputObjectSchema } from './CatalogItemUncheckedUpdateManyWithoutBrandInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.CatalogItemUpdateManyWithWhereWithoutBrandInput>;
export const CatalogItemUpdateManyWithWhereWithoutBrandInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => CatalogItemScalarWhereInputObjectSchema), data: z.union([z.lazy(() => CatalogItemUpdateManyMutationInputObjectSchema), z.lazy(() => CatalogItemUncheckedUpdateManyWithoutBrandInputObjectSchema)])
}).strict() as SchemaType;
