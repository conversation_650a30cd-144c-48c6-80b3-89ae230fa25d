/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { CatalogItemWhereInputObjectSchema } from './CatalogItemWhereInput.schema';
import { CatalogItemUpdateWithoutMatchingProposalsInputObjectSchema } from './CatalogItemUpdateWithoutMatchingProposalsInput.schema';
import { CatalogItemUncheckedUpdateWithoutMatchingProposalsInputObjectSchema } from './CatalogItemUncheckedUpdateWithoutMatchingProposalsInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.CatalogItemUpdateToOneWithWhereWithoutMatchingProposalsInput>;
export const CatalogItemUpdateToOneWithWhereWithoutMatchingProposalsInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => CatalogItemWhereInputObjectSchema).optional().optional(), data: z.union([z.lazy(() => CatalogItemUpdateWithoutMatchingProposalsInputObjectSchema), z.lazy(() => CatalogItemUncheckedUpdateWithoutMatchingProposalsInputObjectSchema)])
}).strict() as SchemaType;
