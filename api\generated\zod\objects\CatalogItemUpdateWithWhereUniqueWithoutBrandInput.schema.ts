/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { CatalogItemWhereUniqueInputObjectSchema } from './CatalogItemWhereUniqueInput.schema';
import { CatalogItemUpdateWithoutBrandInputObjectSchema } from './CatalogItemUpdateWithoutBrandInput.schema';
import { CatalogItemUncheckedUpdateWithoutBrandInputObjectSchema } from './CatalogItemUncheckedUpdateWithoutBrandInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.CatalogItemUpdateWithWhereUniqueWithoutBrandInput>;
export const CatalogItemUpdateWithWhereUniqueWithoutBrandInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => CatalogItemWhereUniqueInputObjectSchema), data: z.union([z.lazy(() => CatalogItemUpdateWithoutBrandInputObjectSchema), z.lazy(() => CatalogItemUncheckedUpdateWithoutBrandInputObjectSchema)])
}).strict() as SchemaType;
