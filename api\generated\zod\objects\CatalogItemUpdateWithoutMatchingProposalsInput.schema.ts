/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { StringFieldUpdateOperationsInputObjectSchema } from './StringFieldUpdateOperationsInput.schema';
import { NullableStringFieldUpdateOperationsInputObjectSchema } from './NullableStringFieldUpdateOperationsInput.schema';
import { BoolFieldUpdateOperationsInputObjectSchema } from './BoolFieldUpdateOperationsInput.schema';
import { BrandUpdateOneRequiredWithoutCatalogItemsNestedInputObjectSchema } from './BrandUpdateOneRequiredWithoutCatalogItemsNestedInput.schema';
import { CatalogItemAttributeUpdateManyWithoutCatalogItemNestedInputObjectSchema } from './CatalogItemAttributeUpdateManyWithoutCatalogItemNestedInput.schema';
import { PartApplicabilityUpdateManyWithoutCatalogItemNestedInputObjectSchema } from './PartApplicabilityUpdateManyWithoutCatalogItemNestedInput.schema';
import { MediaAssetUpdateOneWithoutCatalogItemNestedInputObjectSchema } from './MediaAssetUpdateOneWithoutCatalogItemNestedInput.schema';
import { MediaAssetUpdateManyWithoutCatalogItemsNestedInputObjectSchema } from './MediaAssetUpdateManyWithoutCatalogItemsNestedInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.CatalogItemUpdateWithoutMatchingProposalsInput>;
export const CatalogItemUpdateWithoutMatchingProposalsInputObjectSchema: SchemaType = z.object({
    sku: z.union([z.string(),
    z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(), source: z.union([z.string(),
    z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
    z.null()]).optional().nullable(), description: z.union([z.string(),
    z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
    z.null()]).optional().nullable(), isPublic: z.union([z.boolean(),
    z.lazy(() => BoolFieldUpdateOperationsInputObjectSchema)]).optional(), brand: z.lazy(() => BrandUpdateOneRequiredWithoutCatalogItemsNestedInputObjectSchema).optional().optional(), attributes: z.lazy(() => CatalogItemAttributeUpdateManyWithoutCatalogItemNestedInputObjectSchema).optional().optional(), applicabilities: z.lazy(() => PartApplicabilityUpdateManyWithoutCatalogItemNestedInputObjectSchema).optional().optional(), image: z.lazy(() => MediaAssetUpdateOneWithoutCatalogItemNestedInputObjectSchema).optional().optional(), mediaAssets: z.lazy(() => MediaAssetUpdateManyWithoutCatalogItemsNestedInputObjectSchema).optional().optional()
}).strict() as SchemaType;
