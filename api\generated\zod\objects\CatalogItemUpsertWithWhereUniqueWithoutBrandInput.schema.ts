/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { CatalogItemWhereUniqueInputObjectSchema } from './CatalogItemWhereUniqueInput.schema';
import { CatalogItemUpdateWithoutBrandInputObjectSchema } from './CatalogItemUpdateWithoutBrandInput.schema';
import { CatalogItemUncheckedUpdateWithoutBrandInputObjectSchema } from './CatalogItemUncheckedUpdateWithoutBrandInput.schema';
import { CatalogItemCreateWithoutBrandInputObjectSchema } from './CatalogItemCreateWithoutBrandInput.schema';
import { CatalogItemUncheckedCreateWithoutBrandInputObjectSchema } from './CatalogItemUncheckedCreateWithoutBrandInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.CatalogItemUpsertWithWhereUniqueWithoutBrandInput>;
export const CatalogItemUpsertWithWhereUniqueWithoutBrandInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => CatalogItemWhereUniqueInputObjectSchema), update: z.union([z.lazy(() => CatalogItemUpdateWithoutBrandInputObjectSchema), z.lazy(() => CatalogItemUncheckedUpdateWithoutBrandInputObjectSchema)]), create: z.union([z.lazy(() => CatalogItemCreateWithoutBrandInputObjectSchema), z.lazy(() => CatalogItemUncheckedCreateWithoutBrandInputObjectSchema)])
}).strict() as SchemaType;
