/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { CatalogItemUpdateWithoutMatchingProposalsInputObjectSchema } from './CatalogItemUpdateWithoutMatchingProposalsInput.schema';
import { CatalogItemUncheckedUpdateWithoutMatchingProposalsInputObjectSchema } from './CatalogItemUncheckedUpdateWithoutMatchingProposalsInput.schema';
import { CatalogItemCreateWithoutMatchingProposalsInputObjectSchema } from './CatalogItemCreateWithoutMatchingProposalsInput.schema';
import { CatalogItemUncheckedCreateWithoutMatchingProposalsInputObjectSchema } from './CatalogItemUncheckedCreateWithoutMatchingProposalsInput.schema';
import { CatalogItemWhereInputObjectSchema } from './CatalogItemWhereInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.CatalogItemUpsertWithoutMatchingProposalsInput>;
export const CatalogItemUpsertWithoutMatchingProposalsInputObjectSchema: SchemaType = z.object({
    update: z.union([z.lazy(() => CatalogItemUpdateWithoutMatchingProposalsInputObjectSchema), z.lazy(() => CatalogItemUncheckedUpdateWithoutMatchingProposalsInputObjectSchema)]), create: z.union([z.lazy(() => CatalogItemCreateWithoutMatchingProposalsInputObjectSchema), z.lazy(() => CatalogItemUncheckedCreateWithoutMatchingProposalsInputObjectSchema)]), where: z.lazy(() => CatalogItemWhereInputObjectSchema).optional().optional()
}).strict() as SchemaType;
