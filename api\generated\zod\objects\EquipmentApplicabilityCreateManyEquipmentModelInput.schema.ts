/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.EquipmentApplicabilityCreateManyEquipmentModelInput>;
export const EquipmentApplicabilityCreateManyEquipmentModelInputObjectSchema: SchemaType = z.object({
    id: z.number().optional().optional(), partId: z.number(), notes: z.union([z.string(),
    z.null()]).optional().nullable()
}).strict() as SchemaType;
