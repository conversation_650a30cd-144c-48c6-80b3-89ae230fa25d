/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { EquipmentApplicabilityCreateManyEquipmentModelInputObjectSchema } from './EquipmentApplicabilityCreateManyEquipmentModelInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.EquipmentApplicabilityCreateManyEquipmentModelInputEnvelope>;
export const EquipmentApplicabilityCreateManyEquipmentModelInputEnvelopeObjectSchema: SchemaType = z.object({
    data: z.union([z.lazy(() => EquipmentApplicabilityCreateManyEquipmentModelInputObjectSchema),
    z.lazy(() => EquipmentApplicabilityCreateManyEquipmentModelInputObjectSchema).array()]), skipDuplicates: z.boolean().optional().optional()
}).strict() as SchemaType;
