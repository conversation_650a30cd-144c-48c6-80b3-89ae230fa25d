/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.EquipmentApplicabilityCreateManyPartInput>;
export const EquipmentApplicabilityCreateManyPartInputObjectSchema: SchemaType = z.object({
    id: z.number().optional().optional(), equipmentModelId: z.string(), notes: z.union([z.string(),
    z.null()]).optional().nullable()
}).strict() as SchemaType;
