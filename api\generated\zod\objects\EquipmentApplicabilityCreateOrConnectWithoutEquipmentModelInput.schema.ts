/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { EquipmentApplicabilityWhereUniqueInputObjectSchema } from './EquipmentApplicabilityWhereUniqueInput.schema';
import { EquipmentApplicabilityCreateWithoutEquipmentModelInputObjectSchema } from './EquipmentApplicabilityCreateWithoutEquipmentModelInput.schema';
import { EquipmentApplicabilityUncheckedCreateWithoutEquipmentModelInputObjectSchema } from './EquipmentApplicabilityUncheckedCreateWithoutEquipmentModelInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.EquipmentApplicabilityCreateOrConnectWithoutEquipmentModelInput>;
export const EquipmentApplicabilityCreateOrConnectWithoutEquipmentModelInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => EquipmentApplicabilityWhereUniqueInputObjectSchema), create: z.union([z.lazy(() => EquipmentApplicabilityCreateWithoutEquipmentModelInputObjectSchema), z.lazy(() => EquipmentApplicabilityUncheckedCreateWithoutEquipmentModelInputObjectSchema)])
}).strict() as SchemaType;
