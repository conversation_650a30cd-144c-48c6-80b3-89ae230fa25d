/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartCreateNestedOneWithoutEquipmentApplicabilitiesInputObjectSchema } from './PartCreateNestedOneWithoutEquipmentApplicabilitiesInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.EquipmentApplicabilityCreateWithoutEquipmentModelInput>;
export const EquipmentApplicabilityCreateWithoutEquipmentModelInputObjectSchema: SchemaType = z.object({
    notes: z.union([z.string(),
    z.null()]).optional().nullable(), part: z.lazy(() => PartCreateNestedOneWithoutEquipmentApplicabilitiesInputObjectSchema)
}).strict() as SchemaType;
