/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { EquipmentApplicabilitySelectObjectSchema } from './EquipmentApplicabilitySelect.schema';
import { EquipmentApplicabilityIncludeObjectSchema } from './EquipmentApplicabilityInclude.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.EquipmentApplicabilityDefaultArgs>;
export const EquipmentApplicabilityDefaultArgsObjectSchema: SchemaType = z.object({
    select: z.lazy(() => EquipmentApplicabilitySelectObjectSchema).optional().optional(), include: z.lazy(() => EquipmentApplicabilityIncludeObjectSchema).optional().optional()
}).strict() as SchemaType;
