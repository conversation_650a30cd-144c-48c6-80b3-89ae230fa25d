/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartDefaultArgsObjectSchema } from './PartDefaultArgs.schema';
import { EquipmentModelDefaultArgsObjectSchema } from './EquipmentModelDefaultArgs.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.EquipmentApplicabilityInclude>;
export const EquipmentApplicabilityIncludeObjectSchema: SchemaType = z.object({
    part: z.union([z.boolean(),
    z.lazy(() => PartDefaultArgsObjectSchema)]).optional(), equipmentModel: z.union([z.boolean(),
    z.lazy(() => EquipmentModelDefaultArgsObjectSchema)]).optional()
}).strict() as SchemaType;
