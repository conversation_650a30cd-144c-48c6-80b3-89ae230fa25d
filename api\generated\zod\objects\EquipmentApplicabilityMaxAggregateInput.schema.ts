/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.EquipmentApplicabilityMaxAggregateInputType>;
export const EquipmentApplicabilityMaxAggregateInputObjectSchema: SchemaType = z.object({
    id: z.literal(true).optional().optional(), partId: z.literal(true).optional().optional(), equipmentModelId: z.literal(true).optional().optional(), notes: z.literal(true).optional().optional()
}).strict() as SchemaType;
