/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartDefaultArgsObjectSchema } from './PartDefaultArgs.schema';
import { EquipmentModelDefaultArgsObjectSchema } from './EquipmentModelDefaultArgs.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.EquipmentApplicabilitySelect>;
export const EquipmentApplicabilitySelectObjectSchema: SchemaType = z.object({
    id: z.boolean().optional().optional(), part: z.union([z.boolean(),
    z.lazy(() => PartDefaultArgsObjectSchema)]).optional(), partId: z.boolean().optional().optional(), equipmentModel: z.union([z.boolean(),
    z.lazy(() => EquipmentModelDefaultArgsObjectSchema)]).optional(), equipmentModelId: z.boolean().optional().optional(), notes: z.boolean().optional().optional()
}).strict() as SchemaType;
