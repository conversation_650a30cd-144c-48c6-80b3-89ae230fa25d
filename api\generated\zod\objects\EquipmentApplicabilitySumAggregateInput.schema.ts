/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.EquipmentApplicabilitySumAggregateInputType>;
export const EquipmentApplicabilitySumAggregateInputObjectSchema: SchemaType = z.object({
    id: z.literal(true).optional().optional(), partId: z.literal(true).optional().optional()
}).strict() as SchemaType;
