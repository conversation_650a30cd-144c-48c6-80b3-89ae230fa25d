/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { EquipmentApplicabilityScalarWhereInputObjectSchema } from './EquipmentApplicabilityScalarWhereInput.schema';
import { EquipmentApplicabilityUpdateManyMutationInputObjectSchema } from './EquipmentApplicabilityUpdateManyMutationInput.schema';
import { EquipmentApplicabilityUncheckedUpdateManyWithoutEquipmentModelInputObjectSchema } from './EquipmentApplicabilityUncheckedUpdateManyWithoutEquipmentModelInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.EquipmentApplicabilityUpdateManyWithWhereWithoutEquipmentModelInput>;
export const EquipmentApplicabilityUpdateManyWithWhereWithoutEquipmentModelInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => EquipmentApplicabilityScalarWhereInputObjectSchema), data: z.union([z.lazy(() => EquipmentApplicabilityUpdateManyMutationInputObjectSchema), z.lazy(() => EquipmentApplicabilityUncheckedUpdateManyWithoutEquipmentModelInputObjectSchema)])
}).strict() as SchemaType;
