/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { EquipmentApplicabilityWhereUniqueInputObjectSchema } from './EquipmentApplicabilityWhereUniqueInput.schema';
import { EquipmentApplicabilityUpdateWithoutEquipmentModelInputObjectSchema } from './EquipmentApplicabilityUpdateWithoutEquipmentModelInput.schema';
import { EquipmentApplicabilityUncheckedUpdateWithoutEquipmentModelInputObjectSchema } from './EquipmentApplicabilityUncheckedUpdateWithoutEquipmentModelInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.EquipmentApplicabilityUpdateWithWhereUniqueWithoutEquipmentModelInput>;
export const EquipmentApplicabilityUpdateWithWhereUniqueWithoutEquipmentModelInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => EquipmentApplicabilityWhereUniqueInputObjectSchema), data: z.union([z.lazy(() => EquipmentApplicabilityUpdateWithoutEquipmentModelInputObjectSchema), z.lazy(() => EquipmentApplicabilityUncheckedUpdateWithoutEquipmentModelInputObjectSchema)])
}).strict() as SchemaType;
