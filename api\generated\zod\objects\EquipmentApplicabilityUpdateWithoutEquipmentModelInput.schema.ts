/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { NullableStringFieldUpdateOperationsInputObjectSchema } from './NullableStringFieldUpdateOperationsInput.schema';
import { PartUpdateOneRequiredWithoutEquipmentApplicabilitiesNestedInputObjectSchema } from './PartUpdateOneRequiredWithoutEquipmentApplicabilitiesNestedInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.EquipmentApplicabilityUpdateWithoutEquipmentModelInput>;
export const EquipmentApplicabilityUpdateWithoutEquipmentModelInputObjectSchema: SchemaType = z.object({
    notes: z.union([z.string(),
    z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
    z.null()]).optional().nullable(), part: z.lazy(() => PartUpdateOneRequiredWithoutEquipmentApplicabilitiesNestedInputObjectSchema).optional().optional()
}).strict() as SchemaType;
