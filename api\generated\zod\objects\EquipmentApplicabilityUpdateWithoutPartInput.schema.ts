/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { NullableStringFieldUpdateOperationsInputObjectSchema } from './NullableStringFieldUpdateOperationsInput.schema';
import { EquipmentModelUpdateOneRequiredWithoutPartApplicabilitiesNestedInputObjectSchema } from './EquipmentModelUpdateOneRequiredWithoutPartApplicabilitiesNestedInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.EquipmentApplicabilityUpdateWithoutPartInput>;
export const EquipmentApplicabilityUpdateWithoutPartInputObjectSchema: SchemaType = z.object({
    notes: z.union([z.string(),
    z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
    z.null()]).optional().nullable(), equipmentModel: z.lazy(() => EquipmentModelUpdateOneRequiredWithoutPartApplicabilitiesNestedInputObjectSchema).optional().optional()
}).strict() as SchemaType;
