/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { EquipmentApplicabilityWhereUniqueInputObjectSchema } from './EquipmentApplicabilityWhereUniqueInput.schema';
import { EquipmentApplicabilityUpdateWithoutEquipmentModelInputObjectSchema } from './EquipmentApplicabilityUpdateWithoutEquipmentModelInput.schema';
import { EquipmentApplicabilityUncheckedUpdateWithoutEquipmentModelInputObjectSchema } from './EquipmentApplicabilityUncheckedUpdateWithoutEquipmentModelInput.schema';
import { EquipmentApplicabilityCreateWithoutEquipmentModelInputObjectSchema } from './EquipmentApplicabilityCreateWithoutEquipmentModelInput.schema';
import { EquipmentApplicabilityUncheckedCreateWithoutEquipmentModelInputObjectSchema } from './EquipmentApplicabilityUncheckedCreateWithoutEquipmentModelInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.EquipmentApplicabilityUpsertWithWhereUniqueWithoutEquipmentModelInput>;
export const EquipmentApplicabilityUpsertWithWhereUniqueWithoutEquipmentModelInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => EquipmentApplicabilityWhereUniqueInputObjectSchema), update: z.union([z.lazy(() => EquipmentApplicabilityUpdateWithoutEquipmentModelInputObjectSchema), z.lazy(() => EquipmentApplicabilityUncheckedUpdateWithoutEquipmentModelInputObjectSchema)]), create: z.union([z.lazy(() => EquipmentApplicabilityCreateWithoutEquipmentModelInputObjectSchema), z.lazy(() => EquipmentApplicabilityUncheckedCreateWithoutEquipmentModelInputObjectSchema)])
}).strict() as SchemaType;
