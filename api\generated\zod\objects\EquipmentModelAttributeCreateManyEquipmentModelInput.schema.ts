/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.EquipmentModelAttributeCreateManyEquipmentModelInput>;
export const EquipmentModelAttributeCreateManyEquipmentModelInputObjectSchema: SchemaType = z.object({
    id: z.number().optional().optional(), value: z.string(), numericValue: z.union([z.number(),
    z.null()]).optional().nullable(), templateId: z.number()
}).strict() as SchemaType;
