/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { EquipmentModelAttributeCreateManyEquipmentModelInputObjectSchema } from './EquipmentModelAttributeCreateManyEquipmentModelInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.EquipmentModelAttributeCreateManyEquipmentModelInputEnvelope>;
export const EquipmentModelAttributeCreateManyEquipmentModelInputEnvelopeObjectSchema: SchemaType = z.object({
    data: z.union([z.lazy(() => EquipmentModelAttributeCreateManyEquipmentModelInputObjectSchema),
    z.lazy(() => EquipmentModelAttributeCreateManyEquipmentModelInputObjectSchema).array()]), skipDuplicates: z.boolean().optional().optional()
}).strict() as SchemaType;
