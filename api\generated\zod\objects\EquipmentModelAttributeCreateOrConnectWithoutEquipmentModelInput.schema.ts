/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { EquipmentModelAttributeWhereUniqueInputObjectSchema } from './EquipmentModelAttributeWhereUniqueInput.schema';
import { EquipmentModelAttributeCreateWithoutEquipmentModelInputObjectSchema } from './EquipmentModelAttributeCreateWithoutEquipmentModelInput.schema';
import { EquipmentModelAttributeUncheckedCreateWithoutEquipmentModelInputObjectSchema } from './EquipmentModelAttributeUncheckedCreateWithoutEquipmentModelInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.EquipmentModelAttributeCreateOrConnectWithoutEquipmentModelInput>;
export const EquipmentModelAttributeCreateOrConnectWithoutEquipmentModelInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => EquipmentModelAttributeWhereUniqueInputObjectSchema), create: z.union([z.lazy(() => EquipmentModelAttributeCreateWithoutEquipmentModelInputObjectSchema), z.lazy(() => EquipmentModelAttributeUncheckedCreateWithoutEquipmentModelInputObjectSchema)])
}).strict() as SchemaType;
