/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { AttributeTemplateCreateNestedOneWithoutEquipmentAttributesInputObjectSchema } from './AttributeTemplateCreateNestedOneWithoutEquipmentAttributesInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.EquipmentModelAttributeCreateWithoutEquipmentModelInput>;
export const EquipmentModelAttributeCreateWithoutEquipmentModelInputObjectSchema: SchemaType = z.object({
    value: z.string(), numericValue: z.union([z.number(),
    z.null()]).optional().nullable(), template: z.lazy(() => AttributeTemplateCreateNestedOneWithoutEquipmentAttributesInputObjectSchema)
}).strict() as SchemaType;
