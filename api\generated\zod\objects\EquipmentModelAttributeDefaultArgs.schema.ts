/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { EquipmentModelAttributeSelectObjectSchema } from './EquipmentModelAttributeSelect.schema';
import { EquipmentModelAttributeIncludeObjectSchema } from './EquipmentModelAttributeInclude.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.EquipmentModelAttributeDefaultArgs>;
export const EquipmentModelAttributeDefaultArgsObjectSchema: SchemaType = z.object({
    select: z.lazy(() => EquipmentModelAttributeSelectObjectSchema).optional().optional(), include: z.lazy(() => EquipmentModelAttributeIncludeObjectSchema).optional().optional()
}).strict() as SchemaType;
