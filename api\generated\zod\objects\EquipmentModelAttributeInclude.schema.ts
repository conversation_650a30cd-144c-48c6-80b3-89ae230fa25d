/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { EquipmentModelDefaultArgsObjectSchema } from './EquipmentModelDefaultArgs.schema';
import { AttributeTemplateDefaultArgsObjectSchema } from './AttributeTemplateDefaultArgs.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.EquipmentModelAttributeInclude>;
export const EquipmentModelAttributeIncludeObjectSchema: SchemaType = z.object({
    equipmentModel: z.union([z.boolean(),
    z.lazy(() => EquipmentModelDefaultArgsObjectSchema)]).optional(), template: z.union([z.boolean(),
    z.lazy(() => AttributeTemplateDefaultArgsObjectSchema)]).optional()
}).strict() as SchemaType;
