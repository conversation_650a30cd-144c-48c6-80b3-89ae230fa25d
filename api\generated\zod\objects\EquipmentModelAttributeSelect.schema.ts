/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { EquipmentModelDefaultArgsObjectSchema } from './EquipmentModelDefaultArgs.schema';
import { AttributeTemplateDefaultArgsObjectSchema } from './AttributeTemplateDefaultArgs.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.EquipmentModelAttributeSelect>;
export const EquipmentModelAttributeSelectObjectSchema: SchemaType = z.object({
    id: z.boolean().optional().optional(), value: z.boolean().optional().optional(), numericValue: z.boolean().optional().optional(), equipmentModel: z.union([z.boolean(),
    z.lazy(() => EquipmentModelDefaultArgsObjectSchema)]).optional(), equipmentModelId: z.boolean().optional().optional(), template: z.union([z.boolean(),
    z.lazy(() => AttributeTemplateDefaultArgsObjectSchema)]).optional(), templateId: z.boolean().optional().optional()
}).strict() as SchemaType;
