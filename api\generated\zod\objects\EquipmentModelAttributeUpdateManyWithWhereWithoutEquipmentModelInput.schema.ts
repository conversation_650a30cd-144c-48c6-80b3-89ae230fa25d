/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { EquipmentModelAttributeScalarWhereInputObjectSchema } from './EquipmentModelAttributeScalarWhereInput.schema';
import { EquipmentModelAttributeUpdateManyMutationInputObjectSchema } from './EquipmentModelAttributeUpdateManyMutationInput.schema';
import { EquipmentModelAttributeUncheckedUpdateManyWithoutEquipmentModelInputObjectSchema } from './EquipmentModelAttributeUncheckedUpdateManyWithoutEquipmentModelInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.EquipmentModelAttributeUpdateManyWithWhereWithoutEquipmentModelInput>;
export const EquipmentModelAttributeUpdateManyWithWhereWithoutEquipmentModelInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => EquipmentModelAttributeScalarWhereInputObjectSchema), data: z.union([z.lazy(() => EquipmentModelAttributeUpdateManyMutationInputObjectSchema), z.lazy(() => EquipmentModelAttributeUncheckedUpdateManyWithoutEquipmentModelInputObjectSchema)])
}).strict() as SchemaType;
