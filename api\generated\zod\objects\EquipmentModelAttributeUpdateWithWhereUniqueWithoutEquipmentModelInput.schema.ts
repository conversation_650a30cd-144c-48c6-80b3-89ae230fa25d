/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { EquipmentModelAttributeWhereUniqueInputObjectSchema } from './EquipmentModelAttributeWhereUniqueInput.schema';
import { EquipmentModelAttributeUpdateWithoutEquipmentModelInputObjectSchema } from './EquipmentModelAttributeUpdateWithoutEquipmentModelInput.schema';
import { EquipmentModelAttributeUncheckedUpdateWithoutEquipmentModelInputObjectSchema } from './EquipmentModelAttributeUncheckedUpdateWithoutEquipmentModelInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.EquipmentModelAttributeUpdateWithWhereUniqueWithoutEquipmentModelInput>;
export const EquipmentModelAttributeUpdateWithWhereUniqueWithoutEquipmentModelInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => EquipmentModelAttributeWhereUniqueInputObjectSchema), data: z.union([z.lazy(() => EquipmentModelAttributeUpdateWithoutEquipmentModelInputObjectSchema), z.lazy(() => EquipmentModelAttributeUncheckedUpdateWithoutEquipmentModelInputObjectSchema)])
}).strict() as SchemaType;
