/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { StringFieldUpdateOperationsInputObjectSchema } from './StringFieldUpdateOperationsInput.schema';
import { NullableFloatFieldUpdateOperationsInputObjectSchema } from './NullableFloatFieldUpdateOperationsInput.schema';
import { AttributeTemplateUpdateOneRequiredWithoutEquipmentAttributesNestedInputObjectSchema } from './AttributeTemplateUpdateOneRequiredWithoutEquipmentAttributesNestedInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.EquipmentModelAttributeUpdateWithoutEquipmentModelInput>;
export const EquipmentModelAttributeUpdateWithoutEquipmentModelInputObjectSchema: SchemaType = z.object({
    value: z.union([z.string(),
    z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(), numericValue: z.union([z.number(),
    z.lazy(() => NullableFloatFieldUpdateOperationsInputObjectSchema),
    z.null()]).optional().nullable(), template: z.lazy(() => AttributeTemplateUpdateOneRequiredWithoutEquipmentAttributesNestedInputObjectSchema).optional().optional()
}).strict() as SchemaType;
