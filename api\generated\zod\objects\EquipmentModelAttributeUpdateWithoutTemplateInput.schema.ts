/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { StringFieldUpdateOperationsInputObjectSchema } from './StringFieldUpdateOperationsInput.schema';
import { NullableFloatFieldUpdateOperationsInputObjectSchema } from './NullableFloatFieldUpdateOperationsInput.schema';
import { EquipmentModelUpdateOneRequiredWithoutAttributesNestedInputObjectSchema } from './EquipmentModelUpdateOneRequiredWithoutAttributesNestedInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.EquipmentModelAttributeUpdateWithoutTemplateInput>;
export const EquipmentModelAttributeUpdateWithoutTemplateInputObjectSchema: SchemaType = z.object({
    value: z.union([z.string(),
    z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(), numericValue: z.union([z.number(),
    z.lazy(() => NullableFloatFieldUpdateOperationsInputObjectSchema),
    z.null()]).optional().nullable(), equipmentModel: z.lazy(() => EquipmentModelUpdateOneRequiredWithoutAttributesNestedInputObjectSchema).optional().optional()
}).strict() as SchemaType;
