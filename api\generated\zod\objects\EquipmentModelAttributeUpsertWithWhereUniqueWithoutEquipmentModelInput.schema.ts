/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { EquipmentModelAttributeWhereUniqueInputObjectSchema } from './EquipmentModelAttributeWhereUniqueInput.schema';
import { EquipmentModelAttributeUpdateWithoutEquipmentModelInputObjectSchema } from './EquipmentModelAttributeUpdateWithoutEquipmentModelInput.schema';
import { EquipmentModelAttributeUncheckedUpdateWithoutEquipmentModelInputObjectSchema } from './EquipmentModelAttributeUncheckedUpdateWithoutEquipmentModelInput.schema';
import { EquipmentModelAttributeCreateWithoutEquipmentModelInputObjectSchema } from './EquipmentModelAttributeCreateWithoutEquipmentModelInput.schema';
import { EquipmentModelAttributeUncheckedCreateWithoutEquipmentModelInputObjectSchema } from './EquipmentModelAttributeUncheckedCreateWithoutEquipmentModelInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.EquipmentModelAttributeUpsertWithWhereUniqueWithoutEquipmentModelInput>;
export const EquipmentModelAttributeUpsertWithWhereUniqueWithoutEquipmentModelInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => EquipmentModelAttributeWhereUniqueInputObjectSchema), update: z.union([z.lazy(() => EquipmentModelAttributeUpdateWithoutEquipmentModelInputObjectSchema), z.lazy(() => EquipmentModelAttributeUncheckedUpdateWithoutEquipmentModelInputObjectSchema)]), create: z.union([z.lazy(() => EquipmentModelAttributeCreateWithoutEquipmentModelInputObjectSchema), z.lazy(() => EquipmentModelAttributeUncheckedCreateWithoutEquipmentModelInputObjectSchema)])
}).strict() as SchemaType;
