/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.EquipmentModelCountAggregateInputType>;
export const EquipmentModelCountAggregateInputObjectSchema: SchemaType = z.object({
    id: z.literal(true).optional().optional(), createdAt: z.literal(true).optional().optional(), updatedAt: z.literal(true).optional().optional(), name: z.literal(true).optional().optional(), brandId: z.literal(true).optional().optional(), _all: z.literal(true).optional().optional()
}).strict() as SchemaType;
