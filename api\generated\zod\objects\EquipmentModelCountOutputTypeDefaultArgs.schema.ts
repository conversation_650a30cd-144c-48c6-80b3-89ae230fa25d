/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { EquipmentModelCountOutputTypeSelectObjectSchema } from './EquipmentModelCountOutputTypeSelect.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.EquipmentModelCountOutputTypeDefaultArgs>;
export const EquipmentModelCountOutputTypeDefaultArgsObjectSchema: SchemaType = z.object({
    select: z.lazy(() => EquipmentModelCountOutputTypeSelectObjectSchema).optional().optional()
}).strict() as SchemaType;
