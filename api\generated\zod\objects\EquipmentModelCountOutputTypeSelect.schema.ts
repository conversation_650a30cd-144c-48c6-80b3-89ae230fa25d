/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.EquipmentModelCountOutputTypeSelect>;
export const EquipmentModelCountOutputTypeSelectObjectSchema: SchemaType = z.object({
    partApplicabilities: z.boolean().optional().optional(), attributes: z.boolean().optional().optional()
}).strict() as SchemaType;
