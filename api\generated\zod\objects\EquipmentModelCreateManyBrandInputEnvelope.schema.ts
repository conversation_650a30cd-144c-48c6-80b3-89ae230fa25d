/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { EquipmentModelCreateManyBrandInputObjectSchema } from './EquipmentModelCreateManyBrandInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.EquipmentModelCreateManyBrandInputEnvelope>;
export const EquipmentModelCreateManyBrandInputEnvelopeObjectSchema: SchemaType = z.object({
    data: z.union([z.lazy(() => EquipmentModelCreateManyBrandInputObjectSchema),
    z.lazy(() => EquipmentModelCreateManyBrandInputObjectSchema).array()]), skipDuplicates: z.boolean().optional().optional()
}).strict() as SchemaType;
