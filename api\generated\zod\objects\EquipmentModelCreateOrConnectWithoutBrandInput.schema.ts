/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { EquipmentModelWhereUniqueInputObjectSchema } from './EquipmentModelWhereUniqueInput.schema';
import { EquipmentModelCreateWithoutBrandInputObjectSchema } from './EquipmentModelCreateWithoutBrandInput.schema';
import { EquipmentModelUncheckedCreateWithoutBrandInputObjectSchema } from './EquipmentModelUncheckedCreateWithoutBrandInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.EquipmentModelCreateOrConnectWithoutBrandInput>;
export const EquipmentModelCreateOrConnectWithoutBrandInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => EquipmentModelWhereUniqueInputObjectSchema), create: z.union([z.lazy(() => EquipmentModelCreateWithoutBrandInputObjectSchema), z.lazy(() => EquipmentModelUncheckedCreateWithoutBrandInputObjectSchema)])
}).strict() as SchemaType;
