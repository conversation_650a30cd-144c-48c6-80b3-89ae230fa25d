/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { EquipmentApplicabilityCreateNestedManyWithoutEquipmentModelInputObjectSchema } from './EquipmentApplicabilityCreateNestedManyWithoutEquipmentModelInput.schema';
import { EquipmentModelAttributeCreateNestedManyWithoutEquipmentModelInputObjectSchema } from './EquipmentModelAttributeCreateNestedManyWithoutEquipmentModelInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.EquipmentModelCreateWithoutBrandInput>;
export const EquipmentModelCreateWithoutBrandInputObjectSchema: SchemaType = z.object({
    id: z.string().optional().optional(), createdAt: z.union([z.date().optional(), z.string().datetime().optional()]).optional(), updatedAt: z.union([z.date().optional(), z.string().datetime().optional()]).optional(), name: z.string(), partApplicabilities: z.lazy(() => EquipmentApplicabilityCreateNestedManyWithoutEquipmentModelInputObjectSchema).optional().optional(), attributes: z.lazy(() => EquipmentModelAttributeCreateNestedManyWithoutEquipmentModelInputObjectSchema).optional().optional()
}).strict() as SchemaType;
