/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { EquipmentModelSelectObjectSchema } from './EquipmentModelSelect.schema';
import { EquipmentModelIncludeObjectSchema } from './EquipmentModelInclude.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.EquipmentModelDefaultArgs>;
export const EquipmentModelDefaultArgsObjectSchema: SchemaType = z.object({
    select: z.lazy(() => EquipmentModelSelectObjectSchema).optional().optional(), include: z.lazy(() => EquipmentModelIncludeObjectSchema).optional().optional()
}).strict() as SchemaType;
