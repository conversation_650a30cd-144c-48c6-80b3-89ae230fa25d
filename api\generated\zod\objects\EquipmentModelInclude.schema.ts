/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { EquipmentApplicabilityInputSchema } from '../input/EquipmentApplicabilityInput.schema';
import { BrandDefaultArgsObjectSchema } from './BrandDefaultArgs.schema';
import { EquipmentModelAttributeInputSchema } from '../input/EquipmentModelAttributeInput.schema';
import { EquipmentModelCountOutputTypeDefaultArgsObjectSchema } from './EquipmentModelCountOutputTypeDefaultArgs.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.EquipmentModelInclude>;
export const EquipmentModelIncludeObjectSchema: SchemaType = z.object({
    partApplicabilities: z.union([z.boolean(),
    z.lazy(() => EquipmentApplicabilityInputSchema.findMany)]).optional(), brand: z.union([z.boolean(),
    z.lazy(() => BrandDefaultArgsObjectSchema)]).optional(), attributes: z.union([z.boolean(),
    z.lazy(() => EquipmentModelAttributeInputSchema.findMany)]).optional(), _count: z.union([z.boolean(),
    z.lazy(() => EquipmentModelCountOutputTypeDefaultArgsObjectSchema)]).optional()
}).strict() as SchemaType;
