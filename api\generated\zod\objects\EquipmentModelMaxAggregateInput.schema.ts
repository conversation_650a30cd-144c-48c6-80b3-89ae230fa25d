/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.EquipmentModelMaxAggregateInputType>;
export const EquipmentModelMaxAggregateInputObjectSchema: SchemaType = z.object({
    id: z.literal(true).optional().optional(), createdAt: z.literal(true).optional().optional(), updatedAt: z.literal(true).optional().optional(), name: z.literal(true).optional().optional(), brandId: z.literal(true).optional().optional()
}).strict() as SchemaType;
