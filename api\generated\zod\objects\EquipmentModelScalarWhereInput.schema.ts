/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { StringFilterObjectSchema } from './StringFilter.schema';
import { DateTimeFilterObjectSchema } from './DateTimeFilter.schema';
import { IntNullableFilterObjectSchema } from './IntNullableFilter.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.EquipmentModelScalarWhereInput>;
export const EquipmentModelScalarWhereInputObjectSchema: SchemaType = z.object({
    AND: z.union([z.lazy(() => EquipmentModelScalarWhereInputObjectSchema),
    z.lazy(() => EquipmentModelScalarWhereInputObjectSchema).array()]).optional(), OR: z.lazy(() => EquipmentModelScalarWhereInputObjectSchema).array().optional().optional(), NOT: z.union([z.lazy(() => EquipmentModelScalarWhereInputObjectSchema),
    z.lazy(() => EquipmentModelScalarWhereInputObjectSchema).array()]).optional(), id: z.union([z.lazy(() => StringFilterObjectSchema),
    z.string()]).optional(), createdAt: z.union([z.lazy(() => DateTimeFilterObjectSchema),
    z.union([z.date(), z.string().datetime().optional()])]).optional(), updatedAt: z.union([z.lazy(() => DateTimeFilterObjectSchema),
    z.union([z.date(), z.string().datetime().optional()])]).optional(), name: z.union([z.lazy(() => StringFilterObjectSchema),
    z.string()]).optional(), brandId: z.union([z.lazy(() => IntNullableFilterObjectSchema),
    z.number(),
    z.null()]).optional().nullable()
}).strict() as SchemaType;
