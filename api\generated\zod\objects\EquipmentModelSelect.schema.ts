/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { EquipmentApplicabilityInputSchema } from '../input/EquipmentApplicabilityInput.schema';
import { BrandDefaultArgsObjectSchema } from './BrandDefaultArgs.schema';
import { EquipmentModelAttributeInputSchema } from '../input/EquipmentModelAttributeInput.schema';
import { EquipmentModelCountOutputTypeDefaultArgsObjectSchema } from './EquipmentModelCountOutputTypeDefaultArgs.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.EquipmentModelSelect>;
export const EquipmentModelSelectObjectSchema: SchemaType = z.object({
    id: z.boolean().optional().optional(), createdAt: z.boolean().optional().optional(), updatedAt: z.boolean().optional().optional(), name: z.boolean().optional().optional(), partApplicabilities: z.union([z.boolean(),
    z.lazy(() => EquipmentApplicabilityInputSchema.findMany)]).optional(), brand: z.union([z.boolean(),
    z.lazy(() => BrandDefaultArgsObjectSchema)]).optional(), brandId: z.boolean().optional().optional(), attributes: z.union([z.boolean(),
    z.lazy(() => EquipmentModelAttributeInputSchema.findMany)]).optional(), _count: z.union([z.boolean(),
    z.lazy(() => EquipmentModelCountOutputTypeDefaultArgsObjectSchema)]).optional()
}).strict() as SchemaType;
