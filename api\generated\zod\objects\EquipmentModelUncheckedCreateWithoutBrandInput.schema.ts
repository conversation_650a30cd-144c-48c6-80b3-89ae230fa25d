/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { EquipmentApplicabilityUncheckedCreateNestedManyWithoutEquipmentModelInputObjectSchema } from './EquipmentApplicabilityUncheckedCreateNestedManyWithoutEquipmentModelInput.schema';
import { EquipmentModelAttributeUncheckedCreateNestedManyWithoutEquipmentModelInputObjectSchema } from './EquipmentModelAttributeUncheckedCreateNestedManyWithoutEquipmentModelInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.EquipmentModelUncheckedCreateWithoutBrandInput>;
export const EquipmentModelUncheckedCreateWithoutBrandInputObjectSchema: SchemaType = z.object({
    id: z.string().optional().optional(), createdAt: z.union([z.date().optional(), z.string().datetime().optional()]).optional(), updatedAt: z.union([z.date().optional(), z.string().datetime().optional()]).optional(), name: z.string(), partApplicabilities: z.lazy(() => EquipmentApplicabilityUncheckedCreateNestedManyWithoutEquipmentModelInputObjectSchema).optional().optional(), attributes: z.lazy(() => EquipmentModelAttributeUncheckedCreateNestedManyWithoutEquipmentModelInputObjectSchema).optional().optional()
}).strict() as SchemaType;
