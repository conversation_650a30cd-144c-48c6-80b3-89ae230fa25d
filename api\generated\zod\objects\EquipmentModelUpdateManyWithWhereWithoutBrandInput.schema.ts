/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { EquipmentModelScalarWhereInputObjectSchema } from './EquipmentModelScalarWhereInput.schema';
import { EquipmentModelUpdateManyMutationInputObjectSchema } from './EquipmentModelUpdateManyMutationInput.schema';
import { EquipmentModelUncheckedUpdateManyWithoutBrandInputObjectSchema } from './EquipmentModelUncheckedUpdateManyWithoutBrandInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.EquipmentModelUpdateManyWithWhereWithoutBrandInput>;
export const EquipmentModelUpdateManyWithWhereWithoutBrandInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => EquipmentModelScalarWhereInputObjectSchema), data: z.union([z.lazy(() => EquipmentModelUpdateManyMutationInputObjectSchema), z.lazy(() => EquipmentModelUncheckedUpdateManyWithoutBrandInputObjectSchema)])
}).strict() as SchemaType;
