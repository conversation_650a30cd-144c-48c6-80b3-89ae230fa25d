/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { EquipmentModelWhereUniqueInputObjectSchema } from './EquipmentModelWhereUniqueInput.schema';
import { EquipmentModelUpdateWithoutBrandInputObjectSchema } from './EquipmentModelUpdateWithoutBrandInput.schema';
import { EquipmentModelUncheckedUpdateWithoutBrandInputObjectSchema } from './EquipmentModelUncheckedUpdateWithoutBrandInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.EquipmentModelUpdateWithWhereUniqueWithoutBrandInput>;
export const EquipmentModelUpdateWithWhereUniqueWithoutBrandInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => EquipmentModelWhereUniqueInputObjectSchema), data: z.union([z.lazy(() => EquipmentModelUpdateWithoutBrandInputObjectSchema), z.lazy(() => EquipmentModelUncheckedUpdateWithoutBrandInputObjectSchema)])
}).strict() as SchemaType;
