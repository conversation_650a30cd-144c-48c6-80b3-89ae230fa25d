/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { StringFieldUpdateOperationsInputObjectSchema } from './StringFieldUpdateOperationsInput.schema';
import { DateTimeFieldUpdateOperationsInputObjectSchema } from './DateTimeFieldUpdateOperationsInput.schema';
import { EquipmentApplicabilityUpdateManyWithoutEquipmentModelNestedInputObjectSchema } from './EquipmentApplicabilityUpdateManyWithoutEquipmentModelNestedInput.schema';
import { EquipmentModelAttributeUpdateManyWithoutEquipmentModelNestedInputObjectSchema } from './EquipmentModelAttributeUpdateManyWithoutEquipmentModelNestedInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.EquipmentModelUpdateWithoutBrandInput>;
export const EquipmentModelUpdateWithoutBrandInputObjectSchema: SchemaType = z.object({
    id: z.union([z.string(),
    z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(), createdAt: z.union([z.union([z.date(), z.string().datetime().optional()]),
    z.lazy(() => DateTimeFieldUpdateOperationsInputObjectSchema)]).optional(), updatedAt: z.union([z.union([z.date(), z.string().datetime().optional()]),
    z.lazy(() => DateTimeFieldUpdateOperationsInputObjectSchema)]).optional(), name: z.union([z.string(),
    z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(), partApplicabilities: z.lazy(() => EquipmentApplicabilityUpdateManyWithoutEquipmentModelNestedInputObjectSchema).optional().optional(), attributes: z.lazy(() => EquipmentModelAttributeUpdateManyWithoutEquipmentModelNestedInputObjectSchema).optional().optional()
}).strict() as SchemaType;
