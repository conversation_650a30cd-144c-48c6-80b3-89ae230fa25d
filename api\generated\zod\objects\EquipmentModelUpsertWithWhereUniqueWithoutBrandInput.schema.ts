/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { EquipmentModelWhereUniqueInputObjectSchema } from './EquipmentModelWhereUniqueInput.schema';
import { EquipmentModelUpdateWithoutBrandInputObjectSchema } from './EquipmentModelUpdateWithoutBrandInput.schema';
import { EquipmentModelUncheckedUpdateWithoutBrandInputObjectSchema } from './EquipmentModelUncheckedUpdateWithoutBrandInput.schema';
import { EquipmentModelCreateWithoutBrandInputObjectSchema } from './EquipmentModelCreateWithoutBrandInput.schema';
import { EquipmentModelUncheckedCreateWithoutBrandInputObjectSchema } from './EquipmentModelUncheckedCreateWithoutBrandInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.EquipmentModelUpsertWithWhereUniqueWithoutBrandInput>;
export const EquipmentModelUpsertWithWhereUniqueWithoutBrandInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => EquipmentModelWhereUniqueInputObjectSchema), update: z.union([z.lazy(() => EquipmentModelUpdateWithoutBrandInputObjectSchema), z.lazy(() => EquipmentModelUncheckedUpdateWithoutBrandInputObjectSchema)]), create: z.union([z.lazy(() => EquipmentModelCreateWithoutBrandInputObjectSchema), z.lazy(() => EquipmentModelUncheckedCreateWithoutBrandInputObjectSchema)])
}).strict() as SchemaType;
