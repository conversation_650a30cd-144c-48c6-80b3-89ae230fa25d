/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { ApplicabilityAccuracySchema } from '../enums/ApplicabilityAccuracy.schema';
import { NullableJsonNullValueInputSchema } from '../enums/NullableJsonNullValueInput.schema';
import { ProposalStatusSchema } from '../enums/ProposalStatus.schema';

import type { Prisma } from '@prisma/client';


const literalSchema = z.union([z.string(), z.number(), z.boolean()]);
const jsonSchema: z.ZodType<Prisma.InputJsonValue> = z.lazy(() =>
    z.union([literalSchema, z.array(jsonSchema.nullable()), z.record(jsonSchema.nullable())])
);

type SchemaType = z.ZodType<Prisma.MatchingProposalCreateManyPartInput>;
export const MatchingProposalCreateManyPartInputObjectSchema: SchemaType = z.object({
    id: z.number().optional().optional(), catalogItemId: z.number(), accuracySuggestion: z.lazy(() => ApplicabilityAccuracySchema).optional().optional(), notesSuggestion: z.union([z.string(),
    z.null()]).optional().nullable(), details: z.union([z.lazy(() => NullableJsonNullValueInputSchema),
        jsonSchema]).optional(), status: z.lazy(() => ProposalStatusSchema).optional().optional(), createdAt: z.union([z.date().optional(), z.string().datetime().optional()]).optional(), updatedAt: z.union([z.date().optional(), z.string().datetime().optional()]).optional()
}).strict() as SchemaType;
