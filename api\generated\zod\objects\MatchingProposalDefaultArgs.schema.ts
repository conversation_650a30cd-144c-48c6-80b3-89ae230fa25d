/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { MatchingProposalSelectObjectSchema } from './MatchingProposalSelect.schema';
import { MatchingProposalIncludeObjectSchema } from './MatchingProposalInclude.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.MatchingProposalDefaultArgs>;
export const MatchingProposalDefaultArgsObjectSchema: SchemaType = z.object({
    select: z.lazy(() => MatchingProposalSelectObjectSchema).optional().optional(), include: z.lazy(() => MatchingProposalIncludeObjectSchema).optional().optional()
}).strict() as SchemaType;
