/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { CatalogItemDefaultArgsObjectSchema } from './CatalogItemDefaultArgs.schema';
import { PartDefaultArgsObjectSchema } from './PartDefaultArgs.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.MatchingProposalInclude>;
export const MatchingProposalIncludeObjectSchema: SchemaType = z.object({
    catalogItem: z.union([z.boolean(),
    z.lazy(() => CatalogItemDefaultArgsObjectSchema)]).optional(), part: z.union([z.boolean(),
    z.lazy(() => PartDefaultArgsObjectSchema)]).optional()
}).strict() as SchemaType;
