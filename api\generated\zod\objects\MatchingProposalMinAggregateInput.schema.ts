/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.MatchingProposalMinAggregateInputType>;
export const MatchingProposalMinAggregateInputObjectSchema: SchemaType = z.object({
    id: z.literal(true).optional().optional(), catalogItemId: z.literal(true).optional().optional(), partId: z.literal(true).optional().optional(), accuracySuggestion: z.literal(true).optional().optional(), notesSuggestion: z.literal(true).optional().optional(), status: z.literal(true).optional().optional(), createdAt: z.literal(true).optional().optional(), updatedAt: z.literal(true).optional().optional()
}).strict() as SchemaType;
