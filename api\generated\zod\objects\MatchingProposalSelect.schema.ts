/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { CatalogItemDefaultArgsObjectSchema } from './CatalogItemDefaultArgs.schema';
import { PartDefaultArgsObjectSchema } from './PartDefaultArgs.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.MatchingProposalSelect>;
export const MatchingProposalSelectObjectSchema: SchemaType = z.object({
    id: z.boolean().optional().optional(), catalogItem: z.union([z.boolean(),
    z.lazy(() => CatalogItemDefaultArgsObjectSchema)]).optional(), catalogItemId: z.boolean().optional().optional(), part: z.union([z.boolean(),
    z.lazy(() => PartDefaultArgsObjectSchema)]).optional(), partId: z.boolean().optional().optional(), accuracySuggestion: z.boolean().optional().optional(), notesSuggestion: z.boolean().optional().optional(), details: z.boolean().optional().optional(), status: z.boolean().optional().optional(), createdAt: z.boolean().optional().optional(), updatedAt: z.boolean().optional().optional()
}).strict() as SchemaType;
