/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { MatchingProposalScalarWhereInputObjectSchema } from './MatchingProposalScalarWhereInput.schema';
import { MatchingProposalUpdateManyMutationInputObjectSchema } from './MatchingProposalUpdateManyMutationInput.schema';
import { MatchingProposalUncheckedUpdateManyWithoutCatalogItemInputObjectSchema } from './MatchingProposalUncheckedUpdateManyWithoutCatalogItemInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.MatchingProposalUpdateManyWithWhereWithoutCatalogItemInput>;
export const MatchingProposalUpdateManyWithWhereWithoutCatalogItemInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => MatchingProposalScalarWhereInputObjectSchema), data: z.union([z.lazy(() => MatchingProposalUpdateManyMutationInputObjectSchema), z.lazy(() => MatchingProposalUncheckedUpdateManyWithoutCatalogItemInputObjectSchema)])
}).strict() as SchemaType;
