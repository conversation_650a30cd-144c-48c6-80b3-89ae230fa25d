/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { MatchingProposalWhereUniqueInputObjectSchema } from './MatchingProposalWhereUniqueInput.schema';
import { MatchingProposalUpdateWithoutCatalogItemInputObjectSchema } from './MatchingProposalUpdateWithoutCatalogItemInput.schema';
import { MatchingProposalUncheckedUpdateWithoutCatalogItemInputObjectSchema } from './MatchingProposalUncheckedUpdateWithoutCatalogItemInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.MatchingProposalUpdateWithWhereUniqueWithoutCatalogItemInput>;
export const MatchingProposalUpdateWithWhereUniqueWithoutCatalogItemInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => MatchingProposalWhereUniqueInputObjectSchema), data: z.union([z.lazy(() => MatchingProposalUpdateWithoutCatalogItemInputObjectSchema), z.lazy(() => MatchingProposalUncheckedUpdateWithoutCatalogItemInputObjectSchema)])
}).strict() as SchemaType;
