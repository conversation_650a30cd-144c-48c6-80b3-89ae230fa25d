/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { ApplicabilityAccuracySchema } from '../enums/ApplicabilityAccuracy.schema';
import { EnumApplicabilityAccuracyFieldUpdateOperationsInputObjectSchema } from './EnumApplicabilityAccuracyFieldUpdateOperationsInput.schema';
import { NullableStringFieldUpdateOperationsInputObjectSchema } from './NullableStringFieldUpdateOperationsInput.schema';
import { NullableJsonNullValueInputSchema } from '../enums/NullableJsonNullValueInput.schema';
import { ProposalStatusSchema } from '../enums/ProposalStatus.schema';
import { EnumProposalStatusFieldUpdateOperationsInputObjectSchema } from './EnumProposalStatusFieldUpdateOperationsInput.schema';
import { DateTimeFieldUpdateOperationsInputObjectSchema } from './DateTimeFieldUpdateOperationsInput.schema';
import { PartUpdateOneRequiredWithoutMatchingProposalsNestedInputObjectSchema } from './PartUpdateOneRequiredWithoutMatchingProposalsNestedInput.schema';

import type { Prisma } from '@prisma/client';


const literalSchema = z.union([z.string(), z.number(), z.boolean()]);
const jsonSchema: z.ZodType<Prisma.InputJsonValue> = z.lazy(() =>
    z.union([literalSchema, z.array(jsonSchema.nullable()), z.record(jsonSchema.nullable())])
);

type SchemaType = z.ZodType<Prisma.MatchingProposalUpdateWithoutCatalogItemInput>;
export const MatchingProposalUpdateWithoutCatalogItemInputObjectSchema: SchemaType = z.object({
    accuracySuggestion: z.union([z.lazy(() => ApplicabilityAccuracySchema),
    z.lazy(() => EnumApplicabilityAccuracyFieldUpdateOperationsInputObjectSchema)]).optional(), notesSuggestion: z.union([z.string(),
    z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
    z.null()]).optional().nullable(), details: z.union([z.lazy(() => NullableJsonNullValueInputSchema),
        jsonSchema]).optional(), status: z.union([z.lazy(() => ProposalStatusSchema),
        z.lazy(() => EnumProposalStatusFieldUpdateOperationsInputObjectSchema)]).optional(), createdAt: z.union([z.union([z.date(), z.string().datetime().optional()]),
        z.lazy(() => DateTimeFieldUpdateOperationsInputObjectSchema)]).optional(), updatedAt: z.union([z.union([z.date(), z.string().datetime().optional()]),
        z.lazy(() => DateTimeFieldUpdateOperationsInputObjectSchema)]).optional(), part: z.lazy(() => PartUpdateOneRequiredWithoutMatchingProposalsNestedInputObjectSchema).optional().optional()
}).strict() as SchemaType;
