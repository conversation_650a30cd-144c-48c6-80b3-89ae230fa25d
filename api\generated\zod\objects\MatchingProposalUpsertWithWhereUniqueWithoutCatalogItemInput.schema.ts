/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { MatchingProposalWhereUniqueInputObjectSchema } from './MatchingProposalWhereUniqueInput.schema';
import { MatchingProposalUpdateWithoutCatalogItemInputObjectSchema } from './MatchingProposalUpdateWithoutCatalogItemInput.schema';
import { MatchingProposalUncheckedUpdateWithoutCatalogItemInputObjectSchema } from './MatchingProposalUncheckedUpdateWithoutCatalogItemInput.schema';
import { MatchingProposalCreateWithoutCatalogItemInputObjectSchema } from './MatchingProposalCreateWithoutCatalogItemInput.schema';
import { MatchingProposalUncheckedCreateWithoutCatalogItemInputObjectSchema } from './MatchingProposalUncheckedCreateWithoutCatalogItemInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.MatchingProposalUpsertWithWhereUniqueWithoutCatalogItemInput>;
export const MatchingProposalUpsertWithWhereUniqueWithoutCatalogItemInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => MatchingProposalWhereUniqueInputObjectSchema), update: z.union([z.lazy(() => MatchingProposalUpdateWithoutCatalogItemInputObjectSchema), z.lazy(() => MatchingProposalUncheckedUpdateWithoutCatalogItemInputObjectSchema)]), create: z.union([z.lazy(() => MatchingProposalCreateWithoutCatalogItemInputObjectSchema), z.lazy(() => MatchingProposalUncheckedCreateWithoutCatalogItemInputObjectSchema)])
}).strict() as SchemaType;
