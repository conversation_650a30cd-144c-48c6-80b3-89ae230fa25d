/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { MatchingProposalWhereUniqueInputObjectSchema } from './MatchingProposalWhereUniqueInput.schema';
import { MatchingProposalUpdateWithoutPartInputObjectSchema } from './MatchingProposalUpdateWithoutPartInput.schema';
import { MatchingProposalUncheckedUpdateWithoutPartInputObjectSchema } from './MatchingProposalUncheckedUpdateWithoutPartInput.schema';
import { MatchingProposalCreateWithoutPartInputObjectSchema } from './MatchingProposalCreateWithoutPartInput.schema';
import { MatchingProposalUncheckedCreateWithoutPartInputObjectSchema } from './MatchingProposalUncheckedCreateWithoutPartInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.MatchingProposalUpsertWithWhereUniqueWithoutPartInput>;
export const MatchingProposalUpsertWithWhereUniqueWithoutPartInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => MatchingProposalWhereUniqueInputObjectSchema), update: z.union([z.lazy(() => MatchingProposalUpdateWithoutPartInputObjectSchema), z.lazy(() => MatchingProposalUncheckedUpdateWithoutPartInputObjectSchema)]), create: z.union([z.lazy(() => MatchingProposalCreateWithoutPartInputObjectSchema), z.lazy(() => MatchingProposalUncheckedCreateWithoutPartInputObjectSchema)])
}).strict() as SchemaType;
