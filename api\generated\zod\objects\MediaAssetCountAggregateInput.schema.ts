/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.MediaAssetCountAggregateInputType>;
export const MediaAssetCountAggregateInputObjectSchema: SchemaType = z.object({
    id: z.literal(true).optional().optional(), fileName: z.literal(true).optional().optional(), mimeType: z.literal(true).optional().optional(), fileSize: z.literal(true).optional().optional(), url: z.literal(true).optional().optional(), createdAt: z.literal(true).optional().optional(), updatedAt: z.literal(true).optional().optional(), _all: z.literal(true).optional().optional()
}).strict() as SchemaType;
