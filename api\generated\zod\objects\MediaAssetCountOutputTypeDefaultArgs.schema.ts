/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { MediaAssetCountOutputTypeSelectObjectSchema } from './MediaAssetCountOutputTypeSelect.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.MediaAssetCountOutputTypeDefaultArgs>;
export const MediaAssetCountOutputTypeDefaultArgsObjectSchema: SchemaType = z.object({
    select: z.lazy(() => MediaAssetCountOutputTypeSelectObjectSchema).optional().optional()
}).strict() as SchemaType;
