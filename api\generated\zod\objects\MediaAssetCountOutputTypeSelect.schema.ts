/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.MediaAssetCountOutputTypeSelect>;
export const MediaAssetCountOutputTypeSelectObjectSchema: SchemaType = z.object({
    parts: z.boolean().optional().optional(), catalogItems: z.boolean().optional().optional()
}).strict() as SchemaType;
