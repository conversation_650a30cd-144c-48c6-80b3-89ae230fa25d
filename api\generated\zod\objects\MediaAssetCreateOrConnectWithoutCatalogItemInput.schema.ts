/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { MediaAssetWhereUniqueInputObjectSchema } from './MediaAssetWhereUniqueInput.schema';
import { MediaAssetCreateWithoutCatalogItemInputObjectSchema } from './MediaAssetCreateWithoutCatalogItemInput.schema';
import { MediaAssetUncheckedCreateWithoutCatalogItemInputObjectSchema } from './MediaAssetUncheckedCreateWithoutCatalogItemInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.MediaAssetCreateOrConnectWithoutCatalogItemInput>;
export const MediaAssetCreateOrConnectWithoutCatalogItemInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => MediaAssetWhereUniqueInputObjectSchema), create: z.union([z.lazy(() => MediaAssetCreateWithoutCatalogItemInputObjectSchema), z.lazy(() => MediaAssetUncheckedCreateWithoutCatalogItemInputObjectSchema)])
}).strict() as SchemaType;
