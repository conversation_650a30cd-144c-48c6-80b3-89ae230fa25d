/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { MediaAssetWhereUniqueInputObjectSchema } from './MediaAssetWhereUniqueInput.schema';
import { MediaAssetCreateWithoutCatalogItemsInputObjectSchema } from './MediaAssetCreateWithoutCatalogItemsInput.schema';
import { MediaAssetUncheckedCreateWithoutCatalogItemsInputObjectSchema } from './MediaAssetUncheckedCreateWithoutCatalogItemsInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.MediaAssetCreateOrConnectWithoutCatalogItemsInput>;
export const MediaAssetCreateOrConnectWithoutCatalogItemsInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => MediaAssetWhereUniqueInputObjectSchema), create: z.union([z.lazy(() => MediaAssetCreateWithoutCatalogItemsInputObjectSchema), z.lazy(() => MediaAssetUncheckedCreateWithoutCatalogItemsInputObjectSchema)])
}).strict() as SchemaType;
