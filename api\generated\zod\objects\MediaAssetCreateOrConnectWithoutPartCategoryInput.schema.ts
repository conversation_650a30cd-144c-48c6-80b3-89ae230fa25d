/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { MediaAssetWhereUniqueInputObjectSchema } from './MediaAssetWhereUniqueInput.schema';
import { MediaAssetCreateWithoutPartCategoryInputObjectSchema } from './MediaAssetCreateWithoutPartCategoryInput.schema';
import { MediaAssetUncheckedCreateWithoutPartCategoryInputObjectSchema } from './MediaAssetUncheckedCreateWithoutPartCategoryInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.MediaAssetCreateOrConnectWithoutPartCategoryInput>;
export const MediaAssetCreateOrConnectWithoutPartCategoryInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => MediaAssetWhereUniqueInputObjectSchema), create: z.union([z.lazy(() => MediaAssetCreateWithoutPartCategoryInputObjectSchema), z.lazy(() => MediaAssetUncheckedCreateWithoutPartCategoryInputObjectSchema)])
}).strict() as SchemaType;
