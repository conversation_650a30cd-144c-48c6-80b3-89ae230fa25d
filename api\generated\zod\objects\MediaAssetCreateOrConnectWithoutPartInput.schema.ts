/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { MediaAssetWhereUniqueInputObjectSchema } from './MediaAssetWhereUniqueInput.schema';
import { MediaAssetCreateWithoutPartInputObjectSchema } from './MediaAssetCreateWithoutPartInput.schema';
import { MediaAssetUncheckedCreateWithoutPartInputObjectSchema } from './MediaAssetUncheckedCreateWithoutPartInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.MediaAssetCreateOrConnectWithoutPartInput>;
export const MediaAssetCreateOrConnectWithoutPartInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => MediaAssetWhereUniqueInputObjectSchema), create: z.union([z.lazy(() => MediaAssetCreateWithoutPartInputObjectSchema), z.lazy(() => MediaAssetUncheckedCreateWithoutPartInputObjectSchema)])
}).strict() as SchemaType;
