/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { MediaAssetWhereUniqueInputObjectSchema } from './MediaAssetWhereUniqueInput.schema';
import { MediaAssetCreateWithoutPartsInputObjectSchema } from './MediaAssetCreateWithoutPartsInput.schema';
import { MediaAssetUncheckedCreateWithoutPartsInputObjectSchema } from './MediaAssetUncheckedCreateWithoutPartsInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.MediaAssetCreateOrConnectWithoutPartsInput>;
export const MediaAssetCreateOrConnectWithoutPartsInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => MediaAssetWhereUniqueInputObjectSchema), create: z.union([z.lazy(() => MediaAssetCreateWithoutPartsInputObjectSchema), z.lazy(() => MediaAssetUncheckedCreateWithoutPartsInputObjectSchema)])
}).strict() as SchemaType;
