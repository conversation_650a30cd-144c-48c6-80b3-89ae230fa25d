/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartCreateNestedOneWithoutImageInputObjectSchema } from './PartCreateNestedOneWithoutImageInput.schema';
import { PartCategoryCreateNestedOneWithoutImageInputObjectSchema } from './PartCategoryCreateNestedOneWithoutImageInput.schema';
import { PartCreateNestedManyWithoutMediaAssetsInputObjectSchema } from './PartCreateNestedManyWithoutMediaAssetsInput.schema';
import { CatalogItemCreateNestedOneWithoutImageInputObjectSchema } from './CatalogItemCreateNestedOneWithoutImageInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.MediaAssetCreateWithoutCatalogItemsInput>;
export const MediaAssetCreateWithoutCatalogItemsInputObjectSchema: SchemaType = z.object({
    fileName: z.string(), mimeType: z.string(), fileSize: z.union([z.number(),
    z.null()]).optional().nullable(), url: z.string(), createdAt: z.union([z.date().optional(), z.string().datetime().optional()]).optional(), updatedAt: z.union([z.date().optional(), z.string().datetime().optional()]).optional(), part: z.lazy(() => PartCreateNestedOneWithoutImageInputObjectSchema).optional().optional(), partCategory: z.lazy(() => PartCategoryCreateNestedOneWithoutImageInputObjectSchema).optional().optional(), parts: z.lazy(() => PartCreateNestedManyWithoutMediaAssetsInputObjectSchema).optional().optional(), catalogItem: z.lazy(() => CatalogItemCreateNestedOneWithoutImageInputObjectSchema).optional().optional()
}).strict() as SchemaType;
