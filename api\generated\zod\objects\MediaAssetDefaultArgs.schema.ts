/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { MediaAssetSelectObjectSchema } from './MediaAssetSelect.schema';
import { MediaAssetIncludeObjectSchema } from './MediaAssetInclude.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.MediaAssetDefaultArgs>;
export const MediaAssetDefaultArgsObjectSchema: SchemaType = z.object({
    select: z.lazy(() => MediaAssetSelectObjectSchema).optional().optional(), include: z.lazy(() => MediaAssetIncludeObjectSchema).optional().optional()
}).strict() as SchemaType;
