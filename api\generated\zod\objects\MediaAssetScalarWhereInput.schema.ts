/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { IntFilterObjectSchema } from './IntFilter.schema';
import { StringFilterObjectSchema } from './StringFilter.schema';
import { IntNullableFilterObjectSchema } from './IntNullableFilter.schema';
import { DateTimeFilterObjectSchema } from './DateTimeFilter.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.MediaAssetScalarWhereInput>;
export const MediaAssetScalarWhereInputObjectSchema: SchemaType = z.object({
    AND: z.union([z.lazy(() => MediaAssetScalarWhereInputObjectSchema),
    z.lazy(() => MediaAssetScalarWhereInputObjectSchema).array()]).optional(), OR: z.lazy(() => MediaAssetScalarWhereInputObjectSchema).array().optional().optional(), NOT: z.union([z.lazy(() => MediaAssetScalarWhereInputObjectSchema),
    z.lazy(() => MediaAssetScalarWhereInputObjectSchema).array()]).optional(), id: z.union([z.lazy(() => IntFilterObjectSchema),
    z.number()]).optional(), fileName: z.union([z.lazy(() => StringFilterObjectSchema),
    z.string()]).optional(), mimeType: z.union([z.lazy(() => StringFilterObjectSchema),
    z.string()]).optional(), fileSize: z.union([z.lazy(() => IntNullableFilterObjectSchema),
    z.number(),
    z.null()]).optional().nullable(), url: z.union([z.lazy(() => StringFilterObjectSchema),
    z.string()]).optional(), createdAt: z.union([z.lazy(() => DateTimeFilterObjectSchema),
    z.union([z.date(), z.string().datetime().optional()])]).optional(), updatedAt: z.union([z.lazy(() => DateTimeFilterObjectSchema),
    z.union([z.date(), z.string().datetime().optional()])]).optional()
}).strict() as SchemaType;
