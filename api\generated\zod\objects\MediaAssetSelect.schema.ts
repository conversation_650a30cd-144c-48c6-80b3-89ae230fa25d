/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartDefaultArgsObjectSchema } from './PartDefaultArgs.schema';
import { PartCategoryDefaultArgsObjectSchema } from './PartCategoryDefaultArgs.schema';
import { PartInputSchema } from '../input/PartInput.schema';
import { CatalogItemInputSchema } from '../input/CatalogItemInput.schema';
import { CatalogItemDefaultArgsObjectSchema } from './CatalogItemDefaultArgs.schema';
import { MediaAssetCountOutputTypeDefaultArgsObjectSchema } from './MediaAssetCountOutputTypeDefaultArgs.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.MediaAssetSelect>;
export const MediaAssetSelectObjectSchema: SchemaType = z.object({
    id: z.boolean().optional().optional(), fileName: z.boolean().optional().optional(), mimeType: z.boolean().optional().optional(), fileSize: z.boolean().optional().optional(), url: z.boolean().optional().optional(), createdAt: z.boolean().optional().optional(), updatedAt: z.boolean().optional().optional(), part: z.union([z.boolean(),
    z.lazy(() => PartDefaultArgsObjectSchema)]).optional(), partCategory: z.union([z.boolean(),
    z.lazy(() => PartCategoryDefaultArgsObjectSchema)]).optional(), parts: z.union([z.boolean(),
    z.lazy(() => PartInputSchema.findMany)]).optional(), catalogItems: z.union([z.boolean(),
    z.lazy(() => CatalogItemInputSchema.findMany)]).optional(), catalogItem: z.union([z.boolean(),
    z.lazy(() => CatalogItemDefaultArgsObjectSchema)]).optional(), _count: z.union([z.boolean(),
    z.lazy(() => MediaAssetCountOutputTypeDefaultArgsObjectSchema)]).optional()
}).strict() as SchemaType;
