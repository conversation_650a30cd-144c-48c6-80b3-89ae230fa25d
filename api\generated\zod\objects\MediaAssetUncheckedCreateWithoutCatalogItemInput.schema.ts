/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartUncheckedCreateNestedOneWithoutImageInputObjectSchema } from './PartUncheckedCreateNestedOneWithoutImageInput.schema';
import { PartCategoryUncheckedCreateNestedOneWithoutImageInputObjectSchema } from './PartCategoryUncheckedCreateNestedOneWithoutImageInput.schema';
import { PartUncheckedCreateNestedManyWithoutMediaAssetsInputObjectSchema } from './PartUncheckedCreateNestedManyWithoutMediaAssetsInput.schema';
import { CatalogItemUncheckedCreateNestedManyWithoutMediaAssetsInputObjectSchema } from './CatalogItemUncheckedCreateNestedManyWithoutMediaAssetsInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.MediaAssetUncheckedCreateWithoutCatalogItemInput>;
export const MediaAssetUncheckedCreateWithoutCatalogItemInputObjectSchema: SchemaType = z.object({
    id: z.number().optional().optional(), fileName: z.string(), mimeType: z.string(), fileSize: z.union([z.number(),
    z.null()]).optional().nullable(), url: z.string(), createdAt: z.union([z.date().optional(), z.string().datetime().optional()]).optional(), updatedAt: z.union([z.date().optional(), z.string().datetime().optional()]).optional(), part: z.lazy(() => PartUncheckedCreateNestedOneWithoutImageInputObjectSchema).optional().optional(), partCategory: z.lazy(() => PartCategoryUncheckedCreateNestedOneWithoutImageInputObjectSchema).optional().optional(), parts: z.lazy(() => PartUncheckedCreateNestedManyWithoutMediaAssetsInputObjectSchema).optional().optional(), catalogItems: z.lazy(() => CatalogItemUncheckedCreateNestedManyWithoutMediaAssetsInputObjectSchema).optional().optional()
}).strict() as SchemaType;
