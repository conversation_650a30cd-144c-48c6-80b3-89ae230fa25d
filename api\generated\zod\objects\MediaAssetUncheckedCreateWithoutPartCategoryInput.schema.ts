/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartUncheckedCreateNestedOneWithoutImageInputObjectSchema } from './PartUncheckedCreateNestedOneWithoutImageInput.schema';
import { PartUncheckedCreateNestedManyWithoutMediaAssetsInputObjectSchema } from './PartUncheckedCreateNestedManyWithoutMediaAssetsInput.schema';
import { CatalogItemUncheckedCreateNestedManyWithoutMediaAssetsInputObjectSchema } from './CatalogItemUncheckedCreateNestedManyWithoutMediaAssetsInput.schema';
import { CatalogItemUncheckedCreateNestedOneWithoutImageInputObjectSchema } from './CatalogItemUncheckedCreateNestedOneWithoutImageInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.MediaAssetUncheckedCreateWithoutPartCategoryInput>;
export const MediaAssetUncheckedCreateWithoutPartCategoryInputObjectSchema: SchemaType = z.object({
    id: z.number().optional().optional(), fileName: z.string(), mimeType: z.string(), fileSize: z.union([z.number(),
    z.null()]).optional().nullable(), url: z.string(), createdAt: z.union([z.date().optional(), z.string().datetime().optional()]).optional(), updatedAt: z.union([z.date().optional(), z.string().datetime().optional()]).optional(), part: z.lazy(() => PartUncheckedCreateNestedOneWithoutImageInputObjectSchema).optional().optional(), parts: z.lazy(() => PartUncheckedCreateNestedManyWithoutMediaAssetsInputObjectSchema).optional().optional(), catalogItems: z.lazy(() => CatalogItemUncheckedCreateNestedManyWithoutMediaAssetsInputObjectSchema).optional().optional(), catalogItem: z.lazy(() => CatalogItemUncheckedCreateNestedOneWithoutImageInputObjectSchema).optional().optional()
}).strict() as SchemaType;
