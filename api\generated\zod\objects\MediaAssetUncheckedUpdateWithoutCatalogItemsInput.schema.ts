/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { IntFieldUpdateOperationsInputObjectSchema } from './IntFieldUpdateOperationsInput.schema';
import { StringFieldUpdateOperationsInputObjectSchema } from './StringFieldUpdateOperationsInput.schema';
import { NullableIntFieldUpdateOperationsInputObjectSchema } from './NullableIntFieldUpdateOperationsInput.schema';
import { DateTimeFieldUpdateOperationsInputObjectSchema } from './DateTimeFieldUpdateOperationsInput.schema';
import { PartUncheckedUpdateOneWithoutImageNestedInputObjectSchema } from './PartUncheckedUpdateOneWithoutImageNestedInput.schema';
import { PartCategoryUncheckedUpdateOneWithoutImageNestedInputObjectSchema } from './PartCategoryUncheckedUpdateOneWithoutImageNestedInput.schema';
import { PartUncheckedUpdateManyWithoutMediaAssetsNestedInputObjectSchema } from './PartUncheckedUpdateManyWithoutMediaAssetsNestedInput.schema';
import { CatalogItemUncheckedUpdateOneWithoutImageNestedInputObjectSchema } from './CatalogItemUncheckedUpdateOneWithoutImageNestedInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.MediaAssetUncheckedUpdateWithoutCatalogItemsInput>;
export const MediaAssetUncheckedUpdateWithoutCatalogItemsInputObjectSchema: SchemaType = z.object({
    id: z.union([z.number(),
    z.lazy(() => IntFieldUpdateOperationsInputObjectSchema)]).optional(), fileName: z.union([z.string(),
    z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(), mimeType: z.union([z.string(),
    z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(), fileSize: z.union([z.number(),
    z.lazy(() => NullableIntFieldUpdateOperationsInputObjectSchema),
    z.null()]).optional().nullable(), url: z.union([z.string(),
    z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(), createdAt: z.union([z.union([z.date(), z.string().datetime().optional()]),
    z.lazy(() => DateTimeFieldUpdateOperationsInputObjectSchema)]).optional(), updatedAt: z.union([z.union([z.date(), z.string().datetime().optional()]),
    z.lazy(() => DateTimeFieldUpdateOperationsInputObjectSchema)]).optional(), part: z.lazy(() => PartUncheckedUpdateOneWithoutImageNestedInputObjectSchema).optional().optional(), partCategory: z.lazy(() => PartCategoryUncheckedUpdateOneWithoutImageNestedInputObjectSchema).optional().optional(), parts: z.lazy(() => PartUncheckedUpdateManyWithoutMediaAssetsNestedInputObjectSchema).optional().optional(), catalogItem: z.lazy(() => CatalogItemUncheckedUpdateOneWithoutImageNestedInputObjectSchema).optional().optional()
}).strict() as SchemaType;
