/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { MediaAssetScalarWhereInputObjectSchema } from './MediaAssetScalarWhereInput.schema';
import { MediaAssetUpdateManyMutationInputObjectSchema } from './MediaAssetUpdateManyMutationInput.schema';
import { MediaAssetUncheckedUpdateManyWithoutPartsInputObjectSchema } from './MediaAssetUncheckedUpdateManyWithoutPartsInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.MediaAssetUpdateManyWithWhereWithoutPartsInput>;
export const MediaAssetUpdateManyWithWhereWithoutPartsInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => MediaAssetScalarWhereInputObjectSchema), data: z.union([z.lazy(() => MediaAssetUpdateManyMutationInputObjectSchema), z.lazy(() => MediaAssetUncheckedUpdateManyWithoutPartsInputObjectSchema)])
}).strict() as SchemaType;
