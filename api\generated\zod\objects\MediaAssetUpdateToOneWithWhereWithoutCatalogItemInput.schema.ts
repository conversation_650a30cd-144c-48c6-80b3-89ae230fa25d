/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { MediaAssetWhereInputObjectSchema } from './MediaAssetWhereInput.schema';
import { MediaAssetUpdateWithoutCatalogItemInputObjectSchema } from './MediaAssetUpdateWithoutCatalogItemInput.schema';
import { MediaAssetUncheckedUpdateWithoutCatalogItemInputObjectSchema } from './MediaAssetUncheckedUpdateWithoutCatalogItemInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.MediaAssetUpdateToOneWithWhereWithoutCatalogItemInput>;
export const MediaAssetUpdateToOneWithWhereWithoutCatalogItemInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => MediaAssetWhereInputObjectSchema).optional().optional(), data: z.union([z.lazy(() => MediaAssetUpdateWithoutCatalogItemInputObjectSchema), z.lazy(() => MediaAssetUncheckedUpdateWithoutCatalogItemInputObjectSchema)])
}).strict() as SchemaType;
