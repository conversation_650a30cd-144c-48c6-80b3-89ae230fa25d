/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { MediaAssetWhereInputObjectSchema } from './MediaAssetWhereInput.schema';
import { MediaAssetUpdateWithoutPartCategoryInputObjectSchema } from './MediaAssetUpdateWithoutPartCategoryInput.schema';
import { MediaAssetUncheckedUpdateWithoutPartCategoryInputObjectSchema } from './MediaAssetUncheckedUpdateWithoutPartCategoryInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.MediaAssetUpdateToOneWithWhereWithoutPartCategoryInput>;
export const MediaAssetUpdateToOneWithWhereWithoutPartCategoryInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => MediaAssetWhereInputObjectSchema).optional().optional(), data: z.union([z.lazy(() => MediaAssetUpdateWithoutPartCategoryInputObjectSchema), z.lazy(() => MediaAssetUncheckedUpdateWithoutPartCategoryInputObjectSchema)])
}).strict() as SchemaType;
