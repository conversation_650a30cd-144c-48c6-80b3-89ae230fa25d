/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { MediaAssetWhereInputObjectSchema } from './MediaAssetWhereInput.schema';
import { MediaAssetUpdateWithoutPartInputObjectSchema } from './MediaAssetUpdateWithoutPartInput.schema';
import { MediaAssetUncheckedUpdateWithoutPartInputObjectSchema } from './MediaAssetUncheckedUpdateWithoutPartInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.MediaAssetUpdateToOneWithWhereWithoutPartInput>;
export const MediaAssetUpdateToOneWithWhereWithoutPartInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => MediaAssetWhereInputObjectSchema).optional().optional(), data: z.union([z.lazy(() => MediaAssetUpdateWithoutPartInputObjectSchema), z.lazy(() => MediaAssetUncheckedUpdateWithoutPartInputObjectSchema)])
}).strict() as SchemaType;
