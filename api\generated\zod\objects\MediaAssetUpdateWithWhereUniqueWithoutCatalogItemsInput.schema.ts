/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { MediaAssetWhereUniqueInputObjectSchema } from './MediaAssetWhereUniqueInput.schema';
import { MediaAssetUpdateWithoutCatalogItemsInputObjectSchema } from './MediaAssetUpdateWithoutCatalogItemsInput.schema';
import { MediaAssetUncheckedUpdateWithoutCatalogItemsInputObjectSchema } from './MediaAssetUncheckedUpdateWithoutCatalogItemsInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.MediaAssetUpdateWithWhereUniqueWithoutCatalogItemsInput>;
export const MediaAssetUpdateWithWhereUniqueWithoutCatalogItemsInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => MediaAssetWhereUniqueInputObjectSchema), data: z.union([z.lazy(() => MediaAssetUpdateWithoutCatalogItemsInputObjectSchema), z.lazy(() => MediaAssetUncheckedUpdateWithoutCatalogItemsInputObjectSchema)])
}).strict() as SchemaType;
