/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { MediaAssetWhereUniqueInputObjectSchema } from './MediaAssetWhereUniqueInput.schema';
import { MediaAssetUpdateWithoutPartsInputObjectSchema } from './MediaAssetUpdateWithoutPartsInput.schema';
import { MediaAssetUncheckedUpdateWithoutPartsInputObjectSchema } from './MediaAssetUncheckedUpdateWithoutPartsInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.MediaAssetUpdateWithWhereUniqueWithoutPartsInput>;
export const MediaAssetUpdateWithWhereUniqueWithoutPartsInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => MediaAssetWhereUniqueInputObjectSchema), data: z.union([z.lazy(() => MediaAssetUpdateWithoutPartsInputObjectSchema), z.lazy(() => MediaAssetUncheckedUpdateWithoutPartsInputObjectSchema)])
}).strict() as SchemaType;
