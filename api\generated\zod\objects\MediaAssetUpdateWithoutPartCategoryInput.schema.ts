/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { StringFieldUpdateOperationsInputObjectSchema } from './StringFieldUpdateOperationsInput.schema';
import { NullableIntFieldUpdateOperationsInputObjectSchema } from './NullableIntFieldUpdateOperationsInput.schema';
import { DateTimeFieldUpdateOperationsInputObjectSchema } from './DateTimeFieldUpdateOperationsInput.schema';
import { PartUpdateOneWithoutImageNestedInputObjectSchema } from './PartUpdateOneWithoutImageNestedInput.schema';
import { PartUpdateManyWithoutMediaAssetsNestedInputObjectSchema } from './PartUpdateManyWithoutMediaAssetsNestedInput.schema';
import { CatalogItemUpdateManyWithoutMediaAssetsNestedInputObjectSchema } from './CatalogItemUpdateManyWithoutMediaAssetsNestedInput.schema';
import { CatalogItemUpdateOneWithoutImageNestedInputObjectSchema } from './CatalogItemUpdateOneWithoutImageNestedInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.MediaAssetUpdateWithoutPartCategoryInput>;
export const MediaAssetUpdateWithoutPartCategoryInputObjectSchema: SchemaType = z.object({
    fileName: z.union([z.string(),
    z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(), mimeType: z.union([z.string(),
    z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(), fileSize: z.union([z.number(),
    z.lazy(() => NullableIntFieldUpdateOperationsInputObjectSchema),
    z.null()]).optional().nullable(), url: z.union([z.string(),
    z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(), createdAt: z.union([z.union([z.date(), z.string().datetime().optional()]),
    z.lazy(() => DateTimeFieldUpdateOperationsInputObjectSchema)]).optional(), updatedAt: z.union([z.union([z.date(), z.string().datetime().optional()]),
    z.lazy(() => DateTimeFieldUpdateOperationsInputObjectSchema)]).optional(), part: z.lazy(() => PartUpdateOneWithoutImageNestedInputObjectSchema).optional().optional(), parts: z.lazy(() => PartUpdateManyWithoutMediaAssetsNestedInputObjectSchema).optional().optional(), catalogItems: z.lazy(() => CatalogItemUpdateManyWithoutMediaAssetsNestedInputObjectSchema).optional().optional(), catalogItem: z.lazy(() => CatalogItemUpdateOneWithoutImageNestedInputObjectSchema).optional().optional()
}).strict() as SchemaType;
