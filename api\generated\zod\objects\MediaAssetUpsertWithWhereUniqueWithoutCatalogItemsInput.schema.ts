/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { MediaAssetWhereUniqueInputObjectSchema } from './MediaAssetWhereUniqueInput.schema';
import { MediaAssetUpdateWithoutCatalogItemsInputObjectSchema } from './MediaAssetUpdateWithoutCatalogItemsInput.schema';
import { MediaAssetUncheckedUpdateWithoutCatalogItemsInputObjectSchema } from './MediaAssetUncheckedUpdateWithoutCatalogItemsInput.schema';
import { MediaAssetCreateWithoutCatalogItemsInputObjectSchema } from './MediaAssetCreateWithoutCatalogItemsInput.schema';
import { MediaAssetUncheckedCreateWithoutCatalogItemsInputObjectSchema } from './MediaAssetUncheckedCreateWithoutCatalogItemsInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.MediaAssetUpsertWithWhereUniqueWithoutCatalogItemsInput>;
export const MediaAssetUpsertWithWhereUniqueWithoutCatalogItemsInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => MediaAssetWhereUniqueInputObjectSchema), update: z.union([z.lazy(() => MediaAssetUpdateWithoutCatalogItemsInputObjectSchema), z.lazy(() => MediaAssetUncheckedUpdateWithoutCatalogItemsInputObjectSchema)]), create: z.union([z.lazy(() => MediaAssetCreateWithoutCatalogItemsInputObjectSchema), z.lazy(() => MediaAssetUncheckedCreateWithoutCatalogItemsInputObjectSchema)])
}).strict() as SchemaType;
