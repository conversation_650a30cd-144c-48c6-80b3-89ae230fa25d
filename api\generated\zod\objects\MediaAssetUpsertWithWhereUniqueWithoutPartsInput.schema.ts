/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { MediaAssetWhereUniqueInputObjectSchema } from './MediaAssetWhereUniqueInput.schema';
import { MediaAssetUpdateWithoutPartsInputObjectSchema } from './MediaAssetUpdateWithoutPartsInput.schema';
import { MediaAssetUncheckedUpdateWithoutPartsInputObjectSchema } from './MediaAssetUncheckedUpdateWithoutPartsInput.schema';
import { MediaAssetCreateWithoutPartsInputObjectSchema } from './MediaAssetCreateWithoutPartsInput.schema';
import { MediaAssetUncheckedCreateWithoutPartsInputObjectSchema } from './MediaAssetUncheckedCreateWithoutPartsInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.MediaAssetUpsertWithWhereUniqueWithoutPartsInput>;
export const MediaAssetUpsertWithWhereUniqueWithoutPartsInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => MediaAssetWhereUniqueInputObjectSchema), update: z.union([z.lazy(() => MediaAssetUpdateWithoutPartsInputObjectSchema), z.lazy(() => MediaAssetUncheckedUpdateWithoutPartsInputObjectSchema)]), create: z.union([z.lazy(() => MediaAssetCreateWithoutPartsInputObjectSchema), z.lazy(() => MediaAssetUncheckedCreateWithoutPartsInputObjectSchema)])
}).strict() as SchemaType;
