/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { MediaAssetUpdateWithoutCatalogItemInputObjectSchema } from './MediaAssetUpdateWithoutCatalogItemInput.schema';
import { MediaAssetUncheckedUpdateWithoutCatalogItemInputObjectSchema } from './MediaAssetUncheckedUpdateWithoutCatalogItemInput.schema';
import { MediaAssetCreateWithoutCatalogItemInputObjectSchema } from './MediaAssetCreateWithoutCatalogItemInput.schema';
import { MediaAssetUncheckedCreateWithoutCatalogItemInputObjectSchema } from './MediaAssetUncheckedCreateWithoutCatalogItemInput.schema';
import { MediaAssetWhereInputObjectSchema } from './MediaAssetWhereInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.MediaAssetUpsertWithoutCatalogItemInput>;
export const MediaAssetUpsertWithoutCatalogItemInputObjectSchema: SchemaType = z.object({
    update: z.union([z.lazy(() => MediaAssetUpdateWithoutCatalogItemInputObjectSchema), z.lazy(() => MediaAssetUncheckedUpdateWithoutCatalogItemInputObjectSchema)]), create: z.union([z.lazy(() => MediaAssetCreateWithoutCatalogItemInputObjectSchema), z.lazy(() => MediaAssetUncheckedCreateWithoutCatalogItemInputObjectSchema)]), where: z.lazy(() => MediaAssetWhereInputObjectSchema).optional().optional()
}).strict() as SchemaType;
