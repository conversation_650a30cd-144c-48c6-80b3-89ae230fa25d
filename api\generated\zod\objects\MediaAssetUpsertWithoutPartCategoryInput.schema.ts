/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { MediaAssetUpdateWithoutPartCategoryInputObjectSchema } from './MediaAssetUpdateWithoutPartCategoryInput.schema';
import { MediaAssetUncheckedUpdateWithoutPartCategoryInputObjectSchema } from './MediaAssetUncheckedUpdateWithoutPartCategoryInput.schema';
import { MediaAssetCreateWithoutPartCategoryInputObjectSchema } from './MediaAssetCreateWithoutPartCategoryInput.schema';
import { MediaAssetUncheckedCreateWithoutPartCategoryInputObjectSchema } from './MediaAssetUncheckedCreateWithoutPartCategoryInput.schema';
import { MediaAssetWhereInputObjectSchema } from './MediaAssetWhereInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.MediaAssetUpsertWithoutPartCategoryInput>;
export const MediaAssetUpsertWithoutPartCategoryInputObjectSchema: SchemaType = z.object({
    update: z.union([z.lazy(() => MediaAssetUpdateWithoutPartCategoryInputObjectSchema), z.lazy(() => MediaAssetUncheckedUpdateWithoutPartCategoryInputObjectSchema)]), create: z.union([z.lazy(() => MediaAssetCreateWithoutPartCategoryInputObjectSchema), z.lazy(() => MediaAssetUncheckedCreateWithoutPartCategoryInputObjectSchema)]), where: z.lazy(() => MediaAssetWhereInputObjectSchema).optional().optional()
}).strict() as SchemaType;
