/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { MediaAssetUpdateWithoutPartInputObjectSchema } from './MediaAssetUpdateWithoutPartInput.schema';
import { MediaAssetUncheckedUpdateWithoutPartInputObjectSchema } from './MediaAssetUncheckedUpdateWithoutPartInput.schema';
import { MediaAssetCreateWithoutPartInputObjectSchema } from './MediaAssetCreateWithoutPartInput.schema';
import { MediaAssetUncheckedCreateWithoutPartInputObjectSchema } from './MediaAssetUncheckedCreateWithoutPartInput.schema';
import { MediaAssetWhereInputObjectSchema } from './MediaAssetWhereInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.MediaAssetUpsertWithoutPartInput>;
export const MediaAssetUpsertWithoutPartInputObjectSchema: SchemaType = z.object({
    update: z.union([z.lazy(() => MediaAssetUpdateWithoutPartInputObjectSchema), z.lazy(() => MediaAssetUncheckedUpdateWithoutPartInputObjectSchema)]), create: z.union([z.lazy(() => MediaAssetCreateWithoutPartInputObjectSchema), z.lazy(() => MediaAssetUncheckedCreateWithoutPartInputObjectSchema)]), where: z.lazy(() => MediaAssetWhereInputObjectSchema).optional().optional()
}).strict() as SchemaType;
