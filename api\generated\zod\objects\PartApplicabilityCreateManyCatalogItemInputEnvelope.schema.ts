/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartApplicabilityCreateManyCatalogItemInputObjectSchema } from './PartApplicabilityCreateManyCatalogItemInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartApplicabilityCreateManyCatalogItemInputEnvelope>;
export const PartApplicabilityCreateManyCatalogItemInputEnvelopeObjectSchema: SchemaType = z.object({
    data: z.union([z.lazy(() => PartApplicabilityCreateManyCatalogItemInputObjectSchema),
    z.lazy(() => PartApplicabilityCreateManyCatalogItemInputObjectSchema).array()]), skipDuplicates: z.boolean().optional().optional()
}).strict() as SchemaType;
