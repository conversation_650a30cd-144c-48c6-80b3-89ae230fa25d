/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartApplicabilityCreateManyPartInputObjectSchema } from './PartApplicabilityCreateManyPartInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartApplicabilityCreateManyPartInputEnvelope>;
export const PartApplicabilityCreateManyPartInputEnvelopeObjectSchema: SchemaType = z.object({
    data: z.union([z.lazy(() => PartApplicabilityCreateManyPartInputObjectSchema),
    z.lazy(() => PartApplicabilityCreateManyPartInputObjectSchema).array()]), skipDuplicates: z.boolean().optional().optional()
}).strict() as SchemaType;
