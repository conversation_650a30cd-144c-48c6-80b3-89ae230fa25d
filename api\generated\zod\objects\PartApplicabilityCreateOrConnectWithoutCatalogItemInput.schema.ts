/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartApplicabilityWhereUniqueInputObjectSchema } from './PartApplicabilityWhereUniqueInput.schema';
import { PartApplicabilityCreateWithoutCatalogItemInputObjectSchema } from './PartApplicabilityCreateWithoutCatalogItemInput.schema';
import { PartApplicabilityUncheckedCreateWithoutCatalogItemInputObjectSchema } from './PartApplicabilityUncheckedCreateWithoutCatalogItemInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartApplicabilityCreateOrConnectWithoutCatalogItemInput>;
export const PartApplicabilityCreateOrConnectWithoutCatalogItemInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartApplicabilityWhereUniqueInputObjectSchema), create: z.union([z.lazy(() => PartApplicabilityCreateWithoutCatalogItemInputObjectSchema), z.lazy(() => PartApplicabilityUncheckedCreateWithoutCatalogItemInputObjectSchema)])
}).strict() as SchemaType;
