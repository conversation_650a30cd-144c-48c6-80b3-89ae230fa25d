/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartApplicabilityWhereUniqueInputObjectSchema } from './PartApplicabilityWhereUniqueInput.schema';
import { PartApplicabilityCreateWithoutPartInputObjectSchema } from './PartApplicabilityCreateWithoutPartInput.schema';
import { PartApplicabilityUncheckedCreateWithoutPartInputObjectSchema } from './PartApplicabilityUncheckedCreateWithoutPartInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartApplicabilityCreateOrConnectWithoutPartInput>;
export const PartApplicabilityCreateOrConnectWithoutPartInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartApplicabilityWhereUniqueInputObjectSchema), create: z.union([z.lazy(() => PartApplicabilityCreateWithoutPartInputObjectSchema), z.lazy(() => PartApplicabilityUncheckedCreateWithoutPartInputObjectSchema)])
}).strict() as SchemaType;
