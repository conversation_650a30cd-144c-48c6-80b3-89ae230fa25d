/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { ApplicabilityAccuracySchema } from '../enums/ApplicabilityAccuracy.schema';
import { PartCreateNestedOneWithoutApplicabilitiesInputObjectSchema } from './PartCreateNestedOneWithoutApplicabilitiesInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartApplicabilityCreateWithoutCatalogItemInput>;
export const PartApplicabilityCreateWithoutCatalogItemInputObjectSchema: SchemaType = z.object({
    accuracy: z.lazy(() => ApplicabilityAccuracySchema).optional().optional(), notes: z.union([z.string(),
    z.null()]).optional().nullable(), part: z.lazy(() => PartCreateNestedOneWithoutApplicabilitiesInputObjectSchema)
}).strict() as SchemaType;
