/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { ApplicabilityAccuracySchema } from '../enums/ApplicabilityAccuracy.schema';
import { CatalogItemCreateNestedOneWithoutApplicabilitiesInputObjectSchema } from './CatalogItemCreateNestedOneWithoutApplicabilitiesInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartApplicabilityCreateWithoutPartInput>;
export const PartApplicabilityCreateWithoutPartInputObjectSchema: SchemaType = z.object({
    accuracy: z.lazy(() => ApplicabilityAccuracySchema).optional().optional(), notes: z.union([z.string(),
    z.null()]).optional().nullable(), catalogItem: z.lazy(() => CatalogItemCreateNestedOneWithoutApplicabilitiesInputObjectSchema)
}).strict() as SchemaType;
