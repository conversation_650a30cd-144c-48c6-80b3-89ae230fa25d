/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartApplicabilitySelectObjectSchema } from './PartApplicabilitySelect.schema';
import { PartApplicabilityIncludeObjectSchema } from './PartApplicabilityInclude.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartApplicabilityDefaultArgs>;
export const PartApplicabilityDefaultArgsObjectSchema: SchemaType = z.object({
    select: z.lazy(() => PartApplicabilitySelectObjectSchema).optional().optional(), include: z.lazy(() => PartApplicabilityIncludeObjectSchema).optional().optional()
}).strict() as SchemaType;
