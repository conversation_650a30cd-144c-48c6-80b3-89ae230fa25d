/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartDefaultArgsObjectSchema } from './PartDefaultArgs.schema';
import { CatalogItemDefaultArgsObjectSchema } from './CatalogItemDefaultArgs.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartApplicabilityInclude>;
export const PartApplicabilityIncludeObjectSchema: SchemaType = z.object({
    part: z.union([z.boolean(),
    z.lazy(() => PartDefaultArgsObjectSchema)]).optional(), catalogItem: z.union([z.boolean(),
    z.lazy(() => CatalogItemDefaultArgsObjectSchema)]).optional()
}).strict() as SchemaType;
