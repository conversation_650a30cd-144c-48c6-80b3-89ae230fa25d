/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { IntFilterObjectSchema } from './IntFilter.schema';
import { EnumApplicabilityAccuracyFilterObjectSchema } from './EnumApplicabilityAccuracyFilter.schema';
import { ApplicabilityAccuracySchema } from '../enums/ApplicabilityAccuracy.schema';
import { StringNullableFilterObjectSchema } from './StringNullableFilter.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartApplicabilityScalarWhereInput>;
export const PartApplicabilityScalarWhereInputObjectSchema: SchemaType = z.object({
    AND: z.union([z.lazy(() => PartApplicabilityScalarWhereInputObjectSchema),
    z.lazy(() => PartApplicabilityScalarWhereInputObjectSchema).array()]).optional(), OR: z.lazy(() => PartApplicabilityScalarWhereInputObjectSchema).array().optional().optional(), NOT: z.union([z.lazy(() => PartApplicabilityScalarWhereInputObjectSchema),
    z.lazy(() => PartApplicabilityScalarWhereInputObjectSchema).array()]).optional(), id: z.union([z.lazy(() => IntFilterObjectSchema),
    z.number()]).optional(), partId: z.union([z.lazy(() => IntFilterObjectSchema),
    z.number()]).optional(), catalogItemId: z.union([z.lazy(() => IntFilterObjectSchema),
    z.number()]).optional(), accuracy: z.union([z.lazy(() => EnumApplicabilityAccuracyFilterObjectSchema),
    z.lazy(() => ApplicabilityAccuracySchema)]).optional(), notes: z.union([z.lazy(() => StringNullableFilterObjectSchema),
    z.string(),
    z.null()]).optional().nullable()
}).strict() as SchemaType;
