/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartDefaultArgsObjectSchema } from './PartDefaultArgs.schema';
import { CatalogItemDefaultArgsObjectSchema } from './CatalogItemDefaultArgs.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartApplicabilitySelect>;
export const PartApplicabilitySelectObjectSchema: SchemaType = z.object({
    id: z.boolean().optional().optional(), part: z.union([z.boolean(),
    z.lazy(() => PartDefaultArgsObjectSchema)]).optional(), partId: z.boolean().optional().optional(), catalogItem: z.union([z.boolean(),
    z.lazy(() => CatalogItemDefaultArgsObjectSchema)]).optional(), catalogItemId: z.boolean().optional().optional(), accuracy: z.boolean().optional().optional(), notes: z.boolean().optional().optional()
}).strict() as SchemaType;
