/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { ApplicabilityAccuracySchema } from '../enums/ApplicabilityAccuracy.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartApplicabilityUncheckedCreateWithoutPartInput>;
export const PartApplicabilityUncheckedCreateWithoutPartInputObjectSchema: SchemaType = z.object({
    id: z.number().optional().optional(), catalogItemId: z.number(), accuracy: z.lazy(() => ApplicabilityAccuracySchema).optional().optional(), notes: z.union([z.string(),
    z.null()]).optional().nullable()
}).strict() as SchemaType;
