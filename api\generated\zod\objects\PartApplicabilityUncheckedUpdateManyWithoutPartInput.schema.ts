/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { IntFieldUpdateOperationsInputObjectSchema } from './IntFieldUpdateOperationsInput.schema';
import { ApplicabilityAccuracySchema } from '../enums/ApplicabilityAccuracy.schema';
import { EnumApplicabilityAccuracyFieldUpdateOperationsInputObjectSchema } from './EnumApplicabilityAccuracyFieldUpdateOperationsInput.schema';
import { NullableStringFieldUpdateOperationsInputObjectSchema } from './NullableStringFieldUpdateOperationsInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartApplicabilityUncheckedUpdateManyWithoutPartInput>;
export const PartApplicabilityUncheckedUpdateManyWithoutPartInputObjectSchema: SchemaType = z.object({
    id: z.union([z.number(),
    z.lazy(() => IntFieldUpdateOperationsInputObjectSchema)]).optional(), catalogItemId: z.union([z.number(),
    z.lazy(() => IntFieldUpdateOperationsInputObjectSchema)]).optional(), accuracy: z.union([z.lazy(() => ApplicabilityAccuracySchema),
    z.lazy(() => EnumApplicabilityAccuracyFieldUpdateOperationsInputObjectSchema)]).optional(), notes: z.union([z.string(),
    z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
    z.null()]).optional().nullable()
}).strict() as SchemaType;
