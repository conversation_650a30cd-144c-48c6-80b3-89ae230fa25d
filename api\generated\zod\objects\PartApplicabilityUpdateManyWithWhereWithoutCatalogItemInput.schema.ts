/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartApplicabilityScalarWhereInputObjectSchema } from './PartApplicabilityScalarWhereInput.schema';
import { PartApplicabilityUpdateManyMutationInputObjectSchema } from './PartApplicabilityUpdateManyMutationInput.schema';
import { PartApplicabilityUncheckedUpdateManyWithoutCatalogItemInputObjectSchema } from './PartApplicabilityUncheckedUpdateManyWithoutCatalogItemInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartApplicabilityUpdateManyWithWhereWithoutCatalogItemInput>;
export const PartApplicabilityUpdateManyWithWhereWithoutCatalogItemInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartApplicabilityScalarWhereInputObjectSchema), data: z.union([z.lazy(() => PartApplicabilityUpdateManyMutationInputObjectSchema), z.lazy(() => PartApplicabilityUncheckedUpdateManyWithoutCatalogItemInputObjectSchema)])
}).strict() as SchemaType;
