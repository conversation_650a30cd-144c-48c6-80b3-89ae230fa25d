/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartApplicabilityScalarWhereInputObjectSchema } from './PartApplicabilityScalarWhereInput.schema';
import { PartApplicabilityUpdateManyMutationInputObjectSchema } from './PartApplicabilityUpdateManyMutationInput.schema';
import { PartApplicabilityUncheckedUpdateManyWithoutPartInputObjectSchema } from './PartApplicabilityUncheckedUpdateManyWithoutPartInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartApplicabilityUpdateManyWithWhereWithoutPartInput>;
export const PartApplicabilityUpdateManyWithWhereWithoutPartInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartApplicabilityScalarWhereInputObjectSchema), data: z.union([z.lazy(() => PartApplicabilityUpdateManyMutationInputObjectSchema), z.lazy(() => PartApplicabilityUncheckedUpdateManyWithoutPartInputObjectSchema)])
}).strict() as SchemaType;
