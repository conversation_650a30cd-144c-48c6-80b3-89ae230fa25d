/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartApplicabilityWhereUniqueInputObjectSchema } from './PartApplicabilityWhereUniqueInput.schema';
import { PartApplicabilityUpdateWithoutCatalogItemInputObjectSchema } from './PartApplicabilityUpdateWithoutCatalogItemInput.schema';
import { PartApplicabilityUncheckedUpdateWithoutCatalogItemInputObjectSchema } from './PartApplicabilityUncheckedUpdateWithoutCatalogItemInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartApplicabilityUpdateWithWhereUniqueWithoutCatalogItemInput>;
export const PartApplicabilityUpdateWithWhereUniqueWithoutCatalogItemInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartApplicabilityWhereUniqueInputObjectSchema), data: z.union([z.lazy(() => PartApplicabilityUpdateWithoutCatalogItemInputObjectSchema), z.lazy(() => PartApplicabilityUncheckedUpdateWithoutCatalogItemInputObjectSchema)])
}).strict() as SchemaType;
