/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartApplicabilityWhereUniqueInputObjectSchema } from './PartApplicabilityWhereUniqueInput.schema';
import { PartApplicabilityUpdateWithoutPartInputObjectSchema } from './PartApplicabilityUpdateWithoutPartInput.schema';
import { PartApplicabilityUncheckedUpdateWithoutPartInputObjectSchema } from './PartApplicabilityUncheckedUpdateWithoutPartInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartApplicabilityUpdateWithWhereUniqueWithoutPartInput>;
export const PartApplicabilityUpdateWithWhereUniqueWithoutPartInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartApplicabilityWhereUniqueInputObjectSchema), data: z.union([z.lazy(() => PartApplicabilityUpdateWithoutPartInputObjectSchema), z.lazy(() => PartApplicabilityUncheckedUpdateWithoutPartInputObjectSchema)])
}).strict() as SchemaType;
