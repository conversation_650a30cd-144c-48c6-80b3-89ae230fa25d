/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { ApplicabilityAccuracySchema } from '../enums/ApplicabilityAccuracy.schema';
import { EnumApplicabilityAccuracyFieldUpdateOperationsInputObjectSchema } from './EnumApplicabilityAccuracyFieldUpdateOperationsInput.schema';
import { NullableStringFieldUpdateOperationsInputObjectSchema } from './NullableStringFieldUpdateOperationsInput.schema';
import { PartUpdateOneRequiredWithoutApplicabilitiesNestedInputObjectSchema } from './PartUpdateOneRequiredWithoutApplicabilitiesNestedInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartApplicabilityUpdateWithoutCatalogItemInput>;
export const PartApplicabilityUpdateWithoutCatalogItemInputObjectSchema: SchemaType = z.object({
    accuracy: z.union([z.lazy(() => ApplicabilityAccuracySchema),
    z.lazy(() => EnumApplicabilityAccuracyFieldUpdateOperationsInputObjectSchema)]).optional(), notes: z.union([z.string(),
    z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
    z.null()]).optional().nullable(), part: z.lazy(() => PartUpdateOneRequiredWithoutApplicabilitiesNestedInputObjectSchema).optional().optional()
}).strict() as SchemaType;
