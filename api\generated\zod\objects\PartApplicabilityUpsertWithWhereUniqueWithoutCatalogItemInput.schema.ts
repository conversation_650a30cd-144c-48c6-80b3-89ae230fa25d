/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartApplicabilityWhereUniqueInputObjectSchema } from './PartApplicabilityWhereUniqueInput.schema';
import { PartApplicabilityUpdateWithoutCatalogItemInputObjectSchema } from './PartApplicabilityUpdateWithoutCatalogItemInput.schema';
import { PartApplicabilityUncheckedUpdateWithoutCatalogItemInputObjectSchema } from './PartApplicabilityUncheckedUpdateWithoutCatalogItemInput.schema';
import { PartApplicabilityCreateWithoutCatalogItemInputObjectSchema } from './PartApplicabilityCreateWithoutCatalogItemInput.schema';
import { PartApplicabilityUncheckedCreateWithoutCatalogItemInputObjectSchema } from './PartApplicabilityUncheckedCreateWithoutCatalogItemInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartApplicabilityUpsertWithWhereUniqueWithoutCatalogItemInput>;
export const PartApplicabilityUpsertWithWhereUniqueWithoutCatalogItemInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartApplicabilityWhereUniqueInputObjectSchema), update: z.union([z.lazy(() => PartApplicabilityUpdateWithoutCatalogItemInputObjectSchema), z.lazy(() => PartApplicabilityUncheckedUpdateWithoutCatalogItemInputObjectSchema)]), create: z.union([z.lazy(() => PartApplicabilityCreateWithoutCatalogItemInputObjectSchema), z.lazy(() => PartApplicabilityUncheckedCreateWithoutCatalogItemInputObjectSchema)])
}).strict() as SchemaType;
