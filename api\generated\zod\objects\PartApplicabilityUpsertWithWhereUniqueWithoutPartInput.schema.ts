/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartApplicabilityWhereUniqueInputObjectSchema } from './PartApplicabilityWhereUniqueInput.schema';
import { PartApplicabilityUpdateWithoutPartInputObjectSchema } from './PartApplicabilityUpdateWithoutPartInput.schema';
import { PartApplicabilityUncheckedUpdateWithoutPartInputObjectSchema } from './PartApplicabilityUncheckedUpdateWithoutPartInput.schema';
import { PartApplicabilityCreateWithoutPartInputObjectSchema } from './PartApplicabilityCreateWithoutPartInput.schema';
import { PartApplicabilityUncheckedCreateWithoutPartInputObjectSchema } from './PartApplicabilityUncheckedCreateWithoutPartInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartApplicabilityUpsertWithWhereUniqueWithoutPartInput>;
export const PartApplicabilityUpsertWithWhereUniqueWithoutPartInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartApplicabilityWhereUniqueInputObjectSchema), update: z.union([z.lazy(() => PartApplicabilityUpdateWithoutPartInputObjectSchema), z.lazy(() => PartApplicabilityUncheckedUpdateWithoutPartInputObjectSchema)]), create: z.union([z.lazy(() => PartApplicabilityCreateWithoutPartInputObjectSchema), z.lazy(() => PartApplicabilityUncheckedCreateWithoutPartInputObjectSchema)])
}).strict() as SchemaType;
