/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartAttributeCountAggregateInputType>;
export const PartAttributeCountAggregateInputObjectSchema: SchemaType = z.object({
    id: z.literal(true).optional().optional(), value: z.literal(true).optional().optional(), numericValue: z.literal(true).optional().optional(), partId: z.literal(true).optional().optional(), templateId: z.literal(true).optional().optional(), _all: z.literal(true).optional().optional()
}).strict() as SchemaType;
