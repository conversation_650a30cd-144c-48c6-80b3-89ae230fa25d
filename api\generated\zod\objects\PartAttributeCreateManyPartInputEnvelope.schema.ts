/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartAttributeCreateManyPartInputObjectSchema } from './PartAttributeCreateManyPartInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartAttributeCreateManyPartInputEnvelope>;
export const PartAttributeCreateManyPartInputEnvelopeObjectSchema: SchemaType = z.object({
    data: z.union([z.lazy(() => PartAttributeCreateManyPartInputObjectSchema),
    z.lazy(() => PartAttributeCreateManyPartInputObjectSchema).array()]), skipDuplicates: z.boolean().optional().optional()
}).strict() as SchemaType;
