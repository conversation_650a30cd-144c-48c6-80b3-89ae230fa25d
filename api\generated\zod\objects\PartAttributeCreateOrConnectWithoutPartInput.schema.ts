/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartAttributeWhereUniqueInputObjectSchema } from './PartAttributeWhereUniqueInput.schema';
import { PartAttributeCreateWithoutPartInputObjectSchema } from './PartAttributeCreateWithoutPartInput.schema';
import { PartAttributeUncheckedCreateWithoutPartInputObjectSchema } from './PartAttributeUncheckedCreateWithoutPartInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartAttributeCreateOrConnectWithoutPartInput>;
export const PartAttributeCreateOrConnectWithoutPartInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartAttributeWhereUniqueInputObjectSchema), create: z.union([z.lazy(() => PartAttributeCreateWithoutPartInputObjectSchema), z.lazy(() => PartAttributeUncheckedCreateWithoutPartInputObjectSchema)])
}).strict() as SchemaType;
