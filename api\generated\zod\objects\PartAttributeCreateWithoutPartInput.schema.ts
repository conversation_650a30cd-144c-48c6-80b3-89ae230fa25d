/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { AttributeTemplateCreateNestedOneWithoutPartAttributesInputObjectSchema } from './AttributeTemplateCreateNestedOneWithoutPartAttributesInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartAttributeCreateWithoutPartInput>;
export const PartAttributeCreateWithoutPartInputObjectSchema: SchemaType = z.object({
    value: z.string(), numericValue: z.union([z.number(),
    z.null()]).optional().nullable(), template: z.lazy(() => AttributeTemplateCreateNestedOneWithoutPartAttributesInputObjectSchema)
}).strict() as SchemaType;
