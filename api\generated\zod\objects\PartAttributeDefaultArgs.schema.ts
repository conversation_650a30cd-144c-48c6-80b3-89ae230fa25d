/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartAttributeSelectObjectSchema } from './PartAttributeSelect.schema';
import { PartAttributeIncludeObjectSchema } from './PartAttributeInclude.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartAttributeDefaultArgs>;
export const PartAttributeDefaultArgsObjectSchema: SchemaType = z.object({
    select: z.lazy(() => PartAttributeSelectObjectSchema).optional().optional(), include: z.lazy(() => PartAttributeIncludeObjectSchema).optional().optional()
}).strict() as SchemaType;
