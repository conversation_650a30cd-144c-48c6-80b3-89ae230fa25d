/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartDefaultArgsObjectSchema } from './PartDefaultArgs.schema';
import { AttributeTemplateDefaultArgsObjectSchema } from './AttributeTemplateDefaultArgs.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartAttributeInclude>;
export const PartAttributeIncludeObjectSchema: SchemaType = z.object({
    part: z.union([z.boolean(),
    z.lazy(() => PartDefaultArgsObjectSchema)]).optional(), template: z.union([z.boolean(),
    z.lazy(() => AttributeTemplateDefaultArgsObjectSchema)]).optional()
}).strict() as SchemaType;
