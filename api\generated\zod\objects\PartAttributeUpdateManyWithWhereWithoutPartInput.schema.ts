/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartAttributeScalarWhereInputObjectSchema } from './PartAttributeScalarWhereInput.schema';
import { PartAttributeUpdateManyMutationInputObjectSchema } from './PartAttributeUpdateManyMutationInput.schema';
import { PartAttributeUncheckedUpdateManyWithoutPartInputObjectSchema } from './PartAttributeUncheckedUpdateManyWithoutPartInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartAttributeUpdateManyWithWhereWithoutPartInput>;
export const PartAttributeUpdateManyWithWhereWithoutPartInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartAttributeScalarWhereInputObjectSchema), data: z.union([z.lazy(() => PartAttributeUpdateManyMutationInputObjectSchema), z.lazy(() => PartAttributeUncheckedUpdateManyWithoutPartInputObjectSchema)])
}).strict() as SchemaType;
