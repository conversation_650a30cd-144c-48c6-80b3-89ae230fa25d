/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartAttributeWhereUniqueInputObjectSchema } from './PartAttributeWhereUniqueInput.schema';
import { PartAttributeUpdateWithoutPartInputObjectSchema } from './PartAttributeUpdateWithoutPartInput.schema';
import { PartAttributeUncheckedUpdateWithoutPartInputObjectSchema } from './PartAttributeUncheckedUpdateWithoutPartInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartAttributeUpdateWithWhereUniqueWithoutPartInput>;
export const PartAttributeUpdateWithWhereUniqueWithoutPartInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartAttributeWhereUniqueInputObjectSchema), data: z.union([z.lazy(() => PartAttributeUpdateWithoutPartInputObjectSchema), z.lazy(() => PartAttributeUncheckedUpdateWithoutPartInputObjectSchema)])
}).strict() as SchemaType;
