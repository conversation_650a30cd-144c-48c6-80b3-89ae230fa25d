/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartAttributeWhereUniqueInputObjectSchema } from './PartAttributeWhereUniqueInput.schema';
import { PartAttributeUpdateWithoutPartInputObjectSchema } from './PartAttributeUpdateWithoutPartInput.schema';
import { PartAttributeUncheckedUpdateWithoutPartInputObjectSchema } from './PartAttributeUncheckedUpdateWithoutPartInput.schema';
import { PartAttributeCreateWithoutPartInputObjectSchema } from './PartAttributeCreateWithoutPartInput.schema';
import { PartAttributeUncheckedCreateWithoutPartInputObjectSchema } from './PartAttributeUncheckedCreateWithoutPartInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartAttributeUpsertWithWhereUniqueWithoutPartInput>;
export const PartAttributeUpsertWithWhereUniqueWithoutPartInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartAttributeWhereUniqueInputObjectSchema), update: z.union([z.lazy(() => PartAttributeUpdateWithoutPartInputObjectSchema), z.lazy(() => PartAttributeUncheckedUpdateWithoutPartInputObjectSchema)]), create: z.union([z.lazy(() => PartAttributeCreateWithoutPartInputObjectSchema), z.lazy(() => PartAttributeUncheckedCreateWithoutPartInputObjectSchema)])
}).strict() as SchemaType;
