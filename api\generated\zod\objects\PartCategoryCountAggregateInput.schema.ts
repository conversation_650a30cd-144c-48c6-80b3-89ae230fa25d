/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartCategoryCountAggregateInputType>;
export const PartCategoryCountAggregateInputObjectSchema: SchemaType = z.object({
    id: z.literal(true).optional().optional(), name: z.literal(true).optional().optional(), slug: z.literal(true).optional().optional(), description: z.literal(true).optional().optional(), parentId: z.literal(true).optional().optional(), level: z.literal(true).optional().optional(), path: z.literal(true).optional().optional(), icon: z.literal(true).optional().optional(), imageId: z.literal(true).optional().optional(), createdAt: z.literal(true).optional().optional(), updatedAt: z.literal(true).optional().optional(), _all: z.literal(true).optional().optional()
}).strict() as SchemaType;
