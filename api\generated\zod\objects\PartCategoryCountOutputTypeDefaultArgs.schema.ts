/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartCategoryCountOutputTypeSelectObjectSchema } from './PartCategoryCountOutputTypeSelect.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartCategoryCountOutputTypeDefaultArgs>;
export const PartCategoryCountOutputTypeDefaultArgsObjectSchema: SchemaType = z.object({
    select: z.lazy(() => PartCategoryCountOutputTypeSelectObjectSchema).optional().optional()
}).strict() as SchemaType;
