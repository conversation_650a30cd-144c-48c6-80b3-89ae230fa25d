/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartCategoryCountOutputTypeSelect>;
export const PartCategoryCountOutputTypeSelectObjectSchema: SchemaType = z.object({
    children: z.boolean().optional().optional(), parts: z.boolean().optional().optional()
}).strict() as SchemaType;
