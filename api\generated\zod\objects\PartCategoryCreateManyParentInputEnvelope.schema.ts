/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartCategoryCreateManyParentInputObjectSchema } from './PartCategoryCreateManyParentInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartCategoryCreateManyParentInputEnvelope>;
export const PartCategoryCreateManyParentInputEnvelopeObjectSchema: SchemaType = z.object({
    data: z.union([z.lazy(() => PartCategoryCreateManyParentInputObjectSchema),
    z.lazy(() => PartCategoryCreateManyParentInputObjectSchema).array()]), skipDuplicates: z.boolean().optional().optional()
}).strict() as SchemaType;
