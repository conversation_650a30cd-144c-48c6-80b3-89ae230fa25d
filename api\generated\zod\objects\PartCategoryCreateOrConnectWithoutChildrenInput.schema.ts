/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartCategoryWhereUniqueInputObjectSchema } from './PartCategoryWhereUniqueInput.schema';
import { PartCategoryCreateWithoutChildrenInputObjectSchema } from './PartCategoryCreateWithoutChildrenInput.schema';
import { PartCategoryUncheckedCreateWithoutChildrenInputObjectSchema } from './PartCategoryUncheckedCreateWithoutChildrenInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartCategoryCreateOrConnectWithoutChildrenInput>;
export const PartCategoryCreateOrConnectWithoutChildrenInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartCategoryWhereUniqueInputObjectSchema), create: z.union([z.lazy(() => PartCategoryCreateWithoutChildrenInputObjectSchema), z.lazy(() => PartCategoryUncheckedCreateWithoutChildrenInputObjectSchema)])
}).strict() as SchemaType;
