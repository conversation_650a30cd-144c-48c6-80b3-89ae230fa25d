/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartCategoryWhereUniqueInputObjectSchema } from './PartCategoryWhereUniqueInput.schema';
import { PartCategoryCreateWithoutParentInputObjectSchema } from './PartCategoryCreateWithoutParentInput.schema';
import { PartCategoryUncheckedCreateWithoutParentInputObjectSchema } from './PartCategoryUncheckedCreateWithoutParentInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartCategoryCreateOrConnectWithoutParentInput>;
export const PartCategoryCreateOrConnectWithoutParentInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartCategoryWhereUniqueInputObjectSchema), create: z.union([z.lazy(() => PartCategoryCreateWithoutParentInputObjectSchema), z.lazy(() => PartCategoryUncheckedCreateWithoutParentInputObjectSchema)])
}).strict() as SchemaType;
