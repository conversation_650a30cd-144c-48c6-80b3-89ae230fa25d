/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartCategoryWhereUniqueInputObjectSchema } from './PartCategoryWhereUniqueInput.schema';
import { PartCategoryCreateWithoutPartsInputObjectSchema } from './PartCategoryCreateWithoutPartsInput.schema';
import { PartCategoryUncheckedCreateWithoutPartsInputObjectSchema } from './PartCategoryUncheckedCreateWithoutPartsInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartCategoryCreateOrConnectWithoutPartsInput>;
export const PartCategoryCreateOrConnectWithoutPartsInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartCategoryWhereUniqueInputObjectSchema), create: z.union([z.lazy(() => PartCategoryCreateWithoutPartsInputObjectSchema), z.lazy(() => PartCategoryUncheckedCreateWithoutPartsInputObjectSchema)])
}).strict() as SchemaType;
