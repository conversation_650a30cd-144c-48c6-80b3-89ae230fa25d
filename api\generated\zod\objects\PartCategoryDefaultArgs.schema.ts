/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartCategorySelectObjectSchema } from './PartCategorySelect.schema';
import { PartCategoryIncludeObjectSchema } from './PartCategoryInclude.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartCategoryDefaultArgs>;
export const PartCategoryDefaultArgsObjectSchema: SchemaType = z.object({
    select: z.lazy(() => PartCategorySelectObjectSchema).optional().optional(), include: z.lazy(() => PartCategoryIncludeObjectSchema).optional().optional()
}).strict() as SchemaType;
