/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartCategoryDefaultArgsObjectSchema } from './PartCategoryDefaultArgs.schema';
import { PartCategoryInputSchema } from '../input/PartCategoryInput.schema';
import { PartInputSchema } from '../input/PartInput.schema';
import { MediaAssetDefaultArgsObjectSchema } from './MediaAssetDefaultArgs.schema';
import { PartCategoryCountOutputTypeDefaultArgsObjectSchema } from './PartCategoryCountOutputTypeDefaultArgs.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartCategoryInclude>;
export const PartCategoryIncludeObjectSchema: SchemaType = z.object({
    parent: z.union([z.boolean(),
    z.lazy(() => PartCategoryDefaultArgsObjectSchema)]).optional(), children: z.union([z.boolean(),
    z.lazy(() => PartCategoryInputSchema.findMany)]).optional(), parts: z.union([z.boolean(),
    z.lazy(() => PartInputSchema.findMany)]).optional(), image: z.union([z.boolean(),
    z.lazy(() => MediaAssetDefaultArgsObjectSchema)]).optional(), _count: z.union([z.boolean(),
    z.lazy(() => PartCategoryCountOutputTypeDefaultArgsObjectSchema)]).optional()
}).strict() as SchemaType;
