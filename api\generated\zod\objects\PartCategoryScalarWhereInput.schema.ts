/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { IntFilterObjectSchema } from './IntFilter.schema';
import { StringFilterObjectSchema } from './StringFilter.schema';
import { StringNullableFilterObjectSchema } from './StringNullableFilter.schema';
import { IntNullableFilterObjectSchema } from './IntNullableFilter.schema';
import { DateTimeFilterObjectSchema } from './DateTimeFilter.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartCategoryScalarWhereInput>;
export const PartCategoryScalarWhereInputObjectSchema: SchemaType = z.object({
    AND: z.union([z.lazy(() => PartCategoryScalarWhereInputObjectSchema),
    z.lazy(() => PartCategoryScalarWhereInputObjectSchema).array()]).optional(), OR: z.lazy(() => PartCategoryScalarWhereInputObjectSchema).array().optional().optional(), NOT: z.union([z.lazy(() => PartCategoryScalarWhereInputObjectSchema),
    z.lazy(() => PartCategoryScalarWhereInputObjectSchema).array()]).optional(), id: z.union([z.lazy(() => IntFilterObjectSchema),
    z.number()]).optional(), name: z.union([z.lazy(() => StringFilterObjectSchema),
    z.string()]).optional(), slug: z.union([z.lazy(() => StringFilterObjectSchema),
    z.string()]).optional(), description: z.union([z.lazy(() => StringNullableFilterObjectSchema),
    z.string(),
    z.null()]).optional().nullable(), parentId: z.union([z.lazy(() => IntNullableFilterObjectSchema),
    z.number(),
    z.null()]).optional().nullable(), level: z.union([z.lazy(() => IntFilterObjectSchema),
    z.number()]).optional(), path: z.union([z.lazy(() => StringFilterObjectSchema),
    z.string()]).optional(), icon: z.union([z.lazy(() => StringNullableFilterObjectSchema),
    z.string(),
    z.null()]).optional().nullable(), imageId: z.union([z.lazy(() => IntNullableFilterObjectSchema),
    z.number(),
    z.null()]).optional().nullable(), createdAt: z.union([z.lazy(() => DateTimeFilterObjectSchema),
    z.union([z.date(), z.string().datetime().optional()])]).optional(), updatedAt: z.union([z.lazy(() => DateTimeFilterObjectSchema),
    z.union([z.date(), z.string().datetime().optional()])]).optional()
}).strict() as SchemaType;
