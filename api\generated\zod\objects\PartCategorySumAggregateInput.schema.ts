/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartCategorySumAggregateInputType>;
export const PartCategorySumAggregateInputObjectSchema: SchemaType = z.object({
    id: z.literal(true).optional().optional(), parentId: z.literal(true).optional().optional(), level: z.literal(true).optional().optional(), imageId: z.literal(true).optional().optional()
}).strict() as SchemaType;
