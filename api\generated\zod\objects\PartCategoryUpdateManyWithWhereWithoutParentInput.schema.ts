/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartCategoryScalarWhereInputObjectSchema } from './PartCategoryScalarWhereInput.schema';
import { PartCategoryUpdateManyMutationInputObjectSchema } from './PartCategoryUpdateManyMutationInput.schema';
import { PartCategoryUncheckedUpdateManyWithoutParentInputObjectSchema } from './PartCategoryUncheckedUpdateManyWithoutParentInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartCategoryUpdateManyWithWhereWithoutParentInput>;
export const PartCategoryUpdateManyWithWhereWithoutParentInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartCategoryScalarWhereInputObjectSchema), data: z.union([z.lazy(() => PartCategoryUpdateManyMutationInputObjectSchema), z.lazy(() => PartCategoryUncheckedUpdateManyWithoutParentInputObjectSchema)])
}).strict() as SchemaType;
