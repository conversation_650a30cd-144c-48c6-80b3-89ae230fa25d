/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartCategoryWhereInputObjectSchema } from './PartCategoryWhereInput.schema';
import { PartCategoryUpdateWithoutChildrenInputObjectSchema } from './PartCategoryUpdateWithoutChildrenInput.schema';
import { PartCategoryUncheckedUpdateWithoutChildrenInputObjectSchema } from './PartCategoryUncheckedUpdateWithoutChildrenInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartCategoryUpdateToOneWithWhereWithoutChildrenInput>;
export const PartCategoryUpdateToOneWithWhereWithoutChildrenInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartCategoryWhereInputObjectSchema).optional().optional(), data: z.union([z.lazy(() => PartCategoryUpdateWithoutChildrenInputObjectSchema), z.lazy(() => PartCategoryUncheckedUpdateWithoutChildrenInputObjectSchema)])
}).strict() as SchemaType;
