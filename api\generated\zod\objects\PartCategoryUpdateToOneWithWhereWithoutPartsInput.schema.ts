/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartCategoryWhereInputObjectSchema } from './PartCategoryWhereInput.schema';
import { PartCategoryUpdateWithoutPartsInputObjectSchema } from './PartCategoryUpdateWithoutPartsInput.schema';
import { PartCategoryUncheckedUpdateWithoutPartsInputObjectSchema } from './PartCategoryUncheckedUpdateWithoutPartsInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartCategoryUpdateToOneWithWhereWithoutPartsInput>;
export const PartCategoryUpdateToOneWithWhereWithoutPartsInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartCategoryWhereInputObjectSchema).optional().optional(), data: z.union([z.lazy(() => PartCategoryUpdateWithoutPartsInputObjectSchema), z.lazy(() => PartCategoryUncheckedUpdateWithoutPartsInputObjectSchema)])
}).strict() as SchemaType;
