/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartCategoryWhereUniqueInputObjectSchema } from './PartCategoryWhereUniqueInput.schema';
import { PartCategoryUpdateWithoutParentInputObjectSchema } from './PartCategoryUpdateWithoutParentInput.schema';
import { PartCategoryUncheckedUpdateWithoutParentInputObjectSchema } from './PartCategoryUncheckedUpdateWithoutParentInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartCategoryUpdateWithWhereUniqueWithoutParentInput>;
export const PartCategoryUpdateWithWhereUniqueWithoutParentInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartCategoryWhereUniqueInputObjectSchema), data: z.union([z.lazy(() => PartCategoryUpdateWithoutParentInputObjectSchema), z.lazy(() => PartCategoryUncheckedUpdateWithoutParentInputObjectSchema)])
}).strict() as SchemaType;
