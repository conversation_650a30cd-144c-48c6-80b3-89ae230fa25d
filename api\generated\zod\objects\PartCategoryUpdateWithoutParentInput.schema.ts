/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { StringFieldUpdateOperationsInputObjectSchema } from './StringFieldUpdateOperationsInput.schema';
import { NullableStringFieldUpdateOperationsInputObjectSchema } from './NullableStringFieldUpdateOperationsInput.schema';
import { IntFieldUpdateOperationsInputObjectSchema } from './IntFieldUpdateOperationsInput.schema';
import { DateTimeFieldUpdateOperationsInputObjectSchema } from './DateTimeFieldUpdateOperationsInput.schema';
import { PartCategoryUpdateManyWithoutParentNestedInputObjectSchema } from './PartCategoryUpdateManyWithoutParentNestedInput.schema';
import { PartUpdateManyWithoutPartCategoryNestedInputObjectSchema } from './PartUpdateManyWithoutPartCategoryNestedInput.schema';
import { MediaAssetUpdateOneWithoutPartCategoryNestedInputObjectSchema } from './MediaAssetUpdateOneWithoutPartCategoryNestedInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartCategoryUpdateWithoutParentInput>;
export const PartCategoryUpdateWithoutParentInputObjectSchema: SchemaType = z.object({
    name: z.union([z.string(),
    z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(), slug: z.union([z.string(),
    z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(), description: z.union([z.string(),
    z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
    z.null()]).optional().nullable(), level: z.union([z.number(),
    z.lazy(() => IntFieldUpdateOperationsInputObjectSchema)]).optional(), path: z.union([z.string(),
    z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(), icon: z.union([z.string(),
    z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
    z.null()]).optional().nullable(), createdAt: z.union([z.union([z.date(), z.string().datetime().optional()]),
    z.lazy(() => DateTimeFieldUpdateOperationsInputObjectSchema)]).optional(), updatedAt: z.union([z.union([z.date(), z.string().datetime().optional()]),
    z.lazy(() => DateTimeFieldUpdateOperationsInputObjectSchema)]).optional(), children: z.lazy(() => PartCategoryUpdateManyWithoutParentNestedInputObjectSchema).optional().optional(), parts: z.lazy(() => PartUpdateManyWithoutPartCategoryNestedInputObjectSchema).optional().optional(), image: z.lazy(() => MediaAssetUpdateOneWithoutPartCategoryNestedInputObjectSchema).optional().optional()
}).strict() as SchemaType;
