/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartCategoryWhereUniqueInputObjectSchema } from './PartCategoryWhereUniqueInput.schema';
import { PartCategoryUpdateWithoutParentInputObjectSchema } from './PartCategoryUpdateWithoutParentInput.schema';
import { PartCategoryUncheckedUpdateWithoutParentInputObjectSchema } from './PartCategoryUncheckedUpdateWithoutParentInput.schema';
import { PartCategoryCreateWithoutParentInputObjectSchema } from './PartCategoryCreateWithoutParentInput.schema';
import { PartCategoryUncheckedCreateWithoutParentInputObjectSchema } from './PartCategoryUncheckedCreateWithoutParentInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartCategoryUpsertWithWhereUniqueWithoutParentInput>;
export const PartCategoryUpsertWithWhereUniqueWithoutParentInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartCategoryWhereUniqueInputObjectSchema), update: z.union([z.lazy(() => PartCategoryUpdateWithoutParentInputObjectSchema), z.lazy(() => PartCategoryUncheckedUpdateWithoutParentInputObjectSchema)]), create: z.union([z.lazy(() => PartCategoryCreateWithoutParentInputObjectSchema), z.lazy(() => PartCategoryUncheckedCreateWithoutParentInputObjectSchema)])
}).strict() as SchemaType;
