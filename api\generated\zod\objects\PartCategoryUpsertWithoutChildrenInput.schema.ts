/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartCategoryUpdateWithoutChildrenInputObjectSchema } from './PartCategoryUpdateWithoutChildrenInput.schema';
import { PartCategoryUncheckedUpdateWithoutChildrenInputObjectSchema } from './PartCategoryUncheckedUpdateWithoutChildrenInput.schema';
import { PartCategoryCreateWithoutChildrenInputObjectSchema } from './PartCategoryCreateWithoutChildrenInput.schema';
import { PartCategoryUncheckedCreateWithoutChildrenInputObjectSchema } from './PartCategoryUncheckedCreateWithoutChildrenInput.schema';
import { PartCategoryWhereInputObjectSchema } from './PartCategoryWhereInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartCategoryUpsertWithoutChildrenInput>;
export const PartCategoryUpsertWithoutChildrenInputObjectSchema: SchemaType = z.object({
    update: z.union([z.lazy(() => PartCategoryUpdateWithoutChildrenInputObjectSchema), z.lazy(() => PartCategoryUncheckedUpdateWithoutChildrenInputObjectSchema)]), create: z.union([z.lazy(() => PartCategoryCreateWithoutChildrenInputObjectSchema), z.lazy(() => PartCategoryUncheckedCreateWithoutChildrenInputObjectSchema)]), where: z.lazy(() => PartCategoryWhereInputObjectSchema).optional().optional()
}).strict() as SchemaType;
