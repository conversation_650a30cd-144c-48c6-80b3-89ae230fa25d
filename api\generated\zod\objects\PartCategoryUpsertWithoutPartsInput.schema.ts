/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartCategoryUpdateWithoutPartsInputObjectSchema } from './PartCategoryUpdateWithoutPartsInput.schema';
import { PartCategoryUncheckedUpdateWithoutPartsInputObjectSchema } from './PartCategoryUncheckedUpdateWithoutPartsInput.schema';
import { PartCategoryCreateWithoutPartsInputObjectSchema } from './PartCategoryCreateWithoutPartsInput.schema';
import { PartCategoryUncheckedCreateWithoutPartsInputObjectSchema } from './PartCategoryUncheckedCreateWithoutPartsInput.schema';
import { PartCategoryWhereInputObjectSchema } from './PartCategoryWhereInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartCategoryUpsertWithoutPartsInput>;
export const PartCategoryUpsertWithoutPartsInputObjectSchema: SchemaType = z.object({
    update: z.union([z.lazy(() => PartCategoryUpdateWithoutPartsInputObjectSchema), z.lazy(() => PartCategoryUncheckedUpdateWithoutPartsInputObjectSchema)]), create: z.union([z.lazy(() => PartCategoryCreateWithoutPartsInputObjectSchema), z.lazy(() => PartCategoryUncheckedCreateWithoutPartsInputObjectSchema)]), where: z.lazy(() => PartCategoryWhereInputObjectSchema).optional().optional()
}).strict() as SchemaType;
