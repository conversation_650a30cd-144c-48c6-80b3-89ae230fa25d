/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartCountOutputTypeSelectObjectSchema } from './PartCountOutputTypeSelect.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartCountOutputTypeDefaultArgs>;
export const PartCountOutputTypeDefaultArgsObjectSchema: SchemaType = z.object({
    select: z.lazy(() => PartCountOutputTypeSelectObjectSchema).optional().optional()
}).strict() as SchemaType;
