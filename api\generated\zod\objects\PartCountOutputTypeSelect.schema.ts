/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartCountOutputTypeSelect>;
export const PartCountOutputTypeSelectObjectSchema: SchemaType = z.object({
    children: z.boolean().optional().optional(), attributes: z.boolean().optional().optional(), applicabilities: z.boolean().optional().optional(), equipmentApplicabilities: z.boolean().optional().optional(), matchingProposals: z.boolean().optional().optional(), aggregateSchemas: z.boolean().optional().optional(), schemaPositions: z.boolean().optional().optional(), mediaAssets: z.boolean().optional().optional()
}).strict() as SchemaType;
