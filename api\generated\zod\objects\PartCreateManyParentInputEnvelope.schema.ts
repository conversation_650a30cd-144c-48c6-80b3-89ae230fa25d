/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartCreateManyParentInputObjectSchema } from './PartCreateManyParentInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartCreateManyParentInputEnvelope>;
export const PartCreateManyParentInputEnvelopeObjectSchema: SchemaType = z.object({
    data: z.union([z.lazy(() => PartCreateManyParentInputObjectSchema),
    z.lazy(() => PartCreateManyParentInputObjectSchema).array()]), skipDuplicates: z.boolean().optional().optional()
}).strict() as SchemaType;
