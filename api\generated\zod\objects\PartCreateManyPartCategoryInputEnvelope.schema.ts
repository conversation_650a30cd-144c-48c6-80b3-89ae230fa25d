/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartCreateManyPartCategoryInputObjectSchema } from './PartCreateManyPartCategoryInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartCreateManyPartCategoryInputEnvelope>;
export const PartCreateManyPartCategoryInputEnvelopeObjectSchema: SchemaType = z.object({
    data: z.union([z.lazy(() => PartCreateManyPartCategoryInputObjectSchema),
    z.lazy(() => PartCreateManyPartCategoryInputObjectSchema).array()]), skipDuplicates: z.boolean().optional().optional()
}).strict() as SchemaType;
