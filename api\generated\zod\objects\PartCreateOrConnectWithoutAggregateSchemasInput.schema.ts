/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartWhereUniqueInputObjectSchema } from './PartWhereUniqueInput.schema';
import { PartCreateWithoutAggregateSchemasInputObjectSchema } from './PartCreateWithoutAggregateSchemasInput.schema';
import { PartUncheckedCreateWithoutAggregateSchemasInputObjectSchema } from './PartUncheckedCreateWithoutAggregateSchemasInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartCreateOrConnectWithoutAggregateSchemasInput>;
export const PartCreateOrConnectWithoutAggregateSchemasInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartWhereUniqueInputObjectSchema), create: z.union([z.lazy(() => PartCreateWithoutAggregateSchemasInputObjectSchema), z.lazy(() => PartUncheckedCreateWithoutAggregateSchemasInputObjectSchema)])
}).strict() as SchemaType;
