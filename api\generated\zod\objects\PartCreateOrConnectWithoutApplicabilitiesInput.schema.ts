/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartWhereUniqueInputObjectSchema } from './PartWhereUniqueInput.schema';
import { PartCreateWithoutApplicabilitiesInputObjectSchema } from './PartCreateWithoutApplicabilitiesInput.schema';
import { PartUncheckedCreateWithoutApplicabilitiesInputObjectSchema } from './PartUncheckedCreateWithoutApplicabilitiesInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartCreateOrConnectWithoutApplicabilitiesInput>;
export const PartCreateOrConnectWithoutApplicabilitiesInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartWhereUniqueInputObjectSchema), create: z.union([z.lazy(() => PartCreateWithoutApplicabilitiesInputObjectSchema), z.lazy(() => PartUncheckedCreateWithoutApplicabilitiesInputObjectSchema)])
}).strict() as SchemaType;
