/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartWhereUniqueInputObjectSchema } from './PartWhereUniqueInput.schema';
import { PartCreateWithoutAttributesInputObjectSchema } from './PartCreateWithoutAttributesInput.schema';
import { PartUncheckedCreateWithoutAttributesInputObjectSchema } from './PartUncheckedCreateWithoutAttributesInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartCreateOrConnectWithoutAttributesInput>;
export const PartCreateOrConnectWithoutAttributesInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartWhereUniqueInputObjectSchema), create: z.union([z.lazy(() => PartCreateWithoutAttributesInputObjectSchema), z.lazy(() => PartUncheckedCreateWithoutAttributesInputObjectSchema)])
}).strict() as SchemaType;
