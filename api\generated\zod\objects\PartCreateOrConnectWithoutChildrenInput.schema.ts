/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartWhereUniqueInputObjectSchema } from './PartWhereUniqueInput.schema';
import { PartCreateWithoutChildrenInputObjectSchema } from './PartCreateWithoutChildrenInput.schema';
import { PartUncheckedCreateWithoutChildrenInputObjectSchema } from './PartUncheckedCreateWithoutChildrenInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartCreateOrConnectWithoutChildrenInput>;
export const PartCreateOrConnectWithoutChildrenInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartWhereUniqueInputObjectSchema), create: z.union([z.lazy(() => PartCreateWithoutChildrenInputObjectSchema), z.lazy(() => PartUncheckedCreateWithoutChildrenInputObjectSchema)])
}).strict() as SchemaType;
