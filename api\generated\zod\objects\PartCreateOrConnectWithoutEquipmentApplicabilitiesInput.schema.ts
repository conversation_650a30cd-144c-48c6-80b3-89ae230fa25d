/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartWhereUniqueInputObjectSchema } from './PartWhereUniqueInput.schema';
import { PartCreateWithoutEquipmentApplicabilitiesInputObjectSchema } from './PartCreateWithoutEquipmentApplicabilitiesInput.schema';
import { PartUncheckedCreateWithoutEquipmentApplicabilitiesInputObjectSchema } from './PartUncheckedCreateWithoutEquipmentApplicabilitiesInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartCreateOrConnectWithoutEquipmentApplicabilitiesInput>;
export const PartCreateOrConnectWithoutEquipmentApplicabilitiesInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartWhereUniqueInputObjectSchema), create: z.union([z.lazy(() => PartCreateWithoutEquipmentApplicabilitiesInputObjectSchema), z.lazy(() => PartUncheckedCreateWithoutEquipmentApplicabilitiesInputObjectSchema)])
}).strict() as SchemaType;
