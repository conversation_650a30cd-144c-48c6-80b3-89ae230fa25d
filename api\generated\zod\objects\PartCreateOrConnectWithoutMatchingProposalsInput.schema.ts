/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartWhereUniqueInputObjectSchema } from './PartWhereUniqueInput.schema';
import { PartCreateWithoutMatchingProposalsInputObjectSchema } from './PartCreateWithoutMatchingProposalsInput.schema';
import { PartUncheckedCreateWithoutMatchingProposalsInputObjectSchema } from './PartUncheckedCreateWithoutMatchingProposalsInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartCreateOrConnectWithoutMatchingProposalsInput>;
export const PartCreateOrConnectWithoutMatchingProposalsInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartWhereUniqueInputObjectSchema), create: z.union([z.lazy(() => PartCreateWithoutMatchingProposalsInputObjectSchema), z.lazy(() => PartUncheckedCreateWithoutMatchingProposalsInputObjectSchema)])
}).strict() as SchemaType;
