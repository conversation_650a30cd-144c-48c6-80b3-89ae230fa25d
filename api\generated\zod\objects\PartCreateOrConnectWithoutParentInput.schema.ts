/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartWhereUniqueInputObjectSchema } from './PartWhereUniqueInput.schema';
import { PartCreateWithoutParentInputObjectSchema } from './PartCreateWithoutParentInput.schema';
import { PartUncheckedCreateWithoutParentInputObjectSchema } from './PartUncheckedCreateWithoutParentInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartCreateOrConnectWithoutParentInput>;
export const PartCreateOrConnectWithoutParentInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartWhereUniqueInputObjectSchema), create: z.union([z.lazy(() => PartCreateWithoutParentInputObjectSchema), z.lazy(() => PartUncheckedCreateWithoutParentInputObjectSchema)])
}).strict() as SchemaType;
