/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartWhereUniqueInputObjectSchema } from './PartWhereUniqueInput.schema';
import { PartCreateWithoutPartCategoryInputObjectSchema } from './PartCreateWithoutPartCategoryInput.schema';
import { PartUncheckedCreateWithoutPartCategoryInputObjectSchema } from './PartUncheckedCreateWithoutPartCategoryInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartCreateOrConnectWithoutPartCategoryInput>;
export const PartCreateOrConnectWithoutPartCategoryInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartWhereUniqueInputObjectSchema), create: z.union([z.lazy(() => PartCreateWithoutPartCategoryInputObjectSchema), z.lazy(() => PartUncheckedCreateWithoutPartCategoryInputObjectSchema)])
}).strict() as SchemaType;
