/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartWhereUniqueInputObjectSchema } from './PartWhereUniqueInput.schema';
import { PartCreateWithoutSchemaPositionsInputObjectSchema } from './PartCreateWithoutSchemaPositionsInput.schema';
import { PartUncheckedCreateWithoutSchemaPositionsInputObjectSchema } from './PartUncheckedCreateWithoutSchemaPositionsInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartCreateOrConnectWithoutSchemaPositionsInput>;
export const PartCreateOrConnectWithoutSchemaPositionsInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartWhereUniqueInputObjectSchema), create: z.union([z.lazy(() => PartCreateWithoutSchemaPositionsInputObjectSchema), z.lazy(() => PartUncheckedCreateWithoutSchemaPositionsInputObjectSchema)])
}).strict() as SchemaType;
