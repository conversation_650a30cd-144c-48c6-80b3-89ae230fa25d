/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartCreateNestedOneWithoutChildrenInputObjectSchema } from './PartCreateNestedOneWithoutChildrenInput.schema';
import { PartCreateNestedManyWithoutParentInputObjectSchema } from './PartCreateNestedManyWithoutParentInput.schema';
import { PartAttributeCreateNestedManyWithoutPartInputObjectSchema } from './PartAttributeCreateNestedManyWithoutPartInput.schema';
import { EquipmentApplicabilityCreateNestedManyWithoutPartInputObjectSchema } from './EquipmentApplicabilityCreateNestedManyWithoutPartInput.schema';
import { MatchingProposalCreateNestedManyWithoutPartInputObjectSchema } from './MatchingProposalCreateNestedManyWithoutPartInput.schema';
import { AggregateSchemaCreateNestedManyWithoutPartInputObjectSchema } from './AggregateSchemaCreateNestedManyWithoutPartInput.schema';
import { SchemaPositionCreateNestedManyWithoutPartInputObjectSchema } from './SchemaPositionCreateNestedManyWithoutPartInput.schema';
import { MediaAssetCreateNestedOneWithoutPartInputObjectSchema } from './MediaAssetCreateNestedOneWithoutPartInput.schema';
import { MediaAssetCreateNestedManyWithoutPartsInputObjectSchema } from './MediaAssetCreateNestedManyWithoutPartsInput.schema';
import { PartCategoryCreateNestedOneWithoutPartsInputObjectSchema } from './PartCategoryCreateNestedOneWithoutPartsInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartCreateWithoutApplicabilitiesInput>;
export const PartCreateWithoutApplicabilitiesInputObjectSchema: SchemaType = z.object({
    createdAt: z.union([z.date().optional(), z.string().datetime().optional()]).optional(), updatedAt: z.union([z.date().optional(), z.string().datetime().optional()]).optional(), name: z.union([z.string(),
    z.null()]).optional().nullable(), level: z.number().optional().optional(), path: z.string(), parent: z.lazy(() => PartCreateNestedOneWithoutChildrenInputObjectSchema).optional().optional(), children: z.lazy(() => PartCreateNestedManyWithoutParentInputObjectSchema).optional().optional(), attributes: z.lazy(() => PartAttributeCreateNestedManyWithoutPartInputObjectSchema).optional().optional(), equipmentApplicabilities: z.lazy(() => EquipmentApplicabilityCreateNestedManyWithoutPartInputObjectSchema).optional().optional(), matchingProposals: z.lazy(() => MatchingProposalCreateNestedManyWithoutPartInputObjectSchema).optional().optional(), aggregateSchemas: z.lazy(() => AggregateSchemaCreateNestedManyWithoutPartInputObjectSchema).optional().optional(), schemaPositions: z.lazy(() => SchemaPositionCreateNestedManyWithoutPartInputObjectSchema).optional().optional(), image: z.lazy(() => MediaAssetCreateNestedOneWithoutPartInputObjectSchema).optional().optional(), mediaAssets: z.lazy(() => MediaAssetCreateNestedManyWithoutPartsInputObjectSchema).optional().optional(), partCategory: z.lazy(() => PartCategoryCreateNestedOneWithoutPartsInputObjectSchema)
}).strict() as SchemaType;
