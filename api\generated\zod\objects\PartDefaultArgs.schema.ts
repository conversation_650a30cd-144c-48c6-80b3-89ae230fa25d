/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartSelectObjectSchema } from './PartSelect.schema';
import { PartIncludeObjectSchema } from './PartInclude.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartDefaultArgs>;
export const PartDefaultArgsObjectSchema: SchemaType = z.object({
    select: z.lazy(() => PartSelectObjectSchema).optional().optional(), include: z.lazy(() => PartIncludeObjectSchema).optional().optional()
}).strict() as SchemaType;
