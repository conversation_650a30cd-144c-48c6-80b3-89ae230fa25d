/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartDefaultArgsObjectSchema } from './PartDefaultArgs.schema';
import { PartInputSchema } from '../input/PartInput.schema';
import { PartAttributeInputSchema } from '../input/PartAttributeInput.schema';
import { PartApplicabilityInputSchema } from '../input/PartApplicabilityInput.schema';
import { EquipmentApplicabilityInputSchema } from '../input/EquipmentApplicabilityInput.schema';
import { MatchingProposalInputSchema } from '../input/MatchingProposalInput.schema';
import { AggregateSchemaInputSchema } from '../input/AggregateSchemaInput.schema';
import { SchemaPositionInputSchema } from '../input/SchemaPositionInput.schema';
import { MediaAssetDefaultArgsObjectSchema } from './MediaAssetDefaultArgs.schema';
import { MediaAssetInputSchema } from '../input/MediaAssetInput.schema';
import { PartCategoryDefaultArgsObjectSchema } from './PartCategoryDefaultArgs.schema';
import { PartCountOutputTypeDefaultArgsObjectSchema } from './PartCountOutputTypeDefaultArgs.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartInclude>;
export const PartIncludeObjectSchema: SchemaType = z.object({
    parent: z.union([z.boolean(),
    z.lazy(() => PartDefaultArgsObjectSchema)]).optional(), children: z.union([z.boolean(),
    z.lazy(() => PartInputSchema.findMany)]).optional(), attributes: z.union([z.boolean(),
    z.lazy(() => PartAttributeInputSchema.findMany)]).optional(), applicabilities: z.union([z.boolean(),
    z.lazy(() => PartApplicabilityInputSchema.findMany)]).optional(), equipmentApplicabilities: z.union([z.boolean(),
    z.lazy(() => EquipmentApplicabilityInputSchema.findMany)]).optional(), matchingProposals: z.union([z.boolean(),
    z.lazy(() => MatchingProposalInputSchema.findMany)]).optional(), aggregateSchemas: z.union([z.boolean(),
    z.lazy(() => AggregateSchemaInputSchema.findMany)]).optional(), schemaPositions: z.union([z.boolean(),
    z.lazy(() => SchemaPositionInputSchema.findMany)]).optional(), image: z.union([z.boolean(),
    z.lazy(() => MediaAssetDefaultArgsObjectSchema)]).optional(), mediaAssets: z.union([z.boolean(),
    z.lazy(() => MediaAssetInputSchema.findMany)]).optional(), partCategory: z.union([z.boolean(),
    z.lazy(() => PartCategoryDefaultArgsObjectSchema)]).optional(), _count: z.union([z.boolean(),
    z.lazy(() => PartCountOutputTypeDefaultArgsObjectSchema)]).optional()
}).strict() as SchemaType;
