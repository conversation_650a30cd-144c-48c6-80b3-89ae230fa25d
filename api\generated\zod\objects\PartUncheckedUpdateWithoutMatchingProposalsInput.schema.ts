/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { DateTimeFieldUpdateOperationsInputObjectSchema } from './DateTimeFieldUpdateOperationsInput.schema';
import { IntFieldUpdateOperationsInputObjectSchema } from './IntFieldUpdateOperationsInput.schema';
import { NullableStringFieldUpdateOperationsInputObjectSchema } from './NullableStringFieldUpdateOperationsInput.schema';
import { NullableIntFieldUpdateOperationsInputObjectSchema } from './NullableIntFieldUpdateOperationsInput.schema';
import { StringFieldUpdateOperationsInputObjectSchema } from './StringFieldUpdateOperationsInput.schema';
import { PartUncheckedUpdateManyWithoutParentNestedInputObjectSchema } from './PartUncheckedUpdateManyWithoutParentNestedInput.schema';
import { PartAttributeUncheckedUpdateManyWithoutPartNestedInputObjectSchema } from './PartAttributeUncheckedUpdateManyWithoutPartNestedInput.schema';
import { PartApplicabilityUncheckedUpdateManyWithoutPartNestedInputObjectSchema } from './PartApplicabilityUncheckedUpdateManyWithoutPartNestedInput.schema';
import { EquipmentApplicabilityUncheckedUpdateManyWithoutPartNestedInputObjectSchema } from './EquipmentApplicabilityUncheckedUpdateManyWithoutPartNestedInput.schema';
import { AggregateSchemaUncheckedUpdateManyWithoutPartNestedInputObjectSchema } from './AggregateSchemaUncheckedUpdateManyWithoutPartNestedInput.schema';
import { SchemaPositionUncheckedUpdateManyWithoutPartNestedInputObjectSchema } from './SchemaPositionUncheckedUpdateManyWithoutPartNestedInput.schema';
import { MediaAssetUncheckedUpdateManyWithoutPartsNestedInputObjectSchema } from './MediaAssetUncheckedUpdateManyWithoutPartsNestedInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartUncheckedUpdateWithoutMatchingProposalsInput>;
export const PartUncheckedUpdateWithoutMatchingProposalsInputObjectSchema: SchemaType = z.object({
    createdAt: z.union([z.union([z.date(), z.string().datetime().optional()]),
    z.lazy(() => DateTimeFieldUpdateOperationsInputObjectSchema)]).optional(), updatedAt: z.union([z.union([z.date(), z.string().datetime().optional()]),
    z.lazy(() => DateTimeFieldUpdateOperationsInputObjectSchema)]).optional(), id: z.union([z.number(),
    z.lazy(() => IntFieldUpdateOperationsInputObjectSchema)]).optional(), name: z.union([z.string(),
    z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
    z.null()]).optional().nullable(), parentId: z.union([z.number(),
    z.lazy(() => NullableIntFieldUpdateOperationsInputObjectSchema),
    z.null()]).optional().nullable(), level: z.union([z.number(),
    z.lazy(() => IntFieldUpdateOperationsInputObjectSchema)]).optional(), path: z.union([z.string(),
    z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(), imageId: z.union([z.number(),
    z.lazy(() => NullableIntFieldUpdateOperationsInputObjectSchema),
    z.null()]).optional().nullable(), partCategoryId: z.union([z.number(),
    z.lazy(() => IntFieldUpdateOperationsInputObjectSchema)]).optional(), children: z.lazy(() => PartUncheckedUpdateManyWithoutParentNestedInputObjectSchema).optional().optional(), attributes: z.lazy(() => PartAttributeUncheckedUpdateManyWithoutPartNestedInputObjectSchema).optional().optional(), applicabilities: z.lazy(() => PartApplicabilityUncheckedUpdateManyWithoutPartNestedInputObjectSchema).optional().optional(), equipmentApplicabilities: z.lazy(() => EquipmentApplicabilityUncheckedUpdateManyWithoutPartNestedInputObjectSchema).optional().optional(), aggregateSchemas: z.lazy(() => AggregateSchemaUncheckedUpdateManyWithoutPartNestedInputObjectSchema).optional().optional(), schemaPositions: z.lazy(() => SchemaPositionUncheckedUpdateManyWithoutPartNestedInputObjectSchema).optional().optional(), mediaAssets: z.lazy(() => MediaAssetUncheckedUpdateManyWithoutPartsNestedInputObjectSchema).optional().optional()
}).strict() as SchemaType;
