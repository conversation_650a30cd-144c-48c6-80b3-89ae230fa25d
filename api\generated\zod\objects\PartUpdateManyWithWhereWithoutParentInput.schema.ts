/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartScalarWhereInputObjectSchema } from './PartScalarWhereInput.schema';
import { PartUpdateManyMutationInputObjectSchema } from './PartUpdateManyMutationInput.schema';
import { PartUncheckedUpdateManyWithoutParentInputObjectSchema } from './PartUncheckedUpdateManyWithoutParentInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartUpdateManyWithWhereWithoutParentInput>;
export const PartUpdateManyWithWhereWithoutParentInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartScalarWhereInputObjectSchema), data: z.union([z.lazy(() => PartUpdateManyMutationInputObjectSchema), z.lazy(() => PartUncheckedUpdateManyWithoutParentInputObjectSchema)])
}).strict() as SchemaType;
