/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartScalarWhereInputObjectSchema } from './PartScalarWhereInput.schema';
import { PartUpdateManyMutationInputObjectSchema } from './PartUpdateManyMutationInput.schema';
import { PartUncheckedUpdateManyWithoutPartCategoryInputObjectSchema } from './PartUncheckedUpdateManyWithoutPartCategoryInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartUpdateManyWithWhereWithoutPartCategoryInput>;
export const PartUpdateManyWithWhereWithoutPartCategoryInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartScalarWhereInputObjectSchema), data: z.union([z.lazy(() => PartUpdateManyMutationInputObjectSchema), z.lazy(() => PartUncheckedUpdateManyWithoutPartCategoryInputObjectSchema)])
}).strict() as SchemaType;
