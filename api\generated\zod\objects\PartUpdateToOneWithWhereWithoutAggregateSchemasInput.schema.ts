/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartWhereInputObjectSchema } from './PartWhereInput.schema';
import { PartUpdateWithoutAggregateSchemasInputObjectSchema } from './PartUpdateWithoutAggregateSchemasInput.schema';
import { PartUncheckedUpdateWithoutAggregateSchemasInputObjectSchema } from './PartUncheckedUpdateWithoutAggregateSchemasInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartUpdateToOneWithWhereWithoutAggregateSchemasInput>;
export const PartUpdateToOneWithWhereWithoutAggregateSchemasInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartWhereInputObjectSchema).optional().optional(), data: z.union([z.lazy(() => PartUpdateWithoutAggregateSchemasInputObjectSchema), z.lazy(() => PartUncheckedUpdateWithoutAggregateSchemasInputObjectSchema)])
}).strict() as SchemaType;
