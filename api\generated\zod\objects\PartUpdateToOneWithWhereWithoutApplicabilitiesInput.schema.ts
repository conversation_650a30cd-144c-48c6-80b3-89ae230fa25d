/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartWhereInputObjectSchema } from './PartWhereInput.schema';
import { PartUpdateWithoutApplicabilitiesInputObjectSchema } from './PartUpdateWithoutApplicabilitiesInput.schema';
import { PartUncheckedUpdateWithoutApplicabilitiesInputObjectSchema } from './PartUncheckedUpdateWithoutApplicabilitiesInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartUpdateToOneWithWhereWithoutApplicabilitiesInput>;
export const PartUpdateToOneWithWhereWithoutApplicabilitiesInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartWhereInputObjectSchema).optional().optional(), data: z.union([z.lazy(() => PartUpdateWithoutApplicabilitiesInputObjectSchema), z.lazy(() => PartUncheckedUpdateWithoutApplicabilitiesInputObjectSchema)])
}).strict() as SchemaType;
