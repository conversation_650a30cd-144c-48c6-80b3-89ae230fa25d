/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartWhereInputObjectSchema } from './PartWhereInput.schema';
import { PartUpdateWithoutAttributesInputObjectSchema } from './PartUpdateWithoutAttributesInput.schema';
import { PartUncheckedUpdateWithoutAttributesInputObjectSchema } from './PartUncheckedUpdateWithoutAttributesInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartUpdateToOneWithWhereWithoutAttributesInput>;
export const PartUpdateToOneWithWhereWithoutAttributesInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartWhereInputObjectSchema).optional().optional(), data: z.union([z.lazy(() => PartUpdateWithoutAttributesInputObjectSchema), z.lazy(() => PartUncheckedUpdateWithoutAttributesInputObjectSchema)])
}).strict() as SchemaType;
