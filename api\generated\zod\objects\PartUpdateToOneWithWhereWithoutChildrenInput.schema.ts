/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartWhereInputObjectSchema } from './PartWhereInput.schema';
import { PartUpdateWithoutChildrenInputObjectSchema } from './PartUpdateWithoutChildrenInput.schema';
import { PartUncheckedUpdateWithoutChildrenInputObjectSchema } from './PartUncheckedUpdateWithoutChildrenInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartUpdateToOneWithWhereWithoutChildrenInput>;
export const PartUpdateToOneWithWhereWithoutChildrenInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartWhereInputObjectSchema).optional().optional(), data: z.union([z.lazy(() => PartUpdateWithoutChildrenInputObjectSchema), z.lazy(() => PartUncheckedUpdateWithoutChildrenInputObjectSchema)])
}).strict() as SchemaType;
