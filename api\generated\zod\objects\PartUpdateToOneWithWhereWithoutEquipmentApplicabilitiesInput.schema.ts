/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartWhereInputObjectSchema } from './PartWhereInput.schema';
import { PartUpdateWithoutEquipmentApplicabilitiesInputObjectSchema } from './PartUpdateWithoutEquipmentApplicabilitiesInput.schema';
import { PartUncheckedUpdateWithoutEquipmentApplicabilitiesInputObjectSchema } from './PartUncheckedUpdateWithoutEquipmentApplicabilitiesInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartUpdateToOneWithWhereWithoutEquipmentApplicabilitiesInput>;
export const PartUpdateToOneWithWhereWithoutEquipmentApplicabilitiesInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartWhereInputObjectSchema).optional().optional(), data: z.union([z.lazy(() => PartUpdateWithoutEquipmentApplicabilitiesInputObjectSchema), z.lazy(() => PartUncheckedUpdateWithoutEquipmentApplicabilitiesInputObjectSchema)])
}).strict() as SchemaType;
