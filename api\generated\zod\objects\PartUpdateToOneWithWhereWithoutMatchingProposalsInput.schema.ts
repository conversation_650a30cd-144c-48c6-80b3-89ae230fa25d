/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartWhereInputObjectSchema } from './PartWhereInput.schema';
import { PartUpdateWithoutMatchingProposalsInputObjectSchema } from './PartUpdateWithoutMatchingProposalsInput.schema';
import { PartUncheckedUpdateWithoutMatchingProposalsInputObjectSchema } from './PartUncheckedUpdateWithoutMatchingProposalsInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartUpdateToOneWithWhereWithoutMatchingProposalsInput>;
export const PartUpdateToOneWithWhereWithoutMatchingProposalsInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartWhereInputObjectSchema).optional().optional(), data: z.union([z.lazy(() => PartUpdateWithoutMatchingProposalsInputObjectSchema), z.lazy(() => PartUncheckedUpdateWithoutMatchingProposalsInputObjectSchema)])
}).strict() as SchemaType;
