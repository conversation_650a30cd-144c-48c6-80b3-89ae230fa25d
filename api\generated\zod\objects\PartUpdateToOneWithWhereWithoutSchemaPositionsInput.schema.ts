/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartWhereInputObjectSchema } from './PartWhereInput.schema';
import { PartUpdateWithoutSchemaPositionsInputObjectSchema } from './PartUpdateWithoutSchemaPositionsInput.schema';
import { PartUncheckedUpdateWithoutSchemaPositionsInputObjectSchema } from './PartUncheckedUpdateWithoutSchemaPositionsInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartUpdateToOneWithWhereWithoutSchemaPositionsInput>;
export const PartUpdateToOneWithWhereWithoutSchemaPositionsInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartWhereInputObjectSchema).optional().optional(), data: z.union([z.lazy(() => PartUpdateWithoutSchemaPositionsInputObjectSchema), z.lazy(() => PartUncheckedUpdateWithoutSchemaPositionsInputObjectSchema)])
}).strict() as SchemaType;
