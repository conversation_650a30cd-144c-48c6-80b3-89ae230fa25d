/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartWhereUniqueInputObjectSchema } from './PartWhereUniqueInput.schema';
import { PartUpdateWithoutParentInputObjectSchema } from './PartUpdateWithoutParentInput.schema';
import { PartUncheckedUpdateWithoutParentInputObjectSchema } from './PartUncheckedUpdateWithoutParentInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartUpdateWithWhereUniqueWithoutParentInput>;
export const PartUpdateWithWhereUniqueWithoutParentInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartWhereUniqueInputObjectSchema), data: z.union([z.lazy(() => PartUpdateWithoutParentInputObjectSchema), z.lazy(() => PartUncheckedUpdateWithoutParentInputObjectSchema)])
}).strict() as SchemaType;
