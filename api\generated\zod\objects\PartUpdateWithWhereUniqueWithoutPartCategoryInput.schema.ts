/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartWhereUniqueInputObjectSchema } from './PartWhereUniqueInput.schema';
import { PartUpdateWithoutPartCategoryInputObjectSchema } from './PartUpdateWithoutPartCategoryInput.schema';
import { PartUncheckedUpdateWithoutPartCategoryInputObjectSchema } from './PartUncheckedUpdateWithoutPartCategoryInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartUpdateWithWhereUniqueWithoutPartCategoryInput>;
export const PartUpdateWithWhereUniqueWithoutPartCategoryInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartWhereUniqueInputObjectSchema), data: z.union([z.lazy(() => PartUpdateWithoutPartCategoryInputObjectSchema), z.lazy(() => PartUncheckedUpdateWithoutPartCategoryInputObjectSchema)])
}).strict() as SchemaType;
