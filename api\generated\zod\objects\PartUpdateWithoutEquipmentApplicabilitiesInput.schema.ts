/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { DateTimeFieldUpdateOperationsInputObjectSchema } from './DateTimeFieldUpdateOperationsInput.schema';
import { NullableStringFieldUpdateOperationsInputObjectSchema } from './NullableStringFieldUpdateOperationsInput.schema';
import { IntFieldUpdateOperationsInputObjectSchema } from './IntFieldUpdateOperationsInput.schema';
import { StringFieldUpdateOperationsInputObjectSchema } from './StringFieldUpdateOperationsInput.schema';
import { PartUpdateOneWithoutChildrenNestedInputObjectSchema } from './PartUpdateOneWithoutChildrenNestedInput.schema';
import { PartUpdateManyWithoutParentNestedInputObjectSchema } from './PartUpdateManyWithoutParentNestedInput.schema';
import { PartAttributeUpdateManyWithoutPartNestedInputObjectSchema } from './PartAttributeUpdateManyWithoutPartNestedInput.schema';
import { PartApplicabilityUpdateManyWithoutPartNestedInputObjectSchema } from './PartApplicabilityUpdateManyWithoutPartNestedInput.schema';
import { MatchingProposalUpdateManyWithoutPartNestedInputObjectSchema } from './MatchingProposalUpdateManyWithoutPartNestedInput.schema';
import { AggregateSchemaUpdateManyWithoutPartNestedInputObjectSchema } from './AggregateSchemaUpdateManyWithoutPartNestedInput.schema';
import { SchemaPositionUpdateManyWithoutPartNestedInputObjectSchema } from './SchemaPositionUpdateManyWithoutPartNestedInput.schema';
import { MediaAssetUpdateOneWithoutPartNestedInputObjectSchema } from './MediaAssetUpdateOneWithoutPartNestedInput.schema';
import { MediaAssetUpdateManyWithoutPartsNestedInputObjectSchema } from './MediaAssetUpdateManyWithoutPartsNestedInput.schema';
import { PartCategoryUpdateOneRequiredWithoutPartsNestedInputObjectSchema } from './PartCategoryUpdateOneRequiredWithoutPartsNestedInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartUpdateWithoutEquipmentApplicabilitiesInput>;
export const PartUpdateWithoutEquipmentApplicabilitiesInputObjectSchema: SchemaType = z.object({
    createdAt: z.union([z.union([z.date(), z.string().datetime().optional()]),
    z.lazy(() => DateTimeFieldUpdateOperationsInputObjectSchema)]).optional(), updatedAt: z.union([z.union([z.date(), z.string().datetime().optional()]),
    z.lazy(() => DateTimeFieldUpdateOperationsInputObjectSchema)]).optional(), name: z.union([z.string(),
    z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema),
    z.null()]).optional().nullable(), level: z.union([z.number(),
    z.lazy(() => IntFieldUpdateOperationsInputObjectSchema)]).optional(), path: z.union([z.string(),
    z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(), parent: z.lazy(() => PartUpdateOneWithoutChildrenNestedInputObjectSchema).optional().optional(), children: z.lazy(() => PartUpdateManyWithoutParentNestedInputObjectSchema).optional().optional(), attributes: z.lazy(() => PartAttributeUpdateManyWithoutPartNestedInputObjectSchema).optional().optional(), applicabilities: z.lazy(() => PartApplicabilityUpdateManyWithoutPartNestedInputObjectSchema).optional().optional(), matchingProposals: z.lazy(() => MatchingProposalUpdateManyWithoutPartNestedInputObjectSchema).optional().optional(), aggregateSchemas: z.lazy(() => AggregateSchemaUpdateManyWithoutPartNestedInputObjectSchema).optional().optional(), schemaPositions: z.lazy(() => SchemaPositionUpdateManyWithoutPartNestedInputObjectSchema).optional().optional(), image: z.lazy(() => MediaAssetUpdateOneWithoutPartNestedInputObjectSchema).optional().optional(), mediaAssets: z.lazy(() => MediaAssetUpdateManyWithoutPartsNestedInputObjectSchema).optional().optional(), partCategory: z.lazy(() => PartCategoryUpdateOneRequiredWithoutPartsNestedInputObjectSchema).optional().optional()
}).strict() as SchemaType;
