/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartWhereUniqueInputObjectSchema } from './PartWhereUniqueInput.schema';
import { PartUpdateWithoutParentInputObjectSchema } from './PartUpdateWithoutParentInput.schema';
import { PartUncheckedUpdateWithoutParentInputObjectSchema } from './PartUncheckedUpdateWithoutParentInput.schema';
import { PartCreateWithoutParentInputObjectSchema } from './PartCreateWithoutParentInput.schema';
import { PartUncheckedCreateWithoutParentInputObjectSchema } from './PartUncheckedCreateWithoutParentInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartUpsertWithWhereUniqueWithoutParentInput>;
export const PartUpsertWithWhereUniqueWithoutParentInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartWhereUniqueInputObjectSchema), update: z.union([z.lazy(() => PartUpdateWithoutParentInputObjectSchema), z.lazy(() => PartUncheckedUpdateWithoutParentInputObjectSchema)]), create: z.union([z.lazy(() => PartCreateWithoutParentInputObjectSchema), z.lazy(() => PartUncheckedCreateWithoutParentInputObjectSchema)])
}).strict() as SchemaType;
