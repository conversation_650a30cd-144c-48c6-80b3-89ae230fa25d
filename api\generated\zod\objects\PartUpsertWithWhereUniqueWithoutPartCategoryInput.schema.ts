/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartWhereUniqueInputObjectSchema } from './PartWhereUniqueInput.schema';
import { PartUpdateWithoutPartCategoryInputObjectSchema } from './PartUpdateWithoutPartCategoryInput.schema';
import { PartUncheckedUpdateWithoutPartCategoryInputObjectSchema } from './PartUncheckedUpdateWithoutPartCategoryInput.schema';
import { PartCreateWithoutPartCategoryInputObjectSchema } from './PartCreateWithoutPartCategoryInput.schema';
import { PartUncheckedCreateWithoutPartCategoryInputObjectSchema } from './PartUncheckedCreateWithoutPartCategoryInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartUpsertWithWhereUniqueWithoutPartCategoryInput>;
export const PartUpsertWithWhereUniqueWithoutPartCategoryInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => PartWhereUniqueInputObjectSchema), update: z.union([z.lazy(() => PartUpdateWithoutPartCategoryInputObjectSchema), z.lazy(() => PartUncheckedUpdateWithoutPartCategoryInputObjectSchema)]), create: z.union([z.lazy(() => PartCreateWithoutPartCategoryInputObjectSchema), z.lazy(() => PartUncheckedCreateWithoutPartCategoryInputObjectSchema)])
}).strict() as SchemaType;
