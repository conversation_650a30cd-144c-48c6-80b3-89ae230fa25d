/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartUpdateWithoutAggregateSchemasInputObjectSchema } from './PartUpdateWithoutAggregateSchemasInput.schema';
import { PartUncheckedUpdateWithoutAggregateSchemasInputObjectSchema } from './PartUncheckedUpdateWithoutAggregateSchemasInput.schema';
import { PartCreateWithoutAggregateSchemasInputObjectSchema } from './PartCreateWithoutAggregateSchemasInput.schema';
import { PartUncheckedCreateWithoutAggregateSchemasInputObjectSchema } from './PartUncheckedCreateWithoutAggregateSchemasInput.schema';
import { PartWhereInputObjectSchema } from './PartWhereInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartUpsertWithoutAggregateSchemasInput>;
export const PartUpsertWithoutAggregateSchemasInputObjectSchema: SchemaType = z.object({
    update: z.union([z.lazy(() => PartUpdateWithoutAggregateSchemasInputObjectSchema), z.lazy(() => PartUncheckedUpdateWithoutAggregateSchemasInputObjectSchema)]), create: z.union([z.lazy(() => PartCreateWithoutAggregateSchemasInputObjectSchema), z.lazy(() => PartUncheckedCreateWithoutAggregateSchemasInputObjectSchema)]), where: z.lazy(() => PartWhereInputObjectSchema).optional().optional()
}).strict() as SchemaType;
