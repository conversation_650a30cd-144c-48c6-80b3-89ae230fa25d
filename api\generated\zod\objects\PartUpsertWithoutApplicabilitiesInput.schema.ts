/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartUpdateWithoutApplicabilitiesInputObjectSchema } from './PartUpdateWithoutApplicabilitiesInput.schema';
import { PartUncheckedUpdateWithoutApplicabilitiesInputObjectSchema } from './PartUncheckedUpdateWithoutApplicabilitiesInput.schema';
import { PartCreateWithoutApplicabilitiesInputObjectSchema } from './PartCreateWithoutApplicabilitiesInput.schema';
import { PartUncheckedCreateWithoutApplicabilitiesInputObjectSchema } from './PartUncheckedCreateWithoutApplicabilitiesInput.schema';
import { PartWhereInputObjectSchema } from './PartWhereInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartUpsertWithoutApplicabilitiesInput>;
export const PartUpsertWithoutApplicabilitiesInputObjectSchema: SchemaType = z.object({
    update: z.union([z.lazy(() => PartUpdateWithoutApplicabilitiesInputObjectSchema), z.lazy(() => PartUncheckedUpdateWithoutApplicabilitiesInputObjectSchema)]), create: z.union([z.lazy(() => PartCreateWithoutApplicabilitiesInputObjectSchema), z.lazy(() => PartUncheckedCreateWithoutApplicabilitiesInputObjectSchema)]), where: z.lazy(() => PartWhereInputObjectSchema).optional().optional()
}).strict() as SchemaType;
