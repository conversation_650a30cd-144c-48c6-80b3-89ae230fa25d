/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartUpdateWithoutAttributesInputObjectSchema } from './PartUpdateWithoutAttributesInput.schema';
import { PartUncheckedUpdateWithoutAttributesInputObjectSchema } from './PartUncheckedUpdateWithoutAttributesInput.schema';
import { PartCreateWithoutAttributesInputObjectSchema } from './PartCreateWithoutAttributesInput.schema';
import { PartUncheckedCreateWithoutAttributesInputObjectSchema } from './PartUncheckedCreateWithoutAttributesInput.schema';
import { PartWhereInputObjectSchema } from './PartWhereInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartUpsertWithoutAttributesInput>;
export const PartUpsertWithoutAttributesInputObjectSchema: SchemaType = z.object({
    update: z.union([z.lazy(() => PartUpdateWithoutAttributesInputObjectSchema), z.lazy(() => PartUncheckedUpdateWithoutAttributesInputObjectSchema)]), create: z.union([z.lazy(() => PartCreateWithoutAttributesInputObjectSchema), z.lazy(() => PartUncheckedCreateWithoutAttributesInputObjectSchema)]), where: z.lazy(() => PartWhereInputObjectSchema).optional().optional()
}).strict() as SchemaType;
