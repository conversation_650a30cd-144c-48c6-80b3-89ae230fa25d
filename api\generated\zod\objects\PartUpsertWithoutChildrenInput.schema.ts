/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartUpdateWithoutChildrenInputObjectSchema } from './PartUpdateWithoutChildrenInput.schema';
import { PartUncheckedUpdateWithoutChildrenInputObjectSchema } from './PartUncheckedUpdateWithoutChildrenInput.schema';
import { PartCreateWithoutChildrenInputObjectSchema } from './PartCreateWithoutChildrenInput.schema';
import { PartUncheckedCreateWithoutChildrenInputObjectSchema } from './PartUncheckedCreateWithoutChildrenInput.schema';
import { PartWhereInputObjectSchema } from './PartWhereInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartUpsertWithoutChildrenInput>;
export const PartUpsertWithoutChildrenInputObjectSchema: SchemaType = z.object({
    update: z.union([z.lazy(() => PartUpdateWithoutChildrenInputObjectSchema), z.lazy(() => PartUncheckedUpdateWithoutChildrenInputObjectSchema)]), create: z.union([z.lazy(() => PartCreateWithoutChildrenInputObjectSchema), z.lazy(() => PartUncheckedCreateWithoutChildrenInputObjectSchema)]), where: z.lazy(() => PartWhereInputObjectSchema).optional().optional()
}).strict() as SchemaType;
