/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartUpdateWithoutEquipmentApplicabilitiesInputObjectSchema } from './PartUpdateWithoutEquipmentApplicabilitiesInput.schema';
import { PartUncheckedUpdateWithoutEquipmentApplicabilitiesInputObjectSchema } from './PartUncheckedUpdateWithoutEquipmentApplicabilitiesInput.schema';
import { PartCreateWithoutEquipmentApplicabilitiesInputObjectSchema } from './PartCreateWithoutEquipmentApplicabilitiesInput.schema';
import { PartUncheckedCreateWithoutEquipmentApplicabilitiesInputObjectSchema } from './PartUncheckedCreateWithoutEquipmentApplicabilitiesInput.schema';
import { PartWhereInputObjectSchema } from './PartWhereInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartUpsertWithoutEquipmentApplicabilitiesInput>;
export const PartUpsertWithoutEquipmentApplicabilitiesInputObjectSchema: SchemaType = z.object({
    update: z.union([z.lazy(() => PartUpdateWithoutEquipmentApplicabilitiesInputObjectSchema), z.lazy(() => PartUncheckedUpdateWithoutEquipmentApplicabilitiesInputObjectSchema)]), create: z.union([z.lazy(() => PartCreateWithoutEquipmentApplicabilitiesInputObjectSchema), z.lazy(() => PartUncheckedCreateWithoutEquipmentApplicabilitiesInputObjectSchema)]), where: z.lazy(() => PartWhereInputObjectSchema).optional().optional()
}).strict() as SchemaType;
