/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartUpdateWithoutMatchingProposalsInputObjectSchema } from './PartUpdateWithoutMatchingProposalsInput.schema';
import { PartUncheckedUpdateWithoutMatchingProposalsInputObjectSchema } from './PartUncheckedUpdateWithoutMatchingProposalsInput.schema';
import { PartCreateWithoutMatchingProposalsInputObjectSchema } from './PartCreateWithoutMatchingProposalsInput.schema';
import { PartUncheckedCreateWithoutMatchingProposalsInputObjectSchema } from './PartUncheckedCreateWithoutMatchingProposalsInput.schema';
import { PartWhereInputObjectSchema } from './PartWhereInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartUpsertWithoutMatchingProposalsInput>;
export const PartUpsertWithoutMatchingProposalsInputObjectSchema: SchemaType = z.object({
    update: z.union([z.lazy(() => PartUpdateWithoutMatchingProposalsInputObjectSchema), z.lazy(() => PartUncheckedUpdateWithoutMatchingProposalsInputObjectSchema)]), create: z.union([z.lazy(() => PartCreateWithoutMatchingProposalsInputObjectSchema), z.lazy(() => PartUncheckedCreateWithoutMatchingProposalsInputObjectSchema)]), where: z.lazy(() => PartWhereInputObjectSchema).optional().optional()
}).strict() as SchemaType;
