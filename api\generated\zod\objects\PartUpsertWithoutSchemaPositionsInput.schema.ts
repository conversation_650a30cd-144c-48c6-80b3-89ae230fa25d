/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartUpdateWithoutSchemaPositionsInputObjectSchema } from './PartUpdateWithoutSchemaPositionsInput.schema';
import { PartUncheckedUpdateWithoutSchemaPositionsInputObjectSchema } from './PartUncheckedUpdateWithoutSchemaPositionsInput.schema';
import { PartCreateWithoutSchemaPositionsInputObjectSchema } from './PartCreateWithoutSchemaPositionsInput.schema';
import { PartUncheckedCreateWithoutSchemaPositionsInputObjectSchema } from './PartUncheckedCreateWithoutSchemaPositionsInput.schema';
import { PartWhereInputObjectSchema } from './PartWhereInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.PartUpsertWithoutSchemaPositionsInput>;
export const PartUpsertWithoutSchemaPositionsInputObjectSchema: SchemaType = z.object({
    update: z.union([z.lazy(() => PartUpdateWithoutSchemaPositionsInputObjectSchema), z.lazy(() => PartUncheckedUpdateWithoutSchemaPositionsInputObjectSchema)]), create: z.union([z.lazy(() => PartCreateWithoutSchemaPositionsInputObjectSchema), z.lazy(() => PartUncheckedCreateWithoutSchemaPositionsInputObjectSchema)]), where: z.lazy(() => PartWhereInputObjectSchema).optional().optional()
}).strict() as SchemaType;
