/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.SchemaAnnotationAvgAggregateInputType>;
export const SchemaAnnotationAvgAggregateInputObjectSchema: SchemaType = z.object({
    x: z.literal(true).optional().optional(), y: z.literal(true).optional().optional(), width: z.literal(true).optional().optional(), height: z.literal(true).optional().optional(), fontSize: z.literal(true).optional().optional(), strokeWidth: z.literal(true).optional().optional(), opacity: z.literal(true).optional().optional(), sortOrder: z.literal(true).optional().optional()
}).strict() as SchemaType;
