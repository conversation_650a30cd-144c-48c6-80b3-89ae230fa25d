/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { SchemaAnnotationCreateManySchemaInputObjectSchema } from './SchemaAnnotationCreateManySchemaInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.SchemaAnnotationCreateManySchemaInputEnvelope>;
export const SchemaAnnotationCreateManySchemaInputEnvelopeObjectSchema: SchemaType = z.object({
    data: z.union([z.lazy(() => SchemaAnnotationCreateManySchemaInputObjectSchema),
    z.lazy(() => SchemaAnnotationCreateManySchemaInputObjectSchema).array()]), skipDuplicates: z.boolean().optional().optional()
}).strict() as SchemaType;
