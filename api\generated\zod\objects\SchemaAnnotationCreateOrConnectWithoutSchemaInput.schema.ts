/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { SchemaAnnotationWhereUniqueInputObjectSchema } from './SchemaAnnotationWhereUniqueInput.schema';
import { SchemaAnnotationCreateWithoutSchemaInputObjectSchema } from './SchemaAnnotationCreateWithoutSchemaInput.schema';
import { SchemaAnnotationUncheckedCreateWithoutSchemaInputObjectSchema } from './SchemaAnnotationUncheckedCreateWithoutSchemaInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.SchemaAnnotationCreateOrConnectWithoutSchemaInput>;
export const SchemaAnnotationCreateOrConnectWithoutSchemaInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => SchemaAnnotationWhereUniqueInputObjectSchema), create: z.union([z.lazy(() => SchemaAnnotationCreateWithoutSchemaInputObjectSchema), z.lazy(() => SchemaAnnotationUncheckedCreateWithoutSchemaInputObjectSchema)])
}).strict() as SchemaType;
