/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.SchemaAnnotationCreateWithoutSchemaInput>;
export const SchemaAnnotationCreateWithoutSchemaInputObjectSchema: SchemaType = z.object({
    id: z.string().optional().optional(), x: z.number(), y: z.number(), width: z.union([z.number(),
    z.null()]).optional().nullable(), height: z.union([z.number(),
    z.null()]).optional().nullable(), text: z.string(), annotationType: z.string().optional().optional(), color: z.union([z.string(),
    z.null()]).optional().nullable(), fontSize: z.union([z.number(),
    z.null()]).optional().nullable(), strokeWidth: z.union([z.number(),
    z.null()]).optional().nullable(), opacity: z.union([z.number(),
    z.null()]).optional().nullable(), isVisible: z.boolean().optional().optional(), sortOrder: z.number().optional().optional(), createdAt: z.union([z.date().optional(), z.string().datetime().optional()]).optional(), updatedAt: z.union([z.date().optional(), z.string().datetime().optional()]).optional()
}).strict() as SchemaType;
