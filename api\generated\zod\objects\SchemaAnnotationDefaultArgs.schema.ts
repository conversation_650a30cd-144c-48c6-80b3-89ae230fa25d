/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { SchemaAnnotationSelectObjectSchema } from './SchemaAnnotationSelect.schema';
import { SchemaAnnotationIncludeObjectSchema } from './SchemaAnnotationInclude.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.SchemaAnnotationDefaultArgs>;
export const SchemaAnnotationDefaultArgsObjectSchema: SchemaType = z.object({
    select: z.lazy(() => SchemaAnnotationSelectObjectSchema).optional().optional(), include: z.lazy(() => SchemaAnnotationIncludeObjectSchema).optional().optional()
}).strict() as SchemaType;
