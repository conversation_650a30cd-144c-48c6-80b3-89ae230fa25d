/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { AggregateSchemaDefaultArgsObjectSchema } from './AggregateSchemaDefaultArgs.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.SchemaAnnotationInclude>;
export const SchemaAnnotationIncludeObjectSchema: SchemaType = z.object({
    schema: z.union([z.boolean(),
    z.lazy(() => AggregateSchemaDefaultArgsObjectSchema)]).optional()
}).strict() as SchemaType;
