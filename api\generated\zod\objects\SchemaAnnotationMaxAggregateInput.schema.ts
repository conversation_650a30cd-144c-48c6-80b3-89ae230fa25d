/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.SchemaAnnotationMaxAggregateInputType>;
export const SchemaAnnotationMaxAggregateInputObjectSchema: SchemaType = z.object({
    id: z.literal(true).optional().optional(), schemaId: z.literal(true).optional().optional(), x: z.literal(true).optional().optional(), y: z.literal(true).optional().optional(), width: z.literal(true).optional().optional(), height: z.literal(true).optional().optional(), text: z.literal(true).optional().optional(), annotationType: z.literal(true).optional().optional(), color: z.literal(true).optional().optional(), fontSize: z.literal(true).optional().optional(), strokeWidth: z.literal(true).optional().optional(), opacity: z.literal(true).optional().optional(), isVisible: z.literal(true).optional().optional(), sortOrder: z.literal(true).optional().optional(), createdAt: z.literal(true).optional().optional(), updatedAt: z.literal(true).optional().optional()
}).strict() as SchemaType;
