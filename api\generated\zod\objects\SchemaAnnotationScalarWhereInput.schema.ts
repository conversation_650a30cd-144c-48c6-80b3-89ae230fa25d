/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { StringFilterObjectSchema } from './StringFilter.schema';
import { FloatFilterObjectSchema } from './FloatFilter.schema';
import { FloatNullableFilterObjectSchema } from './FloatNullableFilter.schema';
import { StringNullableFilterObjectSchema } from './StringNullableFilter.schema';
import { IntNullableFilterObjectSchema } from './IntNullableFilter.schema';
import { BoolFilterObjectSchema } from './BoolFilter.schema';
import { IntFilterObjectSchema } from './IntFilter.schema';
import { DateTimeFilterObjectSchema } from './DateTimeFilter.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.SchemaAnnotationScalarWhereInput>;
export const SchemaAnnotationScalarWhereInputObjectSchema: SchemaType = z.object({
    AND: z.union([z.lazy(() => SchemaAnnotationScalarWhereInputObjectSchema),
    z.lazy(() => SchemaAnnotationScalarWhereInputObjectSchema).array()]).optional(), OR: z.lazy(() => SchemaAnnotationScalarWhereInputObjectSchema).array().optional().optional(), NOT: z.union([z.lazy(() => SchemaAnnotationScalarWhereInputObjectSchema),
    z.lazy(() => SchemaAnnotationScalarWhereInputObjectSchema).array()]).optional(), id: z.union([z.lazy(() => StringFilterObjectSchema),
    z.string()]).optional(), schemaId: z.union([z.lazy(() => StringFilterObjectSchema),
    z.string()]).optional(), x: z.union([z.lazy(() => FloatFilterObjectSchema),
    z.number()]).optional(), y: z.union([z.lazy(() => FloatFilterObjectSchema),
    z.number()]).optional(), width: z.union([z.lazy(() => FloatNullableFilterObjectSchema),
    z.number(),
    z.null()]).optional().nullable(), height: z.union([z.lazy(() => FloatNullableFilterObjectSchema),
    z.number(),
    z.null()]).optional().nullable(), text: z.union([z.lazy(() => StringFilterObjectSchema),
    z.string()]).optional(), annotationType: z.union([z.lazy(() => StringFilterObjectSchema),
    z.string()]).optional(), color: z.union([z.lazy(() => StringNullableFilterObjectSchema),
    z.string(),
    z.null()]).optional().nullable(), fontSize: z.union([z.lazy(() => IntNullableFilterObjectSchema),
    z.number(),
    z.null()]).optional().nullable(), strokeWidth: z.union([z.lazy(() => IntNullableFilterObjectSchema),
    z.number(),
    z.null()]).optional().nullable(), opacity: z.union([z.lazy(() => FloatNullableFilterObjectSchema),
    z.number(),
    z.null()]).optional().nullable(), isVisible: z.union([z.lazy(() => BoolFilterObjectSchema),
    z.boolean()]).optional(), sortOrder: z.union([z.lazy(() => IntFilterObjectSchema),
    z.number()]).optional(), createdAt: z.union([z.lazy(() => DateTimeFilterObjectSchema),
    z.union([z.date(), z.string().datetime().optional()])]).optional(), updatedAt: z.union([z.lazy(() => DateTimeFilterObjectSchema),
    z.union([z.date(), z.string().datetime().optional()])]).optional()
}).strict() as SchemaType;
