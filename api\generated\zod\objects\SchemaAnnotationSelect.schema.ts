/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { AggregateSchemaDefaultArgsObjectSchema } from './AggregateSchemaDefaultArgs.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.SchemaAnnotationSelect>;
export const SchemaAnnotationSelectObjectSchema: SchemaType = z.object({
    id: z.boolean().optional().optional(), schema: z.union([z.boolean(),
    z.lazy(() => AggregateSchemaDefaultArgsObjectSchema)]).optional(), schemaId: z.boolean().optional().optional(), x: z.boolean().optional().optional(), y: z.boolean().optional().optional(), width: z.boolean().optional().optional(), height: z.boolean().optional().optional(), text: z.boolean().optional().optional(), annotationType: z.boolean().optional().optional(), color: z.boolean().optional().optional(), fontSize: z.boolean().optional().optional(), strokeWidth: z.boolean().optional().optional(), opacity: z.boolean().optional().optional(), isVisible: z.boolean().optional().optional(), sortOrder: z.boolean().optional().optional(), createdAt: z.boolean().optional().optional(), updatedAt: z.boolean().optional().optional()
}).strict() as SchemaType;
