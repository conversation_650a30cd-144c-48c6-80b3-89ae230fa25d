/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { SchemaAnnotationScalarWhereInputObjectSchema } from './SchemaAnnotationScalarWhereInput.schema';
import { SchemaAnnotationUpdateManyMutationInputObjectSchema } from './SchemaAnnotationUpdateManyMutationInput.schema';
import { SchemaAnnotationUncheckedUpdateManyWithoutSchemaInputObjectSchema } from './SchemaAnnotationUncheckedUpdateManyWithoutSchemaInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.SchemaAnnotationUpdateManyWithWhereWithoutSchemaInput>;
export const SchemaAnnotationUpdateManyWithWhereWithoutSchemaInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => SchemaAnnotationScalarWhereInputObjectSchema), data: z.union([z.lazy(() => SchemaAnnotationUpdateManyMutationInputObjectSchema), z.lazy(() => SchemaAnnotationUncheckedUpdateManyWithoutSchemaInputObjectSchema)])
}).strict() as SchemaType;
