/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { SchemaAnnotationWhereUniqueInputObjectSchema } from './SchemaAnnotationWhereUniqueInput.schema';
import { SchemaAnnotationUpdateWithoutSchemaInputObjectSchema } from './SchemaAnnotationUpdateWithoutSchemaInput.schema';
import { SchemaAnnotationUncheckedUpdateWithoutSchemaInputObjectSchema } from './SchemaAnnotationUncheckedUpdateWithoutSchemaInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.SchemaAnnotationUpdateWithWhereUniqueWithoutSchemaInput>;
export const SchemaAnnotationUpdateWithWhereUniqueWithoutSchemaInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => SchemaAnnotationWhereUniqueInputObjectSchema), data: z.union([z.lazy(() => SchemaAnnotationUpdateWithoutSchemaInputObjectSchema), z.lazy(() => SchemaAnnotationUncheckedUpdateWithoutSchemaInputObjectSchema)])
}).strict() as SchemaType;
