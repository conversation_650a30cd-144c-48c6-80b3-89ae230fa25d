/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { SchemaAnnotationWhereUniqueInputObjectSchema } from './SchemaAnnotationWhereUniqueInput.schema';
import { SchemaAnnotationUpdateWithoutSchemaInputObjectSchema } from './SchemaAnnotationUpdateWithoutSchemaInput.schema';
import { SchemaAnnotationUncheckedUpdateWithoutSchemaInputObjectSchema } from './SchemaAnnotationUncheckedUpdateWithoutSchemaInput.schema';
import { SchemaAnnotationCreateWithoutSchemaInputObjectSchema } from './SchemaAnnotationCreateWithoutSchemaInput.schema';
import { SchemaAnnotationUncheckedCreateWithoutSchemaInputObjectSchema } from './SchemaAnnotationUncheckedCreateWithoutSchemaInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.SchemaAnnotationUpsertWithWhereUniqueWithoutSchemaInput>;
export const SchemaAnnotationUpsertWithWhereUniqueWithoutSchemaInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => SchemaAnnotationWhereUniqueInputObjectSchema), update: z.union([z.lazy(() => SchemaAnnotationUpdateWithoutSchemaInputObjectSchema), z.lazy(() => SchemaAnnotationUncheckedUpdateWithoutSchemaInputObjectSchema)]), create: z.union([z.lazy(() => SchemaAnnotationCreateWithoutSchemaInputObjectSchema), z.lazy(() => SchemaAnnotationUncheckedCreateWithoutSchemaInputObjectSchema)])
}).strict() as SchemaType;
