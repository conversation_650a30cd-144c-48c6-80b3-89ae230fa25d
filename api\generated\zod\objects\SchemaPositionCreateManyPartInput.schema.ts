/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.SchemaPositionCreateManyPartInput>;
export const SchemaPositionCreateManyPartInputObjectSchema: SchemaType = z.object({
    id: z.string().optional().optional(), schemaId: z.string(), positionNumber: z.string(), x: z.number(), y: z.number(), width: z.union([z.number(),
    z.null()]).optional().nullable(), height: z.union([z.number(),
    z.null()]).optional().nullable(), shape: z.string().optional().optional(), color: z.union([z.string(),
    z.null()]).optional().nullable(), label: z.union([z.string(),
    z.null()]).optional().nullable(), quantity: z.number().optional().optional(), isRequired: z.boolean().optional().optional(), isHighlighted: z.boolean().optional().optional(), installationOrder: z.union([z.number(),
    z.null()]).optional().nullable(), notes: z.union([z.string(),
    z.null()]).optional().nullable(), isVisible: z.boolean().optional().optional(), sortOrder: z.number().optional().optional(), createdAt: z.union([z.date().optional(), z.string().datetime().optional()]).optional(), updatedAt: z.union([z.date().optional(), z.string().datetime().optional()]).optional()
}).strict() as SchemaType;
