/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { SchemaPositionCreateManyPartInputObjectSchema } from './SchemaPositionCreateManyPartInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.SchemaPositionCreateManyPartInputEnvelope>;
export const SchemaPositionCreateManyPartInputEnvelopeObjectSchema: SchemaType = z.object({
    data: z.union([z.lazy(() => SchemaPositionCreateManyPartInputObjectSchema),
    z.lazy(() => SchemaPositionCreateManyPartInputObjectSchema).array()]), skipDuplicates: z.boolean().optional().optional()
}).strict() as SchemaType;
