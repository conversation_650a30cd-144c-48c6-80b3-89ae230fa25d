/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { SchemaPositionCreateManySchemaInputObjectSchema } from './SchemaPositionCreateManySchemaInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.SchemaPositionCreateManySchemaInputEnvelope>;
export const SchemaPositionCreateManySchemaInputEnvelopeObjectSchema: SchemaType = z.object({
    data: z.union([z.lazy(() => SchemaPositionCreateManySchemaInputObjectSchema),
    z.lazy(() => SchemaPositionCreateManySchemaInputObjectSchema).array()]), skipDuplicates: z.boolean().optional().optional()
}).strict() as SchemaType;
