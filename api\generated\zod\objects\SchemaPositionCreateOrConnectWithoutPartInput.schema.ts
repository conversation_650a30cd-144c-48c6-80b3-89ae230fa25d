/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { SchemaPositionWhereUniqueInputObjectSchema } from './SchemaPositionWhereUniqueInput.schema';
import { SchemaPositionCreateWithoutPartInputObjectSchema } from './SchemaPositionCreateWithoutPartInput.schema';
import { SchemaPositionUncheckedCreateWithoutPartInputObjectSchema } from './SchemaPositionUncheckedCreateWithoutPartInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.SchemaPositionCreateOrConnectWithoutPartInput>;
export const SchemaPositionCreateOrConnectWithoutPartInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => SchemaPositionWhereUniqueInputObjectSchema), create: z.union([z.lazy(() => SchemaPositionCreateWithoutPartInputObjectSchema), z.lazy(() => SchemaPositionUncheckedCreateWithoutPartInputObjectSchema)])
}).strict() as SchemaType;
