/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { SchemaPositionWhereUniqueInputObjectSchema } from './SchemaPositionWhereUniqueInput.schema';
import { SchemaPositionCreateWithoutSchemaInputObjectSchema } from './SchemaPositionCreateWithoutSchemaInput.schema';
import { SchemaPositionUncheckedCreateWithoutSchemaInputObjectSchema } from './SchemaPositionUncheckedCreateWithoutSchemaInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.SchemaPositionCreateOrConnectWithoutSchemaInput>;
export const SchemaPositionCreateOrConnectWithoutSchemaInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => SchemaPositionWhereUniqueInputObjectSchema), create: z.union([z.lazy(() => SchemaPositionCreateWithoutSchemaInputObjectSchema), z.lazy(() => SchemaPositionUncheckedCreateWithoutSchemaInputObjectSchema)])
}).strict() as SchemaType;
