/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { PartCreateNestedOneWithoutSchemaPositionsInputObjectSchema } from './PartCreateNestedOneWithoutSchemaPositionsInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.SchemaPositionCreateWithoutSchemaInput>;
export const SchemaPositionCreateWithoutSchemaInputObjectSchema: SchemaType = z.object({
    id: z.string().optional().optional(), positionNumber: z.string(), x: z.number(), y: z.number(), width: z.union([z.number(),
    z.null()]).optional().nullable(), height: z.union([z.number(),
    z.null()]).optional().nullable(), shape: z.string().optional().optional(), color: z.union([z.string(),
    z.null()]).optional().nullable(), label: z.union([z.string(),
    z.null()]).optional().nullable(), quantity: z.number().optional().optional(), isRequired: z.boolean().optional().optional(), isHighlighted: z.boolean().optional().optional(), installationOrder: z.union([z.number(),
    z.null()]).optional().nullable(), notes: z.union([z.string(),
    z.null()]).optional().nullable(), isVisible: z.boolean().optional().optional(), sortOrder: z.number().optional().optional(), createdAt: z.union([z.date().optional(), z.string().datetime().optional()]).optional(), updatedAt: z.union([z.date().optional(), z.string().datetime().optional()]).optional(), part: z.lazy(() => PartCreateNestedOneWithoutSchemaPositionsInputObjectSchema)
}).strict() as SchemaType;
