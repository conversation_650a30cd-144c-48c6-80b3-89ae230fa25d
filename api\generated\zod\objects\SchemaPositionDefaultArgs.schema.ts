/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { SchemaPositionSelectObjectSchema } from './SchemaPositionSelect.schema';
import { SchemaPositionIncludeObjectSchema } from './SchemaPositionInclude.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.SchemaPositionDefaultArgs>;
export const SchemaPositionDefaultArgsObjectSchema: SchemaType = z.object({
    select: z.lazy(() => SchemaPositionSelectObjectSchema).optional().optional(), include: z.lazy(() => SchemaPositionIncludeObjectSchema).optional().optional()
}).strict() as SchemaType;
