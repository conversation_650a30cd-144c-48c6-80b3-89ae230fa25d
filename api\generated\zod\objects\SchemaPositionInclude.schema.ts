/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { AggregateSchemaDefaultArgsObjectSchema } from './AggregateSchemaDefaultArgs.schema';
import { PartDefaultArgsObjectSchema } from './PartDefaultArgs.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.SchemaPositionInclude>;
export const SchemaPositionIncludeObjectSchema: SchemaType = z.object({
    schema: z.union([z.boolean(),
    z.lazy(() => AggregateSchemaDefaultArgsObjectSchema)]).optional(), part: z.union([z.boolean(),
    z.lazy(() => PartDefaultArgsObjectSchema)]).optional()
}).strict() as SchemaType;
