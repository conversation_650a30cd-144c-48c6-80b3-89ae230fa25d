/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.SchemaPositionMinAggregateInputType>;
export const SchemaPositionMinAggregateInputObjectSchema: SchemaType = z.object({
    id: z.literal(true).optional().optional(), schemaId: z.literal(true).optional().optional(), partId: z.literal(true).optional().optional(), positionNumber: z.literal(true).optional().optional(), x: z.literal(true).optional().optional(), y: z.literal(true).optional().optional(), width: z.literal(true).optional().optional(), height: z.literal(true).optional().optional(), shape: z.literal(true).optional().optional(), color: z.literal(true).optional().optional(), label: z.literal(true).optional().optional(), quantity: z.literal(true).optional().optional(), isRequired: z.literal(true).optional().optional(), isHighlighted: z.literal(true).optional().optional(), installationOrder: z.literal(true).optional().optional(), notes: z.literal(true).optional().optional(), isVisible: z.literal(true).optional().optional(), sortOrder: z.literal(true).optional().optional(), createdAt: z.literal(true).optional().optional(), updatedAt: z.literal(true).optional().optional()
}).strict() as SchemaType;
