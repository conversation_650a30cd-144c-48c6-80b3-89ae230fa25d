/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { StringFilterObjectSchema } from './StringFilter.schema';
import { IntFilterObjectSchema } from './IntFilter.schema';
import { FloatFilterObjectSchema } from './FloatFilter.schema';
import { FloatNullableFilterObjectSchema } from './FloatNullableFilter.schema';
import { StringNullableFilterObjectSchema } from './StringNullableFilter.schema';
import { BoolFilterObjectSchema } from './BoolFilter.schema';
import { IntNullableFilterObjectSchema } from './IntNullableFilter.schema';
import { DateTimeFilterObjectSchema } from './DateTimeFilter.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.SchemaPositionScalarWhereInput>;
export const SchemaPositionScalarWhereInputObjectSchema: SchemaType = z.object({
    AND: z.union([z.lazy(() => SchemaPositionScalarWhereInputObjectSchema),
    z.lazy(() => SchemaPositionScalarWhereInputObjectSchema).array()]).optional(), OR: z.lazy(() => SchemaPositionScalarWhereInputObjectSchema).array().optional().optional(), NOT: z.union([z.lazy(() => SchemaPositionScalarWhereInputObjectSchema),
    z.lazy(() => SchemaPositionScalarWhereInputObjectSchema).array()]).optional(), id: z.union([z.lazy(() => StringFilterObjectSchema),
    z.string()]).optional(), schemaId: z.union([z.lazy(() => StringFilterObjectSchema),
    z.string()]).optional(), partId: z.union([z.lazy(() => IntFilterObjectSchema),
    z.number()]).optional(), positionNumber: z.union([z.lazy(() => StringFilterObjectSchema),
    z.string()]).optional(), x: z.union([z.lazy(() => FloatFilterObjectSchema),
    z.number()]).optional(), y: z.union([z.lazy(() => FloatFilterObjectSchema),
    z.number()]).optional(), width: z.union([z.lazy(() => FloatNullableFilterObjectSchema),
    z.number(),
    z.null()]).optional().nullable(), height: z.union([z.lazy(() => FloatNullableFilterObjectSchema),
    z.number(),
    z.null()]).optional().nullable(), shape: z.union([z.lazy(() => StringFilterObjectSchema),
    z.string()]).optional(), color: z.union([z.lazy(() => StringNullableFilterObjectSchema),
    z.string(),
    z.null()]).optional().nullable(), label: z.union([z.lazy(() => StringNullableFilterObjectSchema),
    z.string(),
    z.null()]).optional().nullable(), quantity: z.union([z.lazy(() => IntFilterObjectSchema),
    z.number()]).optional(), isRequired: z.union([z.lazy(() => BoolFilterObjectSchema),
    z.boolean()]).optional(), isHighlighted: z.union([z.lazy(() => BoolFilterObjectSchema),
    z.boolean()]).optional(), installationOrder: z.union([z.lazy(() => IntNullableFilterObjectSchema),
    z.number(),
    z.null()]).optional().nullable(), notes: z.union([z.lazy(() => StringNullableFilterObjectSchema),
    z.string(),
    z.null()]).optional().nullable(), isVisible: z.union([z.lazy(() => BoolFilterObjectSchema),
    z.boolean()]).optional(), sortOrder: z.union([z.lazy(() => IntFilterObjectSchema),
    z.number()]).optional(), createdAt: z.union([z.lazy(() => DateTimeFilterObjectSchema),
    z.union([z.date(), z.string().datetime().optional()])]).optional(), updatedAt: z.union([z.lazy(() => DateTimeFilterObjectSchema),
    z.union([z.date(), z.string().datetime().optional()])]).optional()
}).strict() as SchemaType;
