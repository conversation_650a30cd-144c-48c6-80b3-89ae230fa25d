/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { AggregateSchemaDefaultArgsObjectSchema } from './AggregateSchemaDefaultArgs.schema';
import { PartDefaultArgsObjectSchema } from './PartDefaultArgs.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.SchemaPositionSelect>;
export const SchemaPositionSelectObjectSchema: SchemaType = z.object({
    id: z.boolean().optional().optional(), schema: z.union([z.boolean(),
    z.lazy(() => AggregateSchemaDefaultArgsObjectSchema)]).optional(), schemaId: z.boolean().optional().optional(), part: z.union([z.boolean(),
    z.lazy(() => PartDefaultArgsObjectSchema)]).optional(), partId: z.boolean().optional().optional(), positionNumber: z.boolean().optional().optional(), x: z.boolean().optional().optional(), y: z.boolean().optional().optional(), width: z.boolean().optional().optional(), height: z.boolean().optional().optional(), shape: z.boolean().optional().optional(), color: z.boolean().optional().optional(), label: z.boolean().optional().optional(), quantity: z.boolean().optional().optional(), isRequired: z.boolean().optional().optional(), isHighlighted: z.boolean().optional().optional(), installationOrder: z.boolean().optional().optional(), notes: z.boolean().optional().optional(), isVisible: z.boolean().optional().optional(), sortOrder: z.boolean().optional().optional(), createdAt: z.boolean().optional().optional(), updatedAt: z.boolean().optional().optional()
}).strict() as SchemaType;
