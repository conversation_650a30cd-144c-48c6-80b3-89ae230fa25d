/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.SchemaPositionSumAggregateInputType>;
export const SchemaPositionSumAggregateInputObjectSchema: SchemaType = z.object({
    partId: z.literal(true).optional().optional(), x: z.literal(true).optional().optional(), y: z.literal(true).optional().optional(), width: z.literal(true).optional().optional(), height: z.literal(true).optional().optional(), quantity: z.literal(true).optional().optional(), installationOrder: z.literal(true).optional().optional(), sortOrder: z.literal(true).optional().optional()
}).strict() as SchemaType;
