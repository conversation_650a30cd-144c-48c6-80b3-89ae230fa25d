/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { SchemaPositionScalarWhereInputObjectSchema } from './SchemaPositionScalarWhereInput.schema';
import { SchemaPositionUpdateManyMutationInputObjectSchema } from './SchemaPositionUpdateManyMutationInput.schema';
import { SchemaPositionUncheckedUpdateManyWithoutPartInputObjectSchema } from './SchemaPositionUncheckedUpdateManyWithoutPartInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.SchemaPositionUpdateManyWithWhereWithoutPartInput>;
export const SchemaPositionUpdateManyWithWhereWithoutPartInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => SchemaPositionScalarWhereInputObjectSchema), data: z.union([z.lazy(() => SchemaPositionUpdateManyMutationInputObjectSchema), z.lazy(() => SchemaPositionUncheckedUpdateManyWithoutPartInputObjectSchema)])
}).strict() as SchemaType;
