/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { SchemaPositionScalarWhereInputObjectSchema } from './SchemaPositionScalarWhereInput.schema';
import { SchemaPositionUpdateManyMutationInputObjectSchema } from './SchemaPositionUpdateManyMutationInput.schema';
import { SchemaPositionUncheckedUpdateManyWithoutSchemaInputObjectSchema } from './SchemaPositionUncheckedUpdateManyWithoutSchemaInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.SchemaPositionUpdateManyWithWhereWithoutSchemaInput>;
export const SchemaPositionUpdateManyWithWhereWithoutSchemaInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => SchemaPositionScalarWhereInputObjectSchema), data: z.union([z.lazy(() => SchemaPositionUpdateManyMutationInputObjectSchema), z.lazy(() => SchemaPositionUncheckedUpdateManyWithoutSchemaInputObjectSchema)])
}).strict() as SchemaType;
