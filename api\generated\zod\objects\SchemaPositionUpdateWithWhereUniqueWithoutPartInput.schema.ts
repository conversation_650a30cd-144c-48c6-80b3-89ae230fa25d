/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { SchemaPositionWhereUniqueInputObjectSchema } from './SchemaPositionWhereUniqueInput.schema';
import { SchemaPositionUpdateWithoutPartInputObjectSchema } from './SchemaPositionUpdateWithoutPartInput.schema';
import { SchemaPositionUncheckedUpdateWithoutPartInputObjectSchema } from './SchemaPositionUncheckedUpdateWithoutPartInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.SchemaPositionUpdateWithWhereUniqueWithoutPartInput>;
export const SchemaPositionUpdateWithWhereUniqueWithoutPartInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => SchemaPositionWhereUniqueInputObjectSchema), data: z.union([z.lazy(() => SchemaPositionUpdateWithoutPartInputObjectSchema), z.lazy(() => SchemaPositionUncheckedUpdateWithoutPartInputObjectSchema)])
}).strict() as SchemaType;
