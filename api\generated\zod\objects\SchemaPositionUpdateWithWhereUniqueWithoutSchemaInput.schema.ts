/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { SchemaPositionWhereUniqueInputObjectSchema } from './SchemaPositionWhereUniqueInput.schema';
import { SchemaPositionUpdateWithoutSchemaInputObjectSchema } from './SchemaPositionUpdateWithoutSchemaInput.schema';
import { SchemaPositionUncheckedUpdateWithoutSchemaInputObjectSchema } from './SchemaPositionUncheckedUpdateWithoutSchemaInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.SchemaPositionUpdateWithWhereUniqueWithoutSchemaInput>;
export const SchemaPositionUpdateWithWhereUniqueWithoutSchemaInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => SchemaPositionWhereUniqueInputObjectSchema), data: z.union([z.lazy(() => SchemaPositionUpdateWithoutSchemaInputObjectSchema), z.lazy(() => SchemaPositionUncheckedUpdateWithoutSchemaInputObjectSchema)])
}).strict() as SchemaType;
