/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { SchemaPositionWhereUniqueInputObjectSchema } from './SchemaPositionWhereUniqueInput.schema';
import { SchemaPositionUpdateWithoutPartInputObjectSchema } from './SchemaPositionUpdateWithoutPartInput.schema';
import { SchemaPositionUncheckedUpdateWithoutPartInputObjectSchema } from './SchemaPositionUncheckedUpdateWithoutPartInput.schema';
import { SchemaPositionCreateWithoutPartInputObjectSchema } from './SchemaPositionCreateWithoutPartInput.schema';
import { SchemaPositionUncheckedCreateWithoutPartInputObjectSchema } from './SchemaPositionUncheckedCreateWithoutPartInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.SchemaPositionUpsertWithWhereUniqueWithoutPartInput>;
export const SchemaPositionUpsertWithWhereUniqueWithoutPartInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => SchemaPositionWhereUniqueInputObjectSchema), update: z.union([z.lazy(() => SchemaPositionUpdateWithoutPartInputObjectSchema), z.lazy(() => SchemaPositionUncheckedUpdateWithoutPartInputObjectSchema)]), create: z.union([z.lazy(() => SchemaPositionCreateWithoutPartInputObjectSchema), z.lazy(() => SchemaPositionUncheckedCreateWithoutPartInputObjectSchema)])
}).strict() as SchemaType;
