/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { SchemaPositionWhereUniqueInputObjectSchema } from './SchemaPositionWhereUniqueInput.schema';
import { SchemaPositionUpdateWithoutSchemaInputObjectSchema } from './SchemaPositionUpdateWithoutSchemaInput.schema';
import { SchemaPositionUncheckedUpdateWithoutSchemaInputObjectSchema } from './SchemaPositionUncheckedUpdateWithoutSchemaInput.schema';
import { SchemaPositionCreateWithoutSchemaInputObjectSchema } from './SchemaPositionCreateWithoutSchemaInput.schema';
import { SchemaPositionUncheckedCreateWithoutSchemaInputObjectSchema } from './SchemaPositionUncheckedCreateWithoutSchemaInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.SchemaPositionUpsertWithWhereUniqueWithoutSchemaInput>;
export const SchemaPositionUpsertWithWhereUniqueWithoutSchemaInputObjectSchema: SchemaType = z.object({
    where: z.lazy(() => SchemaPositionWhereUniqueInputObjectSchema), update: z.union([z.lazy(() => SchemaPositionUpdateWithoutSchemaInputObjectSchema), z.lazy(() => SchemaPositionUncheckedUpdateWithoutSchemaInputObjectSchema)]), create: z.union([z.lazy(() => SchemaPositionCreateWithoutSchemaInputObjectSchema), z.lazy(() => SchemaPositionUncheckedCreateWithoutSchemaInputObjectSchema)])
}).strict() as SchemaType;
