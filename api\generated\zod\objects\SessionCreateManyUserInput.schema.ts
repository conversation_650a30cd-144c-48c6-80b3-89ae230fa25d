/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.SessionCreateManyUserInput>;
export const SessionCreateManyUserInputObjectSchema: SchemaType = z.object({
    id: z.string(), expiresAt: z.union([z.date(), z.string().datetime()]), token: z.string(), createdAt: z.union([z.date(), z.string().datetime()]), updatedAt: z.union([z.date(), z.string().datetime()]), ipAddress: z.union([z.string(),
    z.null()]).optional().nullable(), userAgent: z.union([z.string(),
    z.null()]).optional().nullable(), impersonatedBy: z.union([z.string(),
    z.null()]).optional().nullable()
}).strict() as SchemaType;
