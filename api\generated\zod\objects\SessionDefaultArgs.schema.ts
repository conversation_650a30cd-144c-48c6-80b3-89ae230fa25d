/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { SessionSelectObjectSchema } from './SessionSelect.schema';
import { SessionIncludeObjectSchema } from './SessionInclude.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.SessionDefaultArgs>;
export const SessionDefaultArgsObjectSchema: SchemaType = z.object({
    select: z.lazy(() => SessionSelectObjectSchema).optional().optional(), include: z.lazy(() => SessionIncludeObjectSchema).optional().optional()
}).strict() as SchemaType;
