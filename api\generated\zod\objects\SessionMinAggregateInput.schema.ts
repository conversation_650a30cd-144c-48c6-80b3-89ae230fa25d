/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.SessionMinAggregateInputType>;
export const SessionMinAggregateInputObjectSchema: SchemaType = z.object({
    id: z.literal(true).optional().optional(), expiresAt: z.literal(true).optional().optional(), token: z.literal(true).optional().optional(), createdAt: z.literal(true).optional().optional(), updatedAt: z.literal(true).optional().optional(), ipAddress: z.literal(true).optional().optional(), userAgent: z.literal(true).optional().optional(), userId: z.literal(true).optional().optional(), impersonatedBy: z.literal(true).optional().optional()
}).strict() as SchemaType;
