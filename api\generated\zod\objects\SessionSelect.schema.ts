/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { UserDefaultArgsObjectSchema } from './UserDefaultArgs.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.SessionSelect>;
export const SessionSelectObjectSchema: SchemaType = z.object({
    id: z.boolean().optional().optional(), expiresAt: z.boolean().optional().optional(), token: z.boolean().optional().optional(), createdAt: z.boolean().optional().optional(), updatedAt: z.boolean().optional().optional(), ipAddress: z.boolean().optional().optional(), userAgent: z.boolean().optional().optional(), userId: z.boolean().optional().optional(), impersonatedBy: z.boolean().optional().optional(), user: z.union([z.boolean(),
    z.lazy(() => UserDefaultArgsObjectSchema)]).optional()
}).strict() as SchemaType;
