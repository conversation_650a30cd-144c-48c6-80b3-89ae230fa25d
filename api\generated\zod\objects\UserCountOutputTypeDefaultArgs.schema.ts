/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { UserCountOutputTypeSelectObjectSchema } from './UserCountOutputTypeSelect.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.UserCountOutputTypeDefaultArgs>;
export const UserCountOutputTypeDefaultArgsObjectSchema: SchemaType = z.object({
    select: z.lazy(() => UserCountOutputTypeSelectObjectSchema).optional().optional()
}).strict() as SchemaType;
