/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { RoleSchema } from '../enums/Role.schema';
import { AccountCreateNestedManyWithoutUserInputObjectSchema } from './AccountCreateNestedManyWithoutUserInput.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.UserCreateWithoutSessionsInput>;
export const UserCreateWithoutSessionsInputObjectSchema: SchemaType = z.object({
    id: z.string().optional().optional(), name: z.union([z.string(),
    z.null()]).optional().nullable(), email: z.string(), emailVerified: z.boolean().optional().optional(), image: z.union([z.string(),
    z.null()]).optional().nullable(), role: z.lazy(() => RoleSchema).optional().optional(), banned: z.boolean().optional().optional(), banReason: z.union([z.string(),
    z.null()]).optional().nullable(), banExpires: z.union([z.union([z.date(), z.string().datetime().optional()]),
    z.null()]).optional().nullable(), createdAt: z.union([z.date().optional(), z.string().datetime().optional()]).optional(), updatedAt: z.union([z.date().optional(), z.string().datetime().optional()]).optional(), accounts: z.lazy(() => AccountCreateNestedManyWithoutUserInputObjectSchema).optional().optional()
}).strict() as SchemaType;
