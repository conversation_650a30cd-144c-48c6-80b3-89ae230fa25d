/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { UserSelectObjectSchema } from './UserSelect.schema';
import { UserIncludeObjectSchema } from './UserInclude.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.UserDefaultArgs>;
export const UserDefaultArgsObjectSchema: SchemaType = z.object({
    select: z.lazy(() => UserSelectObjectSchema).optional().optional(), include: z.lazy(() => UserIncludeObjectSchema).optional().optional()
}).strict() as SchemaType;
