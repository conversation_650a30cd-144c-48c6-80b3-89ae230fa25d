/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { AccountInputSchema } from '../input/AccountInput.schema';
import { SessionInputSchema } from '../input/SessionInput.schema';
import { UserCountOutputTypeDefaultArgsObjectSchema } from './UserCountOutputTypeDefaultArgs.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.UserInclude>;
export const UserIncludeObjectSchema: SchemaType = z.object({
    accounts: z.union([z.boolean(),
    z.lazy(() => AccountInputSchema.findMany)]).optional(), sessions: z.union([z.boolean(),
    z.lazy(() => SessionInputSchema.findMany)]).optional(), _count: z.union([z.boolean(),
    z.lazy(() => UserCountOutputTypeDefaultArgsObjectSchema)]).optional()
}).strict() as SchemaType;
