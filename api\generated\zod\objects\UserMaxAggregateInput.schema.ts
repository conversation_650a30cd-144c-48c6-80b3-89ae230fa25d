/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';


import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.UserMaxAggregateInputType>;
export const UserMaxAggregateInputObjectSchema: SchemaType = z.object({
    id: z.literal(true).optional().optional(), name: z.literal(true).optional().optional(), email: z.literal(true).optional().optional(), emailVerified: z.literal(true).optional().optional(), image: z.literal(true).optional().optional(), role: z.literal(true).optional().optional(), banned: z.literal(true).optional().optional(), banReason: z.literal(true).optional().optional(), banExpires: z.literal(true).optional().optional(), createdAt: z.literal(true).optional().optional(), updatedAt: z.literal(true).optional().optional()
}).strict() as SchemaType;
