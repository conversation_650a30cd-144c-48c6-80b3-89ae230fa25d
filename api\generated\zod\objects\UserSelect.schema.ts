/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import { z } from 'zod';
import { AccountInputSchema } from '../input/AccountInput.schema';
import { SessionInputSchema } from '../input/SessionInput.schema';
import { UserCountOutputTypeDefaultArgsObjectSchema } from './UserCountOutputTypeDefaultArgs.schema';

import type { Prisma } from '@prisma/client';

type SchemaType = z.ZodType<Prisma.UserSelect>;
export const UserSelectObjectSchema: SchemaType = z.object({
    id: z.boolean().optional().optional(), name: z.boolean().optional().optional(), email: z.boolean().optional().optional(), emailVerified: z.boolean().optional().optional(), image: z.boolean().optional().optional(), role: z.boolean().optional().optional(), banned: z.boolean().optional().optional(), banReason: z.boolean().optional().optional(), banExpires: z.boolean().optional().optional(), createdAt: z.boolean().optional().optional(), updatedAt: z.boolean().optional().optional(), accounts: z.union([z.boolean(),
    z.lazy(() => AccountInputSchema.findMany)]).optional(), sessions: z.union([z.boolean(),
    z.lazy(() => SessionInputSchema.findMany)]).optional(), _count: z.union([z.boolean(),
    z.lazy(() => UserCountOutputTypeDefaultArgsObjectSchema)]).optional()
}).strict() as SchemaType;
