# Multi-stage build для cpanel (контекст — корень)
FROM node:20-alpine AS deps
WORKDIR /app

# Копируем package.json cpanel
COPY cpanel/package*.json ./

# Устанавливаем зависимости
RUN npm ci

# Build stage
FROM node:20-alpine AS build
WORKDIR /app

# Копируем зависимости
COPY --from=deps /app/node_modules ./node_modules

# Копируем исходники cpanel и api файлы для алиасов
COPY cpanel/ ./
COPY api/generated ./api/generated
COPY api/schemas ./api/schemas

# Собираем cpanel
RUN npm run build

# Production stage
FROM node:20-alpine AS production
WORKDIR /app

# Устанавливаем только prod-зависимости
COPY --from=build /app/package*.json ./
RUN npm ci --omit=dev && npm cache clean --force

# Копируем собранное приложение
COPY --from=build /app/dist ./dist

# Параметры запуска
ENV NODE_ENV=production
ENV HOST=0.0.0.0
ENV PORT=4322

EXPOSE 4322
CMD ["node", "./dist/server/entry.mjs"]
